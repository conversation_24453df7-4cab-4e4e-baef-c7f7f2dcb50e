import { Request, Response, NextFunction } from 'express'
import { ZodSchema, ZodError, z } from 'zod'
import { createValidationError } from './errorHandler'

export interface ValidationRequest extends Request {
  validatedBody?: any
  validatedQuery?: any
  validatedParams?: any
}

export const validateRequest = (schema: {
  body?: ZodSchema
  query?: ZodSchema
  params?: ZodSchema
}) => {
  return (req: ValidationRequest, _res: Response, next: NextFunction): void => {
    try {
      // Validate request body
      if (schema.body) {
        const result = schema.body.safeParse(req.body)
        if (!result.success) {
          const errors = formatZodErrors(result.error)
          throw createValidationError('Request body validation failed', errors)
        }
        req.validatedBody = result.data
      }

      // Validate query parameters
      if (schema.query) {
        const result = schema.query.safeParse(req.query)
        if (!result.success) {
          const errors = formatZodErrors(result.error)
          throw createValidationError('Query parameters validation failed', errors)
        }
        req.validatedQuery = result.data
      }

      // Validate route parameters
      if (schema.params) {
        const result = schema.params.safeParse(req.params)
        if (!result.success) {
          const errors = formatZodErrors(result.error)
          throw createValidationError('Route parameters validation failed', errors)
        }
        req.validatedParams = result.data
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

// Format Zod errors into a more readable format
const formatZodErrors = (error: ZodError) => {
  return error.errors.map(err => ({
    field: err.path.join('.'),
    message: err.message,
    value: err.code === 'invalid_type' ? err.received : undefined
  }))
}

// Common validation middleware
export const validatePagination = validateRequest({
  query: z.object({
    page: z.string().optional().transform((val: string | undefined) => val ? parseInt(val) : 1),
    limit: z.string().optional().transform((val: string | undefined) => val ? parseInt(val) : 10),
    sort: z.string().optional(),
    order: z.enum(['asc', 'desc']).optional().default('desc')
  })
})

export const validateAccountId = validateRequest({
  params: z.object({
    accountId: z.string().uuid('Invalid account ID format')
  })
})

export const validateTimeframe = validateRequest({
  query: z.object({
    timeframe: z.enum(['1D', '7D', '30D', '90D', 'ALL']).optional().default('30D'),
    startDate: z.string().datetime().optional(),
    endDate: z.string().datetime().optional()
  })
}) 
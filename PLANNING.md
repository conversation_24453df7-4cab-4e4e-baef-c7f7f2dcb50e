# Prop Firm Trading Platform - Planning Document

## Project Vision
A comprehensive proprietary trading firm platform that connects traders to MetaTrader 5 while providing a unique web-based trading interface. The platform will evaluate traders through challenge phases before providing access to live capital.

## Architecture Overview

### Core Components
- **Frontend**: React/Next.js web application with real-time trading interface
- **Backend**: Node.js/Express API server with WebSocket support
- **Database**: Supabase (PostgreSQL with real-time subscriptions)
- **Payments**: Stripe for challenge fees and profit sharing
- **Trading**: MetaTrader 5 integration via MT5 API/Expert Advisors
- **Authentication**: Supabase Auth with MFA support

### High-Level Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web Client    │────│   API Gateway    │────│   Trading Core  │
│  (React/Next)   │    │  (Node.js/WS)   │    │   (MT5 Bridge)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────│    Supabase     │──────────────┘
                        │   (Database)    │
                        └─────────────────┘
```

## Tech Stack

### Frontend
- **Framework**: Next.js 14+ with App Router
- **UI Library**: Tailwind CSS + Shadcn/ui components
- **Charts**: TradingView Charting Library or Lightweight Charts
- **State Management**: Zustand or React Query
- **WebSocket**: Socket.io-client for real-time data

### Backend
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js with Socket.io
- **Database ORM**: Supabase client with type generation
- **Authentication**: Supabase Auth + custom middleware
- **Payment Processing**: Stripe SDK
- **MT5 Integration**: Custom MT5 Expert Advisor + REST API bridge

### Database Schema (Supabase)
- Users & Authentication
- Trading Accounts & Challenges
- Trades & Performance Metrics
- Risk Management Rules
- Payment Records
- Audit Logs

### External Services
- **Supabase**: Database, Auth, Real-time subscriptions
- **Stripe**: Payment processing and subscription management
- **MetaTrader 5**: Trading execution and market data
- **KYC Provider**: Identity verification (Jumio/Onfido)
- **Email Service**: Transactional emails (SendGrid)

## Security Requirements

### Data Protection
- Encrypt sensitive data at rest and in transit
- Implement proper API rate limiting
- Use JWT tokens with short expiration
- Store API keys in environment variables only
- Implement CORS policies

### Trading Security
- Real-time risk monitoring and position limits
- Automated drawdown protection
- IP whitelisting for API access
- Multi-factor authentication for account access
- Audit logging for all trading activities

### Compliance
- KYC/AML verification workflow
- Geographic restrictions and compliance checks
- Risk disclosure and agreement system
- Record keeping for regulatory requirements

## Development Constraints

### File Organization
- Maximum 500 lines per file
- Modular architecture with clear separation of concerns
- Consistent naming conventions across all files
- Use barrel exports for clean imports

### Code Quality
- TypeScript strict mode enabled
- ESLint + Prettier for code formatting
- Comprehensive unit and integration tests
- JSDoc comments for all functions
- Error handling with proper logging

### Performance
- Implement caching strategies for frequently accessed data
- Optimize database queries with proper indexing
- Use connection pooling for database connections
- Implement WebSocket connection management
- Lazy loading for non-critical components

## Key Features

### Trader Onboarding
1. User registration and email verification
2. KYC verification process
3. Challenge selection and payment
4. Risk acknowledgment and agreement

### Challenge System
- Multiple challenge phases (evaluation, verification, funded)
- Configurable risk parameters per challenge
- Real-time performance tracking
- Automated progression/failure handling

### Trading Interface
- Real-time price feeds and charts
- Order management (market, limit, stop orders)
- Position monitoring and P&L tracking
- Risk metrics dashboard
- Trading journal and notes

### Risk Management
- Real-time drawdown monitoring
- Position size limits
- Daily loss limits
- Automated account suspension
- Risk score calculation

### Payment System
- Challenge fee processing
- Profit sharing calculations
- Automated payouts to traders
- Payment history and invoicing

## Development Phases

### Phase 1: Foundation (Weeks 1-2)
- Project setup and environment configuration
- Database schema design and implementation
- Basic authentication system
- Core API structure

### Phase 2: User Management (Weeks 3-4)
- User registration and profile management
- KYC integration and verification flow
- Role-based access control
- Email notification system

### Phase 3: Challenge System (Weeks 5-7)
- Challenge configuration and management
- Payment integration with Stripe
- Challenge progression logic
- Performance tracking system

### Phase 4: Trading Core (Weeks 8-10)
- MT5 integration and bridge development
- Real-time trading interface
- Order management system
- Risk monitoring implementation

### Phase 5: Advanced Features (Weeks 11-12)
- Analytics and reporting dashboard
- Advanced risk management features
- Performance optimization
- Security hardening

### Phase 6: Testing & Launch (Weeks 13-14)
- Comprehensive testing suite
- Security audit and penetration testing
- Performance testing and optimization
- Production deployment and monitoring

## Success Metrics
- User registration and conversion rates
- Challenge completion percentages
- Platform uptime and performance
- Trading volume and activity
- Customer satisfaction scores

## Risk Mitigation
- Implement circuit breakers for system failures
- Regular security audits and updates
- Backup and disaster recovery procedures
- Compliance monitoring and reporting
- Customer support and escalation procedures
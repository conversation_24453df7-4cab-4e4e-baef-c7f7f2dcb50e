import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options) {
          request.cookies.set({
            name,
            value,
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options) {
          request.cookies.set({
            name,
            value: '',
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )

  // Debug: Log the current path and cookies
  console.log('Middleware - Path:', request.nextUrl.pathname)
  console.log('Middleware - Cookies:', request.cookies.getAll().map(c => c.name))

  // Refresh session if expired - required for Server Components
  const { data: { session }, error } = await supabase.auth.getSession()

  // Debug: Log session status
  console.log('Middleware - Session exists:', !!session)
  console.log('Middleware - Session error:', error)
  if (session) {
    console.log('Middleware - User ID:', session.user?.id)
  }

  const isAuthPage = request.nextUrl.pathname.startsWith('/auth') || request.nextUrl.pathname.startsWith('/(auth)')
  const isProtectedPage = request.nextUrl.pathname.startsWith('/dashboard') || 
                          request.nextUrl.pathname.startsWith('/account') ||
                          request.nextUrl.pathname.startsWith('/challenges')

  console.log('Middleware - Is auth page:', isAuthPage)
  console.log('Middleware - Is protected page:', isProtectedPage)

  // Redirect authenticated users away from auth pages
  if (isAuthPage && session) {
    console.log('Middleware - Redirecting authenticated user away from auth page')
    const dashboardUrl = new URL('/dashboard', request.url)
    console.log('Middleware - Dashboard URL:', dashboardUrl.toString())
    return NextResponse.redirect(dashboardUrl)
  }

  // Redirect unauthenticated users to login for protected pages
  if (isProtectedPage && !session) {
    console.log('Middleware - Redirecting unauthenticated user to login')
    
    // Check if this is a redirect loop (user keeps getting sent back to login)
    const referer = request.headers.get('referer')
    if (referer && referer.includes('/auth/login')) {
      console.log('Middleware - Potential redirect loop detected, allowing through')
      return response
    }
    
    const redirectUrl = new URL('/auth/login', request.url)
    redirectUrl.searchParams.set('redirect', request.nextUrl.pathname)
    return NextResponse.redirect(redirectUrl)
  }

  console.log('Middleware - Allowing request to continue')
  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - api/webhooks (public webhooks)
     */
    '/((?!_next/static|_next/image|favicon.ico|api/webhooks).*)',
  ],
} 
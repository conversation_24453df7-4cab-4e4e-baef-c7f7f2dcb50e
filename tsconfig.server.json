{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src/server", "module": "commonjs", "target": "es2020", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "sourceMap": true}, "include": ["src/server/**/*", "src/lib/**/*", "src/types/**/*"], "exclude": ["node_modules", "dist", ".next", "src/app", "src/components", "src/hooks", "src/stores"]}
import { Router, Response } from 'express'
import express from 'express'
import { z } from 'zod'
import { supabase } from '../../lib/supabase/server'
import { validateRequest, ValidationRequest } from '../middleware/validation'
import { asyncHandler, createValidationError, createNotFoundError, createInternalError } from '../middleware/errorHandler'
import { AuthenticatedRequest } from '../middleware/auth'
import Stripe from 'stripe'

const router = Router()

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2025-02-24.acacia'
})

// Validation schemas
const createPaymentIntentSchema = z.object({
  challengeId: z.string().uuid('Invalid challenge ID format'),
  amount: z.number().min(1, 'Amount must be greater than 0'),
  currency: z.string().length(3, 'Currency must be a 3-letter ISO code').default('USD'),
  paymentMethod: z.string().optional()
})

const confirmPaymentSchema = z.object({
  paymentIntentId: z.string().min(1, 'Payment intent ID is required'),
  paymentMethodId: z.string().min(1, 'Payment method ID is required')
})

const paymentHistoryQuerySchema = z.object({
  limit: z.number().min(1).max(100).optional().default(20),
  offset: z.number().min(0).optional().default(0),
  status: z.enum(['pending', 'succeeded', 'failed', 'refunded']).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional()
})

const refundRequestSchema = z.object({
  paymentId: z.string().uuid('Invalid payment ID format'),
  amount: z.number().min(1).optional(), // Partial refund if specified
  reason: z.string().min(5).max(500)
})

// Helper function to get challenge details
const getChallengeDetails = async (challengeId: string) => {
  const { data: challenge, error } = await supabase
    .from('challenges')
    .select('*')
    .eq('id', challengeId)
    .single()

  if (error || !challenge) {
    throw createNotFoundError('Challenge not found')
  }

  return challenge
}

// POST /api/payments/create-intent
router.post('/create-intent',
  validateRequest({ body: createPaymentIntentSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { challengeId, amount, currency, paymentMethod } = req.validatedBody
    const userId = req.user!.id

    try {
      // Get challenge details
      const challenge = await getChallengeDetails(challengeId)

      // Verify amount matches challenge price
      if (amount !== challenge.price) {
        throw createValidationError('Payment amount does not match challenge price')
      }

      // Get user details for Stripe
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('email, first_name, last_name')
        .eq('id', userId)
        .single()

      if (userError || !user) {
        throw createNotFoundError('User not found')
      }

      // Create or retrieve Stripe customer
      let customerId: string
      const { data: existingCustomer } = await supabase
        .from('stripe_customers')
        .select('stripe_customer_id')
        .eq('user_id', userId)
        .single()

      if (existingCustomer) {
        customerId = existingCustomer.stripe_customer_id
      } else {
        // Create new Stripe customer
        const customer = await stripe.customers.create({
          email: user.email,
          name: `${user.first_name} ${user.last_name}`,
          metadata: {
            user_id: userId,
            propfirm_customer: 'true'
          }
        })

        customerId = customer.id

        // Store customer ID
        await supabase
          .from('stripe_customers')
          .insert({
            user_id: userId,
            stripe_customer_id: customerId
          })
      }

      // Create payment intent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency: currency.toLowerCase(),
        customer: customerId,
        payment_method: paymentMethod,
        confirmation_method: 'manual',
        confirm: false,
        metadata: {
          user_id: userId,
          challenge_id: challengeId,
          challenge_name: challenge.name,
          propfirm_payment: 'true'
        },
        description: `Payment for ${challenge.name} challenge`
      })

      // Store payment record in database
      const { data: paymentRecord, error: paymentError } = await supabase
        .from('payments')
        .insert({
          user_id: userId,
          challenge_id: challengeId,
          stripe_payment_intent_id: paymentIntent.id,
          amount,
          currency,
          status: 'pending',
          description: `Payment for ${challenge.name} challenge`
        })
        .select()
        .single()

      if (paymentError) {
        throw createInternalError('Failed to create payment record')
      }

      res.json({
        success: true,
        data: {
          clientSecret: paymentIntent.client_secret,
          paymentIntentId: paymentIntent.id,
          amount,
          currency,
          paymentRecordId: paymentRecord.id
        }
      })

    } catch (error) {
      // Handle Stripe errors
      if (error instanceof Stripe.errors.StripeError) {
        throw createValidationError(`Payment error: ${error.message}`)
      }
      throw error
    }
  })
)

// POST /api/payments/confirm
router.post('/confirm',
  validateRequest({ body: confirmPaymentSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { paymentIntentId, paymentMethodId } = req.validatedBody
    const userId = req.user!.id

    try {
      // Verify payment intent belongs to user
      const { data: paymentRecord, error: paymentError } = await supabase
        .from('payments')
        .select('*')
        .eq('stripe_payment_intent_id', paymentIntentId)
        .eq('user_id', userId)
        .single()

      if (paymentError || !paymentRecord) {
        throw createNotFoundError('Payment not found or access denied')
      }

      // Confirm payment with Stripe
      const paymentIntent = await stripe.paymentIntents.confirm(paymentIntentId, {
        payment_method: paymentMethodId,
        return_url: `${process.env.FRONTEND_URL}/dashboard/challenges`
      })

      // Update payment record
      const { error: updateError } = await supabase
        .from('payments')
        .update({
          status: paymentIntent.status === 'succeeded' ? 'succeeded' : 'pending',
          stripe_payment_method_id: paymentMethodId,
          processed_at: paymentIntent.status === 'succeeded' ? new Date().toISOString() : null,
          updated_at: new Date().toISOString()
        })
        .eq('id', paymentRecord.id)

      if (updateError) {
        throw createInternalError('Failed to update payment record')
      }

      // If payment succeeded, create trading account
      if (paymentIntent.status === 'succeeded') {
        const challenge = await getChallengeDetails(paymentRecord.challenge_id)

        const { error: accountError } = await supabase
          .from('trading_accounts')
          .insert({
            user_id: userId,
            challenge_id: paymentRecord.challenge_id,
            account_type: 'demo', // Start with demo for prop firm challenges
            balance: challenge.starting_balance,
            equity: challenge.starting_balance,
            currency: challenge.currency,
            leverage: challenge.max_leverage,
            status: 'active'
          })

        if (accountError) {
          console.error('Failed to create trading account:', accountError)
          // Don't throw error here as payment was successful
        }

        // Create audit log
        await supabase
          .from('audit_logs')
          .insert({
            user_id: userId,
            action: 'payment_completed',
            details: {
              payment_id: paymentRecord.id,
              challenge_id: paymentRecord.challenge_id,
              amount: paymentRecord.amount,
              currency: paymentRecord.currency
            }
          })
      }

      res.json({
        success: true,
        data: {
          status: paymentIntent.status,
          paymentIntentId,
          requiresAction: paymentIntent.status === 'requires_action',
          clientSecret: paymentIntent.client_secret
        }
      })

    } catch (error) {
      if (error instanceof Stripe.errors.StripeError) {
        throw createValidationError(`Payment confirmation error: ${error.message}`)
      }
      throw error
    }
  })
)

// GET /api/payments/history
router.get('/history',
  validateRequest({ query: paymentHistoryQuerySchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { limit, offset, status, startDate, endDate } = req.validatedQuery
    const userId = req.user!.id

    try {
      let query = supabase
        .from('payments')
        .select(`
          *,
          challenges(name, type),
          trading_accounts(id, account_number)
        `, { count: 'exact' })
        .eq('user_id', userId)

      // Apply filters
      if (status) {
        query = query.eq('status', status)
      }

      if (startDate) {
        query = query.gte('created_at', startDate)
      }

      if (endDate) {
        query = query.lte('created_at', endDate)
      }

      const { data: payments, error, count } = await query
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (error) {
        throw createInternalError('Failed to fetch payment history')
      }

      const formattedPayments = payments?.map(payment => ({
        id: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        status: payment.status,
        description: payment.description,
        challengeId: payment.challenge_id,
        challengeName: payment.challenges?.name,
        challengeType: payment.challenges?.type,
        accountId: payment.trading_accounts?.id,
        accountNumber: payment.trading_accounts?.account_number,
        createdAt: payment.created_at,
        processedAt: payment.processed_at
      })) || []

      res.json({
        success: true,
        data: {
          payments: formattedPayments,
          total: count || 0
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// POST /api/payments/refund
router.post('/refund',
  validateRequest({ body: refundRequestSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { paymentId, amount, reason } = req.validatedBody
    const userId = req.user!.id

    try {
      // Get payment record
      const { data: payment, error: paymentError } = await supabase
        .from('payments')
        .select('*')
        .eq('id', paymentId)
        .eq('user_id', userId)
        .single()

      if (paymentError || !payment) {
        throw createNotFoundError('Payment not found or access denied')
      }

      if (payment.status !== 'succeeded') {
        throw createValidationError('Only successful payments can be refunded')
      }

      // Check if already refunded
      if (payment.status === 'refunded') {
        throw createValidationError('Payment has already been refunded')
      }

      // Create refund with Stripe
      const refundParams: Stripe.RefundCreateParams = {
        payment_intent: payment.stripe_payment_intent_id,
        reason: 'requested_by_customer',
        metadata: {
          user_id: userId,
          refund_reason: reason,
          propfirm_refund: 'true'
        }
      }
      
      if (amount) {
        refundParams.amount = Math.round(amount * 100)
      }
      
      const refund = await stripe.refunds.create(refundParams)

      // Update payment record
      const { error: updateError } = await supabase
        .from('payments')
        .update({
          status: refund.amount === payment.amount * 100 ? 'refunded' : 'partially_refunded',
          refund_amount: refund.amount / 100,
          refund_reason: reason,
          updated_at: new Date().toISOString()
        })
        .eq('id', paymentId)

      if (updateError) {
        throw createInternalError('Failed to update payment record')
      }

      // If full refund, deactivate trading account
      if (refund.amount === payment.amount * 100) {
        await supabase
          .from('trading_accounts')
          .update({
            status: 'suspended',
            suspension_reason: 'Payment refunded'
          })
          .eq('user_id', userId)
          .eq('challenge_id', payment.challenge_id)
      }

      // Create audit log
      await supabase
        .from('audit_logs')
        .insert({
          user_id: userId,
          action: 'payment_refunded',
          details: {
            payment_id: paymentId,
            refund_amount: refund.amount / 100,
            reason,
            stripe_refund_id: refund.id
          }
        })

      res.json({
        success: true,
        data: {
          refundId: refund.id,
          amount: refund.amount / 100,
          status: refund.status,
          message: 'Refund processed successfully'
        }
      })

    } catch (error) {
      if (error instanceof Stripe.errors.StripeError) {
        throw createValidationError(`Refund error: ${error.message}`)
      }
      throw error
    }
  })
)

// POST /api/payments/webhook (Stripe webhooks)
router.post('/webhook',
  express.raw({ type: 'application/json' }),
  asyncHandler(async (req: any, res: Response) => {
    const sig = req.headers['stripe-signature']
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET

    if (!sig || !endpointSecret) {
      throw createValidationError('Missing Stripe signature or webhook secret')
    }

    try {
      // Verify webhook signature
      const event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret)

      // Handle different event types
      switch (event.type) {
        case 'payment_intent.succeeded':
          await handlePaymentSuccess(event.data.object as Stripe.PaymentIntent)
          break

        case 'payment_intent.payment_failed':
          await handlePaymentFailure(event.data.object as Stripe.PaymentIntent)
          break

        case 'payment_intent.canceled':
          await handlePaymentCancellation(event.data.object as Stripe.PaymentIntent)
          break

        case 'charge.dispute.created':
          await handleChargeDispute(event.data.object as Stripe.Dispute)
          break

        default:
          console.log(`Unhandled event type: ${event.type}`)
      }

      res.json({ received: true })

    } catch (error) {
      console.error('Webhook error:', error)
      if (error instanceof Stripe.errors.StripeSignatureVerificationError) {
        throw createValidationError('Invalid webhook signature')
      }
      throw error
    }
  })
)

// Webhook handler functions
const handlePaymentSuccess = async (paymentIntent: Stripe.PaymentIntent) => {
  const { error } = await supabase
    .from('payments')
    .update({
      status: 'succeeded',
      processed_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .eq('stripe_payment_intent_id', paymentIntent.id)

  if (error) {
    console.error('Failed to update payment status:', error)
  }
}

const handlePaymentFailure = async (paymentIntent: Stripe.PaymentIntent) => {
  const { error } = await supabase
    .from('payments')
    .update({
      status: 'failed',
      failure_reason: paymentIntent.last_payment_error?.message || 'Payment failed',
      updated_at: new Date().toISOString()
    })
    .eq('stripe_payment_intent_id', paymentIntent.id)

  if (error) {
    console.error('Failed to update payment status:', error)
  }
}

const handlePaymentCancellation = async (paymentIntent: Stripe.PaymentIntent) => {
  const { error } = await supabase
    .from('payments')
    .update({
      status: 'canceled',
      updated_at: new Date().toISOString()
    })
    .eq('stripe_payment_intent_id', paymentIntent.id)

  if (error) {
    console.error('Failed to update payment status:', error)
  }
}

const handleChargeDispute = async (dispute: Stripe.Dispute) => {
  // Find the payment and create alert
  const { data: payment } = await supabase
    .from('payments')
    .select('user_id, challenge_id')
    .eq('stripe_charge_id', dispute.charge)
    .single()

  if (payment) {
    // Create risk alert for dispute
    await supabase
      .from('risk_alerts')
      .insert({
        account_id: payment.challenge_id,
        type: 'danger',
        title: 'Payment Dispute Filed',
        message: `A payment dispute has been filed for amount ${dispute.amount / 100} ${dispute.currency.toUpperCase()}`,
        severity: 9,
        metadata: {
          dispute_id: dispute.id,
          dispute_reason: dispute.reason,
          dispute_amount: dispute.amount / 100
        }
      })
  }
}

export default router 
import { NextResponse } from 'next/server'
import { createAdminClient } from '@/lib/supabase/server'

export async function POST() {
  try {
    const supabaseAdmin = createAdminClient()

    // First, check if user already exists
    const { data: existingUsers } = await supabaseAdmin.auth.admin.listUsers()
    const existingUser = existingUsers.users.find(user => user.email === '<EMAIL>')
    
    let authUser;
    
    if (existingUser) {
      console.log('User already exists in auth, using existing user')
      authUser = existingUser
    } else {
      // Create user with admin client (bypasses email verification)
      const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
        email: '<EMAIL>',
        password: 'Admin123!',
        email_confirm: true, // Mark email as verified
        user_metadata: {
          first_name: 'Admin',
          last_name: 'User',
          country: 'United States'
        }
      })

      if (authError) {
        console.error('Auth user creation error:', authError)
        return NextResponse.json({ 
          success: false, 
          error: `Failed to create auth user: ${authError.message}` 
        }, { status: 400 })
      }

      authUser = authData.user
      console.log('Created auth user:', authUser.id)
    }

    // Now create/update user profile in users table
    const { data: userProfile, error: profileError } = await supabaseAdmin
      .from('users')
      .upsert({
        id: authUser.id,
        email: authUser.email!,
        first_name: 'Admin',
        last_name: 'User',
        country: 'United States',
        date_of_birth: '1990-01-01',
        kyc_status: 'approved', // Set as approved for testing
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (profileError) {
      console.error('Profile creation error:', profileError)
      return NextResponse.json({ 
        success: false, 
        error: `Failed to create user profile: ${profileError.message}` 
      }, { status: 400 })
    }

    console.log('Created user profile:', userProfile.id)

    return NextResponse.json({ 
      success: true, 
      message: 'Admin user created successfully',
      data: {
        auth_user_id: authUser.id,
        profile_id: userProfile.id,
        email: authUser.email
      }
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ 
      success: false, 
      error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}` 
    }, { status: 500 })
  }
} 
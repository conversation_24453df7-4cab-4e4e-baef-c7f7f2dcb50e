import { Metadata } from 'next'
import Link from 'next/link'
import { TrendingUp, Lock } from 'lucide-react'
import { LoginForm } from '@/components/auth/LoginForm'

export const metadata: Metadata = {
  title: 'Login - PropFirm Elite Trading Platform',
  description: 'Access your trading account and continue your journey to becoming a funded trader.',
}

export default function LoginPage() {
  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
        <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 via-transparent to-blue-500/5 animate-pulse" />
      </div>

      {/* Floating Background Elements */}
      <div className="absolute top-20 left-20 w-32 h-32 bg-green-500/10 rounded-full blur-xl animate-pulse" />
      <div className="absolute bottom-20 right-20 w-48 h-48 bg-blue-500/10 rounded-full blur-xl animate-pulse delay-1000" />
      <div className="absolute top-1/2 left-10 w-24 h-24 bg-amber-500/10 rounded-full blur-xl animate-pulse delay-500" />

      {/* Main Content */}
      <div className="relative z-10 w-full max-w-md mx-auto p-6">
        {/* Header */}
        <div className="text-center mb-8 animate-fade-in-up">
          <div className="flex items-center justify-center space-x-3 mb-6">
            <div className="flex h-12 w-12 items-center justify-center rounded-xl professional-button">
              <TrendingUp className="h-8 w-8 text-slate-800" />
            </div>
            <span className="text-3xl font-bold text-gradient-gold">PropFirm</span>
          </div>
          
          <h1 className="text-3xl font-bold text-primary-content mb-2">
            Welcome Back
          </h1>
          <p className="text-secondary-content">
            Sign in to access your trading dashboard
          </p>
        </div>

        {/* Login Form Card */}
        <div className="glass-card p-8 neon-border animate-fade-in-up">
          <div className="flex items-center justify-center mb-6">
            <div className="live-indicator">
              <Lock className="h-5 w-5 text-green-400" />
              <span className="text-sm font-medium text-green-400">Secure Login</span>
            </div>
          </div>

          <LoginForm />

          {/* Social Login Divider */}
          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-slate-700/50" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="bg-slate-800 px-4 text-secondary-content">Or continue with</span>
            </div>
          </div>

          {/* Social Login Buttons */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <button className="glass-card p-3 neon-border hover:bg-slate-700/50 transition-all group">
              <div className="flex items-center justify-center space-x-2">
                <div className="w-5 h-5 bg-white rounded-sm flex items-center justify-center">
                  <span className="text-slate-900 font-bold text-xs">G</span>
                </div>
                <span className="text-secondary-content text-sm group-hover:text-primary-content">Google</span>
              </div>
            </button>
            
            <button className="glass-card p-3 neon-border hover:bg-slate-700/50 transition-all group">
              <div className="flex items-center justify-center space-x-2">
                <div className="w-5 h-5 bg-blue-600 rounded-sm flex items-center justify-center">
                  <span className="text-white font-bold text-xs">f</span>
                </div>
                <span className="text-secondary-content text-sm group-hover:text-primary-content">Facebook</span>
              </div>
            </button>
          </div>

          {/* Register Link */}
          <div className="text-center">
            <p className="text-secondary-content text-sm">
              Don&apos;t have an account?{' '}
              <Link 
                href="/auth/register" 
                className="text-green-400 hover:text-green-300 font-medium transition-colors"
              >
                Start your trading journey
              </Link>
            </p>
          </div>
        </div>

        {/* Security Features */}
        <div className="mt-8 text-center animate-fade-in-up">
          <div className="flex items-center justify-center space-x-6 text-xs text-secondary-content">
            <div className="live-indicator">
              <div className="live-dot" />
              <span>256-bit SSL</span>
            </div>
            <div className="live-indicator">
              <div className="live-dot" />
              <span>2FA Protected</span>
            </div>
            <div className="live-indicator">
              <div className="live-dot" />
              <span>SOC 2 Compliant</span>
            </div>
          </div>
        </div>

        {/* Back to Home */}
        <div className="mt-8 text-center">
          <Link 
            href="/" 
            className="inline-flex items-center text-secondary-content hover:text-primary-content text-sm transition-colors"
          >
            ← Back to home
          </Link>
        </div>
      </div>
    </div>
  )
} 
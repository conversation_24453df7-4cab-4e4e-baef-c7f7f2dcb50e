import { Metadata } from 'next'
import Link from 'next/link'
import { ArrowLeft } from 'lucide-react'
import { Logo } from '@/components/ui/Logo'
import { DottedText } from '@/components/ui/DottedText'
import { EliteText } from '@/components/ui/EliteText'

export const metadata: Metadata = {
  title: 'Text Styling Demo - Prop Bully',
  description: 'Compare different text styling options for the Prop Bully brand',
}

export default function TextDemoPage() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Professional Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black">
        <div className="absolute inset-0 bg-gradient-to-r from-green-500/3 via-transparent to-blue-500/3" />
      </div>

      <div className="relative z-10 max-w-6xl mx-auto p-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-12">
          <div className="flex items-center space-x-4">
            <Logo size="md" imageSrc="/images/logos/prop-bully-logo.png" alt="Prop Bully Logo" />
            <h1 className="text-3xl font-bold text-white">Text Styling Demo</h1>
          </div>
          <Link href="/" className="flex items-center space-x-2 text-white/60 hover:text-white transition-colors">
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Home</span>
          </Link>
        </div>

        {/* Demo Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          
          {/* Original DottedText */}
          <div className="glass-card p-8">
            <h2 className="text-xl font-bold text-white mb-6">Original Style (DottedText)</h2>
            <div className="space-y-6">
              <div className="flex items-center space-x-3">
                <Logo size="sm" />
                <DottedText size="sm">Prop Bully</DottedText>
              </div>
              <div className="flex items-center space-x-3">
                <Logo size="md" />
                <DottedText size="md">Prop Bully</DottedText>
              </div>
              <div className="flex items-center space-x-3">
                <Logo size="lg" />
                <DottedText size="lg">Prop Bully</DottedText>
              </div>
              <div className="text-center">
                <DottedText size="xl">Prop Bully</DottedText>
              </div>
            </div>
            <p className="text-white/60 text-sm mt-4">
              Features: Dotted overlay effect, Courier New font, retro gaming aesthetic
            </p>
          </div>

          {/* New EliteText - Gold */}
          <div className="glass-card p-8">
            <h2 className="text-xl font-bold text-white mb-6">New Elite Style - Gold</h2>
            <div className="space-y-6">
              <div className="flex items-center space-x-3">
                <Logo size="sm" />
                <EliteText size="sm" variant="gold">Prop Bully</EliteText>
              </div>
              <div className="flex items-center space-x-3">
                <Logo size="md" />
                <EliteText size="md" variant="gold">Prop Bully</EliteText>
              </div>
              <div className="flex items-center space-x-3">
                <Logo size="lg" />
                <EliteText size="lg" variant="gold">Prop Bully</EliteText>
              </div>
              <div className="text-center">
                <EliteText size="xl" variant="gold">Prop Bully</EliteText>
              </div>
            </div>
            <p className="text-white/60 text-sm mt-4">
              Features: Sharp edges, professional glow, Orbitron font, institutional feel
            </p>
          </div>

          {/* New EliteText - Platinum */}
          <div className="glass-card p-8">
            <h2 className="text-xl font-bold text-white mb-6">Elite Style - Platinum</h2>
            <div className="space-y-6">
              <div className="flex items-center space-x-3">
                <Logo size="sm" />
                <EliteText size="sm" variant="platinum">Prop Bully</EliteText>
              </div>
              <div className="flex items-center space-x-3">
                <Logo size="md" />
                <EliteText size="md" variant="platinum">Prop Bully</EliteText>
              </div>
              <div className="flex items-center space-x-3">
                <Logo size="lg" />
                <EliteText size="lg" variant="platinum">Prop Bully</EliteText>
              </div>
              <div className="text-center">
                <EliteText size="xl" variant="platinum">Prop Bully</EliteText>
              </div>
            </div>
            <p className="text-white/60 text-sm mt-4">
              Features: Silver/platinum styling, sophisticated, premium financial look
            </p>
          </div>

          {/* New EliteText - Emerald */}
          <div className="glass-card p-8">
            <h2 className="text-xl font-bold text-white mb-6">Elite Style - Emerald</h2>
            <div className="space-y-6">
              <div className="flex items-center space-x-3">
                <Logo size="sm" />
                <EliteText size="sm" variant="emerald">Prop Bully</EliteText>
              </div>
              <div className="flex items-center space-x-3">
                <Logo size="md" />
                <EliteText size="md" variant="emerald">Prop Bully</EliteText>
              </div>
              <div className="flex items-center space-x-3">
                <Logo size="lg" />
                <EliteText size="lg" variant="emerald">Prop Bully</EliteText>
              </div>
              <div className="text-center">
                <EliteText size="xl" variant="emerald">Prop Bully</EliteText>
              </div>
            </div>
            <p className="text-white/60 text-sm mt-4">
              Features: Green/emerald styling, matches logo colors, trading/money theme
            </p>
          </div>
        </div>

        {/* Comparison Section */}
        <div className="glass-card p-8">
          <h2 className="text-2xl font-bold text-white mb-6 text-center">Side-by-Side Comparison</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div>
              <h3 className="text-white/80 text-sm mb-4">Original</h3>
              <DottedText size="lg">Prop Bully</DottedText>
            </div>
            <div>
              <h3 className="text-white/80 text-sm mb-4">Elite Gold</h3>
              <EliteText size="lg" variant="gold">Prop Bully</EliteText>
            </div>
            <div>
              <h3 className="text-white/80 text-sm mb-4">Elite Platinum</h3>
              <EliteText size="lg" variant="platinum">Prop Bully</EliteText>
            </div>
            <div>
              <h3 className="text-white/80 text-sm mb-4">Elite Emerald</h3>
              <EliteText size="lg" variant="emerald">Prop Bully</EliteText>
            </div>
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="glass-card p-8 mt-8">
          <h2 className="text-xl font-bold text-white mb-4">Usage Instructions</h2>
          <div className="space-y-4 text-white/80">
            <div>
              <h3 className="font-semibold text-white mb-2">Original DottedText:</h3>
              <code className="bg-slate-800 p-2 rounded text-sm">
                &lt;DottedText size="lg"&gt;Prop Bully&lt;/DottedText&gt;
              </code>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-2">New EliteText:</h3>
              <code className="bg-slate-800 p-2 rounded text-sm">
                &lt;EliteText size="lg" variant="gold"&gt;Prop Bully&lt;/EliteText&gt;
              </code>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-2">Variants Available:</h3>
              <ul className="list-disc list-inside text-sm space-y-1 ml-4">
                <li><code>variant="gold"</code> - Premium gold styling (recommended)</li>
                <li><code>variant="platinum"</code> - Sophisticated silver styling</li>
                <li><code>variant="emerald"</code> - Trading green styling</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 
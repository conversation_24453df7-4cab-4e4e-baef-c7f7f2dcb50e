import React from 'react'
import Image from 'next/image'

interface LogoProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
  imageSrc?: string
  alt?: string
}

export function Logo({ size = 'md', className = '', imageSrc, alt = 'Prop Bully Logo' }: LogoProps) {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12', 
    lg: 'h-16 w-16'
  }

  const imageSizes = {
    sm: 32,
    md: 48,
    lg: 64
  }

  // If PNG image is provided, use it
  if (imageSrc) {
    return (
      <div className={`${sizeClasses[size]} ${className} relative rounded-xl overflow-hidden shadow-lg`}>
        <Image
          src={imageSrc}
          alt={alt}
          width={imageSizes[size]}
          height={imageSizes[size]}
          className="object-contain w-full h-full"
          priority
        />
      </div>
    )
  }

  // Fallback to SVG if no image provided
  return (
    <div className={`${sizeClasses[size]} ${className} flex items-center justify-center rounded-xl bg-gradient-to-br from-emerald-600 via-green-600 to-emerald-700 shadow-lg relative overflow-hidden`}>
      {/* Strong Bull Head SVG with Prominent Horns */}
      <svg 
        viewBox="0 0 100 100" 
        className="w-4/5 h-4/5 fill-white"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Left Horn - Much More Prominent */}
        <path d="M25 30 C15 20, 8 15, 2 18 C0 19, 0 22, 3 24 C8 26, 15 30, 25 35 L30 38 Z" 
              className="fill-white" 
              stroke="#059669" 
              strokeWidth="1" />
        
        {/* Right Horn - Much More Prominent */}
        <path d="M75 30 C85 20, 92 15, 98 18 C100 19, 100 22, 97 24 C92 26, 85 30, 75 35 L70 38 Z" 
              className="fill-white" 
              stroke="#059669" 
              strokeWidth="1" />
        
        {/* Main Bull Head - Wider and More Muscular */}
        <path d="M50 25 C65 25, 78 35, 78 50 L78 65 C78 75, 70 82, 60 85 L40 85 C30 82, 22 75, 22 65 L22 50 C22 35, 35 25, 50 25 Z" />
        
        {/* Forehead/Brow Ridge - More Prominent */}
        <path d="M30 42 Q50 38 70 42 Q65 48 50 49 Q35 48 30 42 Z" className="fill-green-100" />
        
        {/* Muscular Neck/Chest Area */}
        <ellipse cx="50" cy="82" rx="18" ry="8" className="fill-green-200" />
        
        {/* Eyes - Fierce and Intense */}
        <ellipse cx="38" cy="50" rx="5" ry="6" className="fill-red-600" />
        <ellipse cx="62" cy="50" rx="5" ry="6" className="fill-red-600" />
        
        {/* Eye pupils/highlights - Angry look */}
        <circle cx="39" cy="48" r="2" className="fill-white" />
        <circle cx="63" cy="48" r="2" className="fill-white" />
        
        {/* Angry Eyebrows */}
        <path d="M32 44 L44 46" stroke="#059669" strokeWidth="2" className="fill-none" />
        <path d="M56 46 L68 44" stroke="#059669" strokeWidth="2" className="fill-none" />
        
        {/* Large Snout/Muzzle */}
        <ellipse cx="50" cy="65" rx="15" ry="10" className="fill-green-100" />
        
        {/* Large Nostrils - Bull-like */}
        <ellipse cx="45" cy="63" rx="3" ry="5" className="fill-green-800" />
        <ellipse cx="55" cy="63" rx="3" ry="5" className="fill-green-800" />
        
        {/* Angry Mouth */}
        <path d="M40 72 Q50 77 60 72" stroke="#059669" strokeWidth="3" fill="none" />
        
        {/* Additional Horn Details - Make them look sharp */}
        <path d="M25 30 L20 25 L15 22" stroke="#047857" strokeWidth="1.5" fill="none" />
        <path d="M75 30 L80 25 L85 22" stroke="#047857" strokeWidth="1.5" fill="none" />
        
        {/* Face Definition Lines */}
        <path d="M25 55 Q30 60 35 55" stroke="#059669" strokeWidth="1" fill="none" className="opacity-50" />
        <path d="M65 55 Q70 60 75 55" stroke="#059669" strokeWidth="1" fill="none" className="opacity-50" />
        
        {/* Horn Base Connection */}
        <ellipse cx="27" cy="35" rx="4" ry="3" className="fill-green-200" />
        <ellipse cx="73" cy="35" rx="4" ry="3" className="fill-green-200" />
      </svg>
      
      {/* Gradient Overlay for depth */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-black/20 rounded-xl"></div>
      
      {/* Additional glow effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-emerald-300/20 via-transparent to-transparent rounded-xl"></div>
    </div>
  )
}

export default Logo 
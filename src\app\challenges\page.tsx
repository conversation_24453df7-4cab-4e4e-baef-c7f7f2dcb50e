import { <PERSON>ada<PERSON> } from 'next'
import Link from 'next/link'
import { ArrowLeft, ArrowRight, CheckCircle, Star, Shield, TrendingUp, Clock } from 'lucide-react'
import { Logo } from '@/components/ui/Logo'
import { EliteText } from '@/components/ui/EliteText'

export const metadata: Metadata = {
  title: 'Trading Challenges - Prop Bully',
  description: 'Choose your trading challenge package and start your journey to funded trading with Prop Bully.',
}

export default function ChallengesPage() {
  const challenges = [
    {
      id: 'starter',
      name: 'Starter Challenge',
      capital: '$10,000',
      price: '$149',
      profitTarget: '8%',
      maxDrawdown: '10%',
      tradingDays: '4 minimum',
      duration: '30 days',
      profitShare: '80%',
      popular: false,
      features: [
        'Live market conditions',
        'Real-time risk monitoring',
        'Expert Advisor allowed',
        'Weekend holding allowed',
        'Free retake on first attempt',
        '24/7 support access'
      ]
    },
    {
      id: 'professional',
      name: 'Professional Challenge',
      capital: '$25,000',
      price: '$299',
      profitTarget: '8%',
      maxDrawdown: '10%',
      tradingDays: '4 minimum',
      duration: '30 days',
      profitShare: '85%',
      popular: true,
      features: [
        'Live market conditions',
        'Real-time risk monitoring',
        'Expert Advisor allowed',
        'Weekend holding allowed',
        'Free retake on first attempt',
        '24/7 support access',
        'Priority customer support',
        'Advanced analytics dashboard'
      ]
    },
    {
      id: 'elite',
      name: 'Elite Challenge',
      capital: '$50,000',
      price: '$549',
      profitTarget: '8%',
      maxDrawdown: '10%',
      tradingDays: '4 minimum',
      duration: '30 days',
      profitShare: '90%',
      popular: false,
      features: [
        'Live market conditions',
        'Real-time risk monitoring',
        'Expert Advisor allowed',
        'Weekend holding allowed',
        'Free retake on first attempt',
        '24/7 support access',
        'Priority customer support',
        'Advanced analytics dashboard',
        'Personal account manager',
        'Custom risk parameters'
      ]
    },
    {
      id: 'institutional',
      name: 'Institutional Challenge',
      capital: '$100,000',
      price: '$999',
      profitTarget: '8%',
      maxDrawdown: '10%',
      tradingDays: '4 minimum',
      duration: '30 days',
      profitShare: '90%',
      popular: false,
      features: [
        'Live market conditions',
        'Real-time risk monitoring',
        'Expert Advisor allowed',
        'Weekend holding allowed',
        'Free retake on first attempt',
        '24/7 support access',
        'Priority customer support',
        'Advanced analytics dashboard',
        'Personal account manager',
        'Custom risk parameters',
        'Direct line to trading desk',
        'Institutional-grade execution'
      ]
    }
  ]

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Professional Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black">
        <div className="absolute inset-0 bg-gradient-to-r from-green-500/3 via-transparent to-blue-500/3" />
      </div>

      {/* Navigation */}
      <nav className="relative z-10 flex items-center justify-between p-6 border-b border-slate-700/50">
        <div className="flex items-center space-x-4">
          <Logo size="md" imageSrc="/images/logos/prop-bully-logo.png" alt="Prop Bully Logo" />
          <EliteText size="lg" variant="gold">Prop Bully</EliteText>
        </div>
        <div className="flex items-center space-x-4">
          <Link href="/" className="flex items-center space-x-2 text-white/60 hover:text-white transition-colors">
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Home</span>
          </Link>
          <Link href="/auth/login">
            <button className="professional-button">Sign In</button>
          </Link>
        </div>
      </nav>

      <div className="relative z-10 max-w-7xl mx-auto px-6 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            Trading <span className="text-gradient-gold">Challenges</span>
          </h1>
          <p className="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
            Choose your challenge package and prove your trading skills. Pass the evaluation to access our capital and keep up to 90% of your profits.
          </p>
        </div>

        {/* Challenge Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {challenges.map((challenge) => (
            <div key={challenge.id} className={`glass-card p-6 relative ${challenge.popular ? 'ring-2 ring-green-500/50' : ''}`}>
              {challenge.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-semibold flex items-center space-x-1">
                    <Star className="h-3 w-3" />
                    <span>Most Popular</span>
                  </div>
                </div>
              )}
              
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-white mb-2">{challenge.name}</h3>
                <div className="text-3xl font-bold text-green-400 mb-1">{challenge.capital}</div>
                <div className="text-white/60 text-sm">Trading Capital</div>
              </div>

              <div className="space-y-4 mb-6">
                <div className="flex justify-between items-center">
                  <span className="text-white/70 text-sm">Price</span>
                  <span className="text-white font-semibold">{challenge.price}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white/70 text-sm">Profit Target</span>
                  <span className="text-green-400 font-semibold">{challenge.profitTarget}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white/70 text-sm">Max Drawdown</span>
                  <span className="text-red-400 font-semibold">{challenge.maxDrawdown}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white/70 text-sm">Duration</span>
                  <span className="text-blue-400 font-semibold">{challenge.duration}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white/70 text-sm">Profit Share</span>
                  <span className="text-purple-400 font-semibold">{challenge.profitShare}</span>
                </div>
              </div>

              <div className="space-y-2 mb-6">
                {challenge.features.map((feature, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-400 flex-shrink-0 mt-0.5" />
                    <span className="text-white/80 text-xs">{feature}</span>
                  </div>
                ))}
              </div>

              <Link href={`/auth/register?challenge=${challenge.id}`}>
                <button className={`w-full py-3 px-4 rounded-lg font-semibold transition-all ${
                  challenge.popular 
                    ? 'professional-button-dark' 
                    : 'professional-button'
                }`}>
                  Start Challenge
                </button>
              </Link>
            </div>
          ))}
        </div>

        {/* Challenge Rules */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Challenge Rules</h2>
            <p className="text-white/70">Fair and transparent rules designed for your success</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="glass-card p-6">
              <div className="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center mb-4">
                <TrendingUp className="h-6 w-6 text-green-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-3">Profit Targets</h3>
              <ul className="space-y-2 text-white/70 text-sm">
                <li>• Phase 1: 8% profit target in 30 days</li>
                <li>• Phase 2: 5% profit target in 30 days</li>
                <li>• No time limit after funding</li>
                <li>• Consistent trading required</li>
              </ul>
            </div>

            <div className="glass-card p-6">
              <div className="w-12 h-12 bg-red-500/20 rounded-xl flex items-center justify-center mb-4">
                <Shield className="h-6 w-6 text-red-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-3">Risk Management</h3>
              <ul className="space-y-2 text-white/70 text-sm">
                <li>• Maximum daily loss: 5%</li>
                <li>• Maximum total drawdown: 10%</li>
                <li>• Position size limits enforced</li>
                <li>• Real-time monitoring system</li>
              </ul>
            </div>

            <div className="glass-card p-6">
              <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mb-4">
                <Clock className="h-6 w-6 text-blue-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-3">Trading Requirements</h3>
              <ul className="space-y-2 text-white/70 text-sm">
                <li>• Minimum 4 trading days</li>
                <li>• No forbidden trading styles</li>
                <li>• Expert Advisors allowed</li>
                <li>• Weekend holding permitted</li>
              </ul>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Frequently Asked Questions</h2>
            <p className="text-white/70">Everything you need to know about our challenges</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-white mb-3">What happens if I fail the challenge?</h3>
                <p className="text-white/70 text-sm">
                  You get one free retake on your first attempt. After that, you can purchase a new challenge at a 20% discount. 
                  Our support team is always available to help you understand what went wrong and improve your strategy.
                </p>
              </div>

              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-white mb-3">Can I use Expert Advisors (EAs)?</h3>
                <p className="text-white/70 text-sm">
                  Yes, Expert Advisors are fully allowed on all our challenge accounts. However, they must comply with our 
                  risk management rules. High-frequency scalping EAs that might cause server issues are not permitted.
                </p>
              </div>

              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-white mb-3">How long does verification take?</h3>
                <p className="text-white/70 text-sm">
                  Once you complete Phase 1, the verification phase (Phase 2) also takes 30 days with a 5% profit target. 
                  After successfully completing both phases, you receive your funded account within 24-48 hours.
                </p>
              </div>
            </div>

            <div className="space-y-6">
              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-white mb-3">What trading platforms do you support?</h3>
                <p className="text-white/70 text-sm">
                  We provide MetaTrader 5 (MT5) access for all challenge and funded accounts. You'll receive your login 
                  credentials within 24 hours of purchase. Our web-based dashboard provides additional analytics and account management.
                </p>
              </div>

              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-white mb-3">How often can I withdraw profits?</h3>
                <p className="text-white/70 text-sm">
                  Funded traders can request profit withdrawals bi-weekly (every 14 days). Withdrawals are processed within 
                  1-3 business days via bank transfer, cryptocurrency, or other supported payment methods.
                </p>
              </div>

              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-white mb-3">Is there ongoing support during challenges?</h3>
                <p className="text-white/70 text-sm">
                  Absolutely! Our support team is available 24/7 via live chat, email, or phone. We also provide educational 
                  resources, trading guides, and regular webinars to help you succeed in your challenge.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="text-center">
          <div className="glass-card p-12">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Start Your Challenge?</h2>
            <p className="text-xl text-white/70 mb-8 max-w-2xl mx-auto">
              Join thousands of traders who have successfully completed our challenges and are now trading with our capital.
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
              <Link href="/auth/register">
                <button className="professional-button-dark flex items-center space-x-2 px-8 py-4 text-lg">
                  <span>Start Your Journey</span>
                  <ArrowRight className="h-5 w-5" />
                </button>
              </Link>
              <Link href="/learn-more">
                <button className="professional-button px-8 py-4 text-lg">
                  Learn More
                </button>
              </Link>
            </div>

            <div className="mt-8 text-center">
              <p className="text-white/50 text-sm">
                30-day money-back guarantee • No hidden fees • Start trading within 24 hours
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
} 
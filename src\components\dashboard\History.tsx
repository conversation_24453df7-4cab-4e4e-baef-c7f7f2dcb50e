'use client'

import { useState } from 'react'
import { 
  Download, 
  Search,
  TrendingUp,
  Clock,
  BarChart3,
  Eye
} from 'lucide-react'

interface HistoricalTrade {
  id: string
  symbol: string
  type: 'BUY' | 'SELL'
  volume: number
  openPrice: number
  closePrice: number
  openTime: string
  closeTime: string
  duration: string
  pnl: number
  pnlPercent: number
  commission: number
  swap: number
  status: 'completed' | 'closed'
}

const History = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('1M')
  const [filterType, setFilterType] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')

  const trades: HistoricalTrade[] = [
    {
      id: '1',
      symbol: 'EUR/USD',
      type: 'BUY',
      volume: 0.1,
      openPrice: 1.0823,
      closePrice: 1.0862,
      openTime: '2024-01-15 09:30:15',
      closeTime: '2024-01-15 14:45:22',
      duration: '5h 15m',
      pnl: 390.00,
      pnlPercent: 3.61,
      commission: 3.50,
      swap: -1.20,
      status: 'completed'
    },
    {
      id: '2',
      symbol: 'GBP/USD',
      type: 'SELL',
      volume: 0.15,
      openPrice: 1.2650,
      closePrice: 1.2621,
      openTime: '2024-01-14 11:20:30',
      closeTime: '2024-01-14 16:35:18',
      duration: '5h 14m',
      pnl: 435.00,
      pnlPercent: 2.29,
      commission: 5.25,
      swap: 0.80,
      status: 'completed'
    },
    {
      id: '3',
      symbol: 'USD/JPY',
      type: 'BUY',
      volume: 0.08,
      openPrice: 149.82,
      closePrice: 148.95,
      openTime: '2024-01-13 14:15:45',
      closeTime: '2024-01-13 20:30:12',
      duration: '6h 14m',
      pnl: -696.00,
      pnlPercent: -5.82,
      commission: 2.80,
      swap: -2.40,
      status: 'completed'
    },
    {
      id: '4',
      symbol: 'GOLD',
      type: 'BUY',
      volume: 0.02,
      openPrice: 2015.30,
      closePrice: 2031.45,
      openTime: '2024-01-12 08:45:20',
      closeTime: '2024-01-12 18:20:35',
      duration: '9h 35m',
      pnl: 323.00,
      pnlPercent: 0.80,
      commission: 4.00,
      swap: 0.00,
      status: 'completed'
    },
    {
      id: '5',
      symbol: 'BTC/USD',
      type: 'BUY',
      volume: 0.001,
      openPrice: 41200.00,
      closePrice: 43247.00,
      openTime: '2024-01-11 16:30:10',
      closeTime: '2024-01-12 10:15:45',
      duration: '17h 45m',
      pnl: 2047.00,
      pnlPercent: 4.97,
      commission: 10.00,
      swap: 0.00,
      status: 'completed'
    }
  ]

  const performanceSummary = {
    totalTrades: 47,
    winningTrades: 36,
    losingTrades: 11,
    winRate: 76.6,
    totalPnL: 12450.80,
    totalPnLPercent: 23.4,
    avgWin: 485.50,
    avgLoss: -345.20,
    profitFactor: 1.84,
    maxConsecutiveWins: 8,
    maxConsecutiveLosses: 3
  }

  const filteredTrades = trades.filter(trade => {
    const matchesType = filterType === 'all' || trade.type === filterType
    const matchesSearch = trade.symbol.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesType && matchesSearch
  })

  return (
    <div className="space-y-6">
      {/* Performance Summary */}
      <div className="glass-card p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-primary-content">Trading History</h2>
            <p className="text-secondary-content">Complete trading performance and analytics</p>
          </div>
          <div className="flex items-center space-x-2">
            {['1W', '1M', '3M', '6M', '1Y', 'ALL'].map((period) => (
              <button
                key={period}
                onClick={() => setSelectedPeriod(period)}
                className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                  selectedPeriod === period 
                    ? 'bg-green-500 text-white' 
                    : 'text-secondary-content hover:text-primary-content bg-slate-800'
                }`}
              >
                {period}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-gradient-to-br from-green-500/20 to-green-600/20 rounded-xl p-6 border border-green-500/30">
            <div className="flex items-center justify-between mb-4">
              <TrendingUp className="h-8 w-8 text-green-400" />
              <span className="text-xs text-green-400 bg-green-400/10 px-2 py-1 rounded">PROFIT</span>
            </div>
            <div className="text-3xl font-bold text-green-400 mb-1">
              +${performanceSummary.totalPnL.toLocaleString()}
            </div>
            <div className="text-sm text-secondary-content">
              Total P&L (+{performanceSummary.totalPnLPercent}%)
            </div>
          </div>

          <div className="bg-slate-800/60 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <BarChart3 className="h-8 w-8 text-blue-400" />
              <span className="text-xs text-blue-400 bg-blue-400/10 px-2 py-1 rounded">TRADES</span>
            </div>
            <div className="text-2xl font-bold text-primary-content mb-1">
              {performanceSummary.totalTrades}
            </div>
            <div className="text-sm text-secondary-content">
              Total Trades
            </div>
          </div>

          <div className="bg-slate-800/60 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <TrendingUp className="h-8 w-8 text-green-400" />
              <span className="text-xs text-green-400 bg-green-400/10 px-2 py-1 rounded">WIN RATE</span>
            </div>
            <div className="text-2xl font-bold text-green-400 mb-1">
              {performanceSummary.winRate}%
            </div>
            <div className="text-sm text-secondary-content">
              {performanceSummary.winningTrades}W / {performanceSummary.losingTrades}L
            </div>
          </div>

          <div className="bg-slate-800/60 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <BarChart3 className="h-8 w-8 text-yellow-400" />
              <span className="text-xs text-yellow-400 bg-yellow-400/10 px-2 py-1 rounded">FACTOR</span>
            </div>
            <div className="text-2xl font-bold text-yellow-400 mb-1">
              {performanceSummary.profitFactor}
            </div>
            <div className="text-sm text-secondary-content">
              Profit Factor
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 glass-card p-6">
          <h3 className="text-xl font-bold text-primary-content mb-6">Performance Metrics</h3>
          <div className="grid grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="bg-slate-800/60 rounded-lg p-4">
                <div className="text-sm text-secondary-content mb-1">Average Win</div>
                <div className="text-xl font-bold text-green-400">
                  +${performanceSummary.avgWin.toLocaleString()}
                </div>
              </div>
              <div className="bg-slate-800/60 rounded-lg p-4">
                <div className="text-sm text-secondary-content mb-1">Average Loss</div>
                <div className="text-xl font-bold text-red-400">
                  ${performanceSummary.avgLoss.toLocaleString()}
                </div>
              </div>
              <div className="bg-slate-800/60 rounded-lg p-4">
                <div className="text-sm text-secondary-content mb-1">Max Consecutive Wins</div>
                <div className="text-xl font-bold text-green-400">
                  {performanceSummary.maxConsecutiveWins}
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div className="bg-slate-800/60 rounded-lg p-4">
                <div className="text-sm text-secondary-content mb-1">Winning Trades</div>
                <div className="text-xl font-bold text-green-400">
                  {performanceSummary.winningTrades}
                </div>
              </div>
              <div className="bg-slate-800/60 rounded-lg p-4">
                <div className="text-sm text-secondary-content mb-1">Losing Trades</div>
                <div className="text-xl font-bold text-red-400">
                  {performanceSummary.losingTrades}
                </div>
              </div>
              <div className="bg-slate-800/60 rounded-lg p-4">
                <div className="text-sm text-secondary-content mb-1">Max Consecutive Losses</div>
                <div className="text-xl font-bold text-red-400">
                  {performanceSummary.maxConsecutiveLosses}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="glass-card p-6">
          <h3 className="text-xl font-bold text-primary-content mb-6">Monthly Progress</h3>
          <div className="h-48 bg-slate-900/60 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-secondary-content mx-auto mb-3" />
              <p className="text-secondary-content">Performance Chart</p>
              <p className="text-xs text-secondary-content/70">Monthly analysis coming soon</p>
            </div>
          </div>
        </div>
      </div>

      {/* Trade History Table */}
      <div className="glass-card p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-primary-content">Trade History</h3>
          <div className="flex items-center space-x-3">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-secondary-content" />
              <input
                type="text"
                placeholder="Search symbols..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 bg-slate-800 text-primary-content rounded-lg text-sm border border-slate-600 focus:border-green-500 focus:outline-none w-48"
              />
            </div>
            
            {/* Filter */}
            <select 
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="bg-slate-800 text-primary-content px-3 py-2 rounded-lg text-sm border border-slate-600 focus:border-green-500 focus:outline-none"
            >
              <option value="all">All Types</option>
              <option value="BUY">Buy Orders</option>
              <option value="SELL">Sell Orders</option>
            </select>
            
            {/* Export */}
            <button className="glass-card p-2 hover:bg-slate-700 transition-colors">
              <Download className="h-5 w-5 text-secondary-content" />
            </button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-slate-700">
                <th className="text-left py-3 px-4 text-secondary-content text-sm font-semibold">Symbol</th>
                <th className="text-left py-3 px-4 text-secondary-content text-sm font-semibold">Type</th>
                <th className="text-left py-3 px-4 text-secondary-content text-sm font-semibold">Volume</th>
                <th className="text-left py-3 px-4 text-secondary-content text-sm font-semibold">Open Price</th>
                <th className="text-left py-3 px-4 text-secondary-content text-sm font-semibold">Close Price</th>
                <th className="text-left py-3 px-4 text-secondary-content text-sm font-semibold">Duration</th>
                <th className="text-left py-3 px-4 text-secondary-content text-sm font-semibold">P&L</th>
                <th className="text-left py-3 px-4 text-secondary-content text-sm font-semibold">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredTrades.map((trade) => (
                <tr key={trade.id} className="border-b border-slate-800 hover:bg-slate-800/30 transition-colors">
                  <td className="py-4 px-4">
                    <div className="font-mono font-bold text-primary-content">{trade.symbol}</div>
                  </td>
                  <td className="py-4 px-4">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      trade.type === 'BUY' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                    }`}>
                      {trade.type}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <span className="text-primary-content font-mono">{trade.volume}</span>
                  </td>
                  <td className="py-4 px-4">
                    <span className="text-primary-content font-mono">{trade.openPrice}</span>
                  </td>
                  <td className="py-4 px-4">
                    <span className="text-primary-content font-mono">{trade.closePrice}</span>
                  </td>
                  <td className="py-4 px-4">
                    <div className="flex items-center space-x-1 text-secondary-content">
                      <Clock className="h-3 w-3" />
                      <span className="text-sm">{trade.duration}</span>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className={`font-bold ${trade.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {trade.pnl >= 0 ? '+' : ''}${trade.pnl.toFixed(2)}
                    </div>
                    <div className={`text-xs ${trade.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      ({trade.pnlPercent >= 0 ? '+' : ''}{trade.pnlPercent.toFixed(2)}%)
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <button className="p-1 rounded hover:bg-slate-700 transition-colors">
                      <Eye className="h-4 w-4 text-secondary-content" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default History 
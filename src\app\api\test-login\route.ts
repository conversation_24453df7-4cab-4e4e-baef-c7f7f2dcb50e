import { NextRequest, NextResponse } from 'next/server'
import { createAdminClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()
    
    const supabaseAdmin = createAdminClient()
    
    // Test authentication
    const { data, error } = await supabaseAdmin.auth.signInWithPassword({
      email,
      password
    })
    
    if (error) {
      return NextResponse.json({
        success: false,
        error: error.message,
        details: error
      })
    }
    
    if (data.user) {
      // Check if user exists in users table
      const { data: userProfile, error: profileError } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('id', data.user.id)
        .single()
      
      return NextResponse.json({
        success: true,
        message: 'Authentication successful',
        data: {
          user_id: data.user.id,
          email: data.user.email,
          email_confirmed: data.user.email_confirmed_at,
          profile_exists: !profileError,
          profile: userProfile
        }
      })
    }
    
    return NextResponse.json({
      success: false,
      error: 'No user data returned'
    })
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
    }, { status: 500 })
  }
} 
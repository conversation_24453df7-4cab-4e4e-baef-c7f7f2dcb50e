# PropFirm Platform - Environment Variables Template
# Copy this file to .env.local and fill in your actual values

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
SUPABASE_JWT_SECRET=your-supabase-jwt-secret

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# Application Configuration
NEXTAUTH_SECRET=your-nextauth-secret-key
NEXTAUTH_URL=http://localhost:3000
FRONTEND_URL=http://localhost:3000
NODE_ENV=development

# Backend Server Configuration
API_PORT=8000
JWT_SECRET=your-jwt-secret-key
ENCRYPTION_KEY=your-encryption-key

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
FROM_EMAIL=<EMAIL>

# MT5 Integration (configure when ready)
MT5_SERVER_URL=your-mt5-server-url
MT5_API_KEY=your-mt5-api-key
MT5_LOGIN=your-mt5-login
MT5_PASSWORD=your-mt5-password

# Database Direct URL (for migrations if needed)
DATABASE_URL=postgresql://user:password@host:port/database

# Security Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=5

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log 
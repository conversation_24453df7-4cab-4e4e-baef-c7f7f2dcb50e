import React from 'react'

interface EliteTextProps {
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  variant?: 'gold' | 'platinum' | 'emerald'
}

export function EliteText({ children, size = 'md', className = '', variant = 'gold' }: EliteTextProps) {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-2xl',
    xl: 'text-3xl'
  }

  const variantStyles = {
    gold: {
      gradient: 'from-amber-400 via-yellow-500 to-amber-600',
      glow: 'rgba(251, 191, 36, 0.4)',
      shadow: 'rgba(251, 191, 36, 0.3)'
    },
    platinum: {
      gradient: 'from-slate-300 via-slate-100 to-slate-400',
      glow: 'rgba(148, 163, 184, 0.4)',
      shadow: 'rgba(148, 163, 184, 0.3)'
    },
    emerald: {
      gradient: 'from-emerald-400 via-green-500 to-emerald-600',
      glow: 'rgba(16, 185, 129, 0.4)',
      shadow: 'rgba(16, 185, 129, 0.3)'
    }
  }

  const currentVariant = variantStyles[variant]

  return (
    <span 
      className={`
        ${sizeClasses[size]} 
        ${className}
        font-extrabold
        tracking-[0.2em]
        text-transparent
        bg-clip-text
        bg-gradient-to-r
        ${currentVariant.gradient}
        relative
        select-none
        transition-all
        duration-500
        hover:scale-[1.02]
        transform
        uppercase
        leading-none
      `}
      style={{
        fontFamily: '"Orbitron", "Exo 2", "Rajdhani", sans-serif',
        fontWeight: '900',
        letterSpacing: '0.25em',
        textShadow: `
          0 0 20px ${currentVariant.glow},
          0 0 40px ${currentVariant.shadow},
          1px 1px 0px rgba(0, 0, 0, 0.9),
          2px 2px 0px rgba(0, 0, 0, 0.7),
          3px 3px 0px rgba(0, 0, 0, 0.5),
          4px 4px 0px rgba(0, 0, 0, 0.3)
        `,
        WebkitTextStroke: '1px rgba(255, 255, 255, 0.1)',
        filter: `drop-shadow(0 0 10px ${currentVariant.glow})`
      }}
    >
      {children}
      
      {/* Professional underline */}
      <span 
        className={`
          absolute -bottom-1 left-0 right-0 h-px 
          bg-gradient-to-r from-transparent via-current to-transparent 
          opacity-70
        `}
        style={{
          background: `linear-gradient(90deg, transparent 0%, ${currentVariant.glow} 50%, transparent 100%)`
        }}
      />
      
      {/* Subtle backdrop glow */}
      <span 
        className="absolute inset-0 opacity-30 blur-md -z-10"
        style={{
          background: `linear-gradient(90deg, ${currentVariant.glow}, ${currentVariant.shadow})`,
          WebkitBackgroundClip: 'text',
          backgroundClip: 'text',
          filter: 'blur(12px)'
        }}
      >
        {children}
      </span>
    </span>
  )
}

export default EliteText 
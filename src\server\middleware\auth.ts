import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import { supabase } from '../../lib/supabase/server'

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string
    email: string
    role: string
    accountId?: string
  }
}

interface JWTPayload {
  sub: string
  email: string
  role: string
  accountId?: string
  iat: number
  exp: number
}

export const authMiddleware = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Get token from Authorization header or cookies
    const authHeader = req.headers.authorization
    const token = authHeader?.startsWith('Bearer ') 
      ? authHeader.substring(7)
      : req.cookies?.access_token

    if (!token) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_001',
          message: 'Access token is required',
          timestamp: new Date().toISOString()
        }
      })
      return
    }

    // Verify JWT token
    const JWT_SECRET = process.env.JWT_SECRET || process.env.SUPABASE_JWT_SECRET
    if (!JWT_SECRET) {
      throw new Error('JWT secret not configured')
    }

    try {
      jwt.verify(token, JWT_SECRET) as JWTPayload
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        res.status(401).json({
          success: false,
          error: {
            code: 'AUTH_002',
            message: 'Token has expired',
            timestamp: new Date().toISOString()
          }
        })
        return
      }
      
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_001',
          message: 'Invalid token',
          timestamp: new Date().toISOString()
        }
      })
      return
    }

    // Verify user exists in Supabase
    const { data: user, error } = await supabase.auth.getUser(token)
    
    if (error || !user.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_001',
          message: 'Invalid or expired token',
          timestamp: new Date().toISOString()
        }
      })
      return
    }

    // Get user profile and trading account
    const { data: profile } = await supabase
      .from('users')
      .select(`
        id,
        email,
        first_name,
        last_name,
        account_status,
        trading_accounts (
          id,
          status
        )
      `)
      .eq('id', user.user.id)
      .single()

    if (!profile) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_001',
          message: 'User profile not found',
          timestamp: new Date().toISOString()
        }
      })
      return
    }

    // Check if account is active
    if (profile.account_status !== 'active') {
      res.status(403).json({
        success: false,
        error: {
          code: 'ACC_001',
          message: 'Account is suspended or inactive',
          timestamp: new Date().toISOString()
        }
      })
      return
    }

    // Attach user to request
    req.user = {
      id: profile.id,
      email: profile.email,
      role: 'trader', // Default role since we don't have a role column
      accountId: profile.trading_accounts?.[0]?.id
    }

    next()
  } catch (error) {
    console.error('Auth middleware error:', error)
    res.status(500).json({
      success: false,
      error: {
        code: 'SYS_001',
        message: 'Internal authentication error',
        timestamp: new Date().toISOString()
      }
    })
  }
}

// Middleware for specific roles
export const requireRole = (roles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_001',
          message: 'Authentication required',
          timestamp: new Date().toISOString()
        }
      })
      return
    }

    if (!roles.includes(req.user.role)) {
      res.status(403).json({
        success: false,
        error: {
          code: 'AUTH_003',
          message: 'Insufficient permissions',
          timestamp: new Date().toISOString()
        }
      })
      return
    }

    next()
  }
}

// Middleware for API key authentication
export const apiKeyAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const apiKey = req.headers['x-api-key'] as string

    if (!apiKey) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_001',
          message: 'API key is required',
          timestamp: new Date().toISOString()
        }
      })
      return
    }

    // Verify API key in database
    const { data: apiKeyData, error } = await supabase
      .from('api_keys')
      .select(`
        id,
        user_id,
        permissions,
        expires_at,
        is_active,
        users!inner (
          id,
          email,
          role,
          account_status
        )
      `)
      .eq('key_hash', apiKey)
      .single()

    if (error || !apiKeyData || !apiKeyData.is_active) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_001',
          message: 'Invalid API key',
          timestamp: new Date().toISOString()
        }
      })
      return
    }

    // Check if API key is expired
    if (apiKeyData.expires_at && new Date(apiKeyData.expires_at) < new Date()) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_002',
          message: 'API key has expired',
          timestamp: new Date().toISOString()
        }
      })
      return
    }

    // Attach user to request
    req.user = {
      id: (apiKeyData.users as any).id,
      email: (apiKeyData.users as any).email,
      role: (apiKeyData.users as any).role || 'trader'
    }

    next()
  } catch (error) {
    console.error('API key auth error:', error)
    res.status(500).json({
      success: false,
      error: {
        code: 'SYS_001',
        message: 'Internal authentication error',
        timestamp: new Date().toISOString()
      }
    })
  }
} 
import React from 'react'

interface DottedTextProps {
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}

export function DottedText({ children, size = 'md', className = '' }: DottedTextProps) {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-2xl',
    xl: 'text-3xl'
  }

  return (
    <span 
      className={`
        ${sizeClasses[size]} 
        ${className}
        font-bold
        tracking-wider
        text-transparent
        bg-clip-text
        bg-gradient-to-r
        from-yellow-400
        via-amber-300
        to-yellow-500
        drop-shadow-[0_0_8px_rgba(251,191,36,0.3)]
        relative
        select-none
        transition-all
        duration-300
        hover:from-yellow-300
        hover:via-amber-200
        hover:to-yellow-400
        hover:drop-shadow-[0_0_12px_rgba(251,191,36,0.5)]
        hover:scale-105
        transform
        uppercase
        font-black
        text-shadow-lg
      `}
      style={{
        fontFamily: '"Orbitron", "Exo 2", "Raj<PERSON>ni", "Arial Black", sans-serif',
        letterSpacing: '0.15em',
        textShadow: `
          0 0 10px rgba(251, 191, 36, 0.4),
          0 0 20px rgba(251, 191, 36, 0.2),
          0 0 30px rgba(251, 191, 36, 0.1),
          1px 1px 0px rgba(0, 0, 0, 0.8),
          2px 2px 0px rgba(0, 0, 0, 0.6),
          3px 3px 0px rgba(0, 0, 0, 0.4)
        `,
        WebkitTextStroke: '0.5px rgba(255, 255, 255, 0.1)'
      }}
    >
      {children}
      
      {/* Animated underline accent */}
      <span 
        className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-yellow-400 to-transparent opacity-60 animate-pulse"
        style={{
          background: 'linear-gradient(90deg, transparent 0%, #fbbf24 20%, #f59e0b 50%, #fbbf24 80%, transparent 100%)'
        }}
      />
      
      {/* Subtle glow effect */}
      <span 
        className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 via-amber-300/20 to-yellow-500/20 blur-sm -z-10 opacity-50"
        style={{ filter: 'blur(8px)' }}
      >
        {children}
      </span>
    </span>
  )
}

export default DottedText 
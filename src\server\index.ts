// Load environment variables first
import dotenv from 'dotenv'
import path from 'path'

// Load .env.local file explicitly
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') })
// Also load .env as fallback
dotenv.config()

console.log('🔧 Environment Loading:')
console.log('CWD:', process.cwd())
console.log('ENV FILE:', path.resolve(process.cwd(), '.env.local'))
console.log('NEXT_PUBLIC_SUPABASE_URL loaded:', !!process.env.NEXT_PUBLIC_SUPABASE_URL)

import express from 'express'
import http from 'http'
import { Server as SocketIOServer } from 'socket.io'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import cookieParser from 'cookie-parser'
import { initializeWebSocket } from './websocket/socketManager'

const app = express()
const server = http.createServer(app)

// Initialize Socket.IO
const io = new SocketIOServer(server, {
  cors: {
    origin: [
      process.env.FRONTEND_URL || "http://localhost:3000",
      "http://localhost:3001"
    ],
    credentials: true,
    methods: ['GET', 'POST']
  }
})

// Initialize WebSocket handlers
initializeWebSocket(io)

// Basic security middleware
app.use(helmet({
  contentSecurityPolicy: false,
  crossOriginEmbedderPolicy: false
}))

// CORS configuration
app.use(cors({
  origin: [
    process.env.FRONTEND_URL || "http://localhost:3000",
    "http://localhost:3001"
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}))

// Basic middleware
app.use(compression())
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))
app.use(cookieParser())

// Simple health check
app.get('/health', (_req, res) => {
  res.json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0'
    }
  })
})

// Test database connection
app.get('/test-db', async (_req, res) => {
  try {
    const { supabase } = await import('../lib/supabase/server')
    
    // Test a simple query
    const { data, error } = await supabase
      .from('challenges')
      .select('id, name')
      .limit(1)

    if (error) {
      throw error
    }

    res.json({
      success: true,
      data: {
        message: 'Database connection successful',
        sample_data: data || [],
        tables_accessible: true
      }
    })
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        message: 'Database connection failed',
        details: error.message,
        code: error.code
      }
    })
  }
})

// Simple API routes (without complex middleware for now)
app.get('/api/test', (_req, res) => {
  res.json({ 
    success: true, 
    message: 'API is working',
    timestamp: new Date().toISOString()
  })
})

// Analytics endpoint (simple version)
app.get('/api/analytics/test', (_req, res) => {
  res.json({
    success: true,
    data: {
      message: 'Analytics API endpoint working',
      sampleData: {
        totalTrades: 150,
        winRate: 68.5,
        profit: 2847.50,
        drawdown: 5.2
      }
    }
  })
})

// Risk management endpoint (simple version)
app.get('/api/risk/test', (_req, res) => {
  res.json({
    success: true,
    data: {
      message: 'Risk management API endpoint working',
      currentRisk: {
        dailyDrawdown: 2.1,
        maxDailyDrawdown: 5.0,
        totalDrawdown: 3.5,
        maxTotalDrawdown: 10.0,
        riskScore: 3.2
      }
    }
  })
})

// User settings endpoint (simple version)
app.get('/api/users/test', (_req, res) => {
  res.json({
    success: true,
    data: {
      message: 'User settings API endpoint working',
      userPreferences: {
        theme: 'dark',
        notifications: true,
        defaultLotSize: 0.1
      }
    }
  })
})

// Support endpoint (simple version)
app.get('/api/support/test', (_req, res) => {
  res.json({
    success: true,
    data: {
      message: 'Support API endpoint working',
      supportInfo: {
        ticketsOpen: 2,
        avgResponseTime: '4 hours',
        status: 'online'
      }
    }
  })
})

// CORS middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}))

// Simple connectivity test endpoint
app.get('/api/connectivity-test', (req, res) => {
  res.json({
    success: true,
    message: 'Frontend-Backend connectivity working!',
    timestamp: new Date().toISOString(),
    headers: req.headers,
    method: req.method,
    url: req.url
  })
})

// === DEVELOPMENT API ENDPOINTS ===
// These endpoints provide data for development without requiring authentication

// Analytics Performance Endpoint
app.get('/api/analytics/performance/:accountId', (req, res) => {
  const timeframe = req.query.timeframe || '7D'
  res.json({
    success: true,
    data: {
      totalTrades: 150,
      winningTrades: 103,
      losingTrades: 47,
      winRate: 68.67,
      avgWin: 125.50,
      avgLoss: 89.30,
      profitFactor: 1.41,
      sharpeRatio: 0.85,
      maxDrawdown: 8.2,
      returnOnInvestment: 24.7,
      totalProfit: 2847.50,
      period: {
        start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        end: new Date().toISOString()
      }
    }
  })
})

// Analytics Equity Curve Endpoint
app.get('/api/analytics/equity-curve/:accountId', (req, res) => {
  const timeframe = req.query.timeframe || '7D'
  const days = timeframe === '1D' ? 1 : timeframe === '7D' ? 7 : timeframe === '30D' ? 30 : 90
  
  // Generate equity curve data points
  const dataPoints = []
  const baseBalance = 125000
  let currentEquity = baseBalance
  
  for (let i = days; i >= 0; i--) {
    const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
    
    // Simulate realistic equity curve with some volatility
    const dailyChange = (Math.random() - 0.48) * 1000 // Slight upward bias
    currentEquity += dailyChange
    
    dataPoints.push({
      timestamp: date.toISOString(),
      value: Math.round(currentEquity * 100) / 100,
      balance: Math.round(currentEquity * 100) / 100,
      equity: Math.round(currentEquity * 100) / 100
    })
  }
  
  res.json({
    success: true,
    data: {
      data: dataPoints,
      totalReturn: ((currentEquity - baseBalance) / baseBalance * 100).toFixed(2),
      maxDrawdown: 8.2,
      volatility: 12.5
    }
  })
})

// Analytics Daily P&L Endpoint  
app.get('/api/analytics/daily-pnl/:accountId', (req, res) => {
  const timeframe = req.query.timeframe || '7D'
  const days = timeframe === '1D' ? 1 : timeframe === '7D' ? 7 : timeframe === '30D' ? 30 : 90
  
  const dailyData = []
  for (let i = days; i >= 1; i--) {
    const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
    const profit = (Math.random() - 0.45) * 2000 // Slight upward bias
    const trades = Math.floor(Math.random() * 20) + 1
    const winRate = Math.random() * 40 + 50 // 50-90%
    
    dailyData.push({
      date: date.toISOString().split('T')[0],
      profit: Math.round(profit * 100) / 100,
      trades,
      winRate: Math.round(winRate * 100) / 100
    })
  }
  
  res.json({
    success: true,
    data: {
      data: dailyData,
      averageDailyReturn: 125.50,
      bestDay: { date: '2024-12-01', profit: 1845.50 },
      worstDay: { date: '2024-11-28', profit: -892.30 }
    }
  })
})

// Risk Management Current Metrics
app.get('/api/risk/current-metrics/:accountId', (req, res) => {
  res.json({
    success: true,
    data: {
      dailyDrawdown: 2.5,
      maxDailyDrawdownLimit: 5.0,
      totalDrawdown: 8.2,
      maxTotalDrawdownLimit: 10.0,
      positionSize: 0.5,
      maxPositionSizeLimit: 2.0,
      openPositions: 3,
      maxPositionsLimit: 10,
      marginLevel: 250.0,
      freeMargin: 5000.0,
      equity: 10250.0,
      balance: 10000.0,
      riskScore: 3.2,
      lastUpdate: new Date().toISOString()
    }
  })
})

// User Profile Endpoint
app.get('/api/users/profile', (req, res) => {
  res.json({
    success: true,
    data: {
      id: 'demo-user-123',
      email: '<EMAIL>',
      firstName: 'Demo',
      lastName: 'Trader',
      phone: '+****************',
      country: 'United States',
      timezone: 'America/New_York',
      language: 'English',
      dateOfBirth: '1990-01-01',
      kycStatus: 'verified',
      accountStatus: 'active',
      createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date().toISOString()
    }
  })
})

// Support FAQ Endpoint
app.get('/api/support/faq', (req, res) => {
  const category = (req.query.category as string) || 'all'
  const search = (req.query.search as string) || ''
  
  const faqs = [
    {
      id: '1',
      question: 'How do I start trading?',
      answer: 'First, complete your profile setup and choose a challenge. Then fund your account and start trading.',
      category: 'getting-started',
      helpful: 25,
      notHelpful: 2,
      tags: ['trading', 'setup'],
      lastUpdated: new Date().toISOString()
    },
    {
      id: '2', 
      question: 'What are the risk management rules?',
      answer: 'Daily drawdown limit is 5%, total drawdown limit is 10%. Maximum position size is 2% per trade.',
      category: 'risk-management',
      helpful: 18,
      notHelpful: 1,
      tags: ['risk', 'rules'],
      lastUpdated: new Date().toISOString()
    },
    {
      id: '3',
      question: 'How do withdrawals work?',
      answer: 'You can request withdrawals after 30 days of consistent trading. Minimum withdrawal is $100.',
      category: 'payments',
      helpful: 32,
      notHelpful: 3,
      tags: ['withdrawal', 'payments'],
      lastUpdated: new Date().toISOString()
    }
  ]

  const filtered = category === 'all' ? faqs : faqs.filter(faq => faq.category === category)
  const searched = search ? filtered.filter(faq => 
    faq.question.toLowerCase().includes(search.toLowerCase()) ||
    faq.answer.toLowerCase().includes(search.toLowerCase())
  ) : filtered

  res.json({
    success: true,
    data: {
      faqs: searched,
      total: searched.length,
      categories: ['all', 'getting-started', 'risk-management', 'payments', 'technical']
    }
  })
})

// Handle 404 errors
app.use('*', (_req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Route ${_req.originalUrl} not found`,
      timestamp: new Date().toISOString()
    }
  })
})

const PORT = process.env.API_PORT || 8000

// Start server
if (require.main === module) {
  server.listen(PORT, () => {
    console.log(`🚀 Prop Firm API Server running on port ${PORT}`)
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`)
    console.log(`🔗 Health check: http://localhost:${PORT}/health`)
    console.log(`🔗 Database test: http://localhost:${PORT}/test-db`)
    console.log(`🧪 API Test: http://localhost:${PORT}/api/test`)
    console.log(`📊 Analytics Test: http://localhost:${PORT}/api/analytics/test`)
    console.log(`🛡️ Risk Test: http://localhost:${PORT}/api/risk/test`)
    console.log(`👤 Users Test: http://localhost:${PORT}/api/users/test`)
    console.log(`❓ Support Test: http://localhost:${PORT}/api/support/test`)
    console.log('🎉 Server ready for frontend connection!')
  })
}

export { app, server, io }

/*
// TEMPORARILY COMMENTED OUT TO ISOLATE ISSUE
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import compression from 'compression'
import cookieParser from 'cookie-parser'
import rateLimit from 'express-rate-limit'
import { Server as SocketIOServer } from 'socket.io'

// Import middleware
// import { authMiddleware } from './middleware/auth' // Temporarily commented out
import { errorHandler } from './middleware/errorHandler'
*/ 
// Minimal test server
const express = require('express')
const cors = require('cors')

const app = express()
const PORT = 8001

app.use(cors())
app.use(express.json())

app.get('/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      message: 'Test server is working!'
    }
  })
})

app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'API endpoint working',
    timestamp: new Date().toISOString()
  })
})

app.listen(PORT, () => {
  console.log(`🚀 Test server running on http://localhost:${PORT}`)
  console.log(`🔗 Health check: http://localhost:${PORT}/health`)
  console.log(`🧪 API test: http://localhost:${PORT}/api/test`)
}) 
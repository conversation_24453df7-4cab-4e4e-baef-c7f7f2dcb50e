import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value
          },
          set() {
            // Not needed for read-only operations
          },
          remove() {
            // Not needed for read-only operations
          },
        },
      }
    )

    // Test 1: Check if we can connect to Supabase
    const { error: connectionError } = await supabase
      .from('challenges')
      .select('count')
      .limit(1)

    if (connectionError) {
      return NextResponse.json({ 
        success: false, 
        error: 'Database connection failed',
        details: connectionError.message 
      }, { status: 500 })
    }

    // Test 2: Check if users table exists
    const { error: usersError } = await supabase
      .from('users')
      .select('count')
      .limit(1)

    if (usersError) {
      return NextResponse.json({ 
        success: false, 
        error: 'Users table not found - Schema not applied',
        details: usersError.message,
        instruction: 'Please apply the database schema from supabase/schema.sql to your Supabase project'
      }, { status: 500 })
    }

    // Test 3: Check if challenges table has sample data
    const { data: challengesData } = await supabase
      .from('challenges')
      .select('*')
      .limit(5)

    return NextResponse.json({ 
      success: true, 
      message: 'Database connection successful',
      data: {
        connection: 'OK',
        usersTable: 'OK',
        challengesCount: challengesData?.length || 0,
        sampleChallenges: challengesData || []
      }
    })
  } catch (error) {
    return NextResponse.json({ 
      success: false, 
      error: 'Unexpected error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 
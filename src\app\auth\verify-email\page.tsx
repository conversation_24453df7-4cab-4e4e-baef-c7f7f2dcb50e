import { Metadata } from 'next'
import Link from 'next/link'
import { Mail, CheckCircle, ArrowLeft, RefreshCw } from 'lucide-react'
import { Logo } from '@/components/ui/Logo'
import { EliteText } from '@/components/ui/EliteText'

export const metadata: Metadata = {
  title: 'Verify Your Email - Prop Bully',
  description: 'Verify your email address to complete your Prop Bully registration',
}

export default function VerifyEmailPage() {
  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Professional Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black">
        <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 via-transparent to-blue-500/5 animate-pulse" />
      </div>

      {/* Floating Background Elements */}
      <div className="absolute top-20 left-20 w-32 h-32 bg-green-500/10 rounded-full blur-xl animate-pulse" />
      <div className="absolute bottom-20 right-20 w-48 h-48 bg-blue-500/10 rounded-full blur-xl animate-pulse delay-1000" />
      <div className="absolute top-1/2 left-10 w-24 h-24 bg-yellow-500/10 rounded-full blur-xl animate-pulse delay-500" />

      {/* Main Content */}
      <div className="relative z-10 w-full max-w-md mx-auto p-6">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-3 mb-6">
            <Logo size="md" imageSrc="/images/logos/prop-bully-logo.png" alt="Prop Bully Logo" />
            <EliteText size="xl" variant="gold">Prop Bully</EliteText>
          </div>
        </div>

        {/* Verification Card */}
        <div className="glass-card p-8 neon-border text-center">
          {/* Success Icon */}
          <div className="flex justify-center mb-6">
            <div className="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center">
              <Mail className="h-10 w-10 text-green-400" />
            </div>
          </div>

          <h1 className="text-2xl font-bold text-white mb-4">
            Check Your Email
          </h1>
          
          <p className="text-white/70 mb-6 leading-relaxed">
            We've sent a verification link to your email address. Please click the link in the email to verify your account and complete your registration.
          </p>

          {/* Action Steps */}
          <div className="space-y-4 mb-8">
            <div className="flex items-center space-x-3 text-left">
              <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-green-400 text-sm font-bold">1</span>
              </div>
              <span className="text-white/80 text-sm">Check your email inbox</span>
            </div>
            
            <div className="flex items-center space-x-3 text-left">
              <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-green-400 text-sm font-bold">2</span>
              </div>
              <span className="text-white/80 text-sm">Click the verification link</span>
            </div>
            
            <div className="flex items-center space-x-3 text-left">
              <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                <CheckCircle className="h-4 w-4 text-green-400" />
              </div>
              <span className="text-white/80 text-sm">Start your trading journey</span>
            </div>
          </div>

          {/* Resend Email */}
          <div className="space-y-4">
            <button className="w-full professional-button-dark flex items-center justify-center space-x-2">
              <RefreshCw className="h-4 w-4" />
              <span>Resend Verification Email</span>
            </button>
            
            <p className="text-white/50 text-xs">
              Didn't receive the email? Check your spam folder or try resending.
            </p>
          </div>
        </div>

        {/* Additional Actions */}
        <div className="mt-8 space-y-4 text-center">
          <p className="text-white/60 text-sm">
            Wrong email address?{' '}
            <Link href="/auth/register" className="text-green-400 hover:text-green-300 transition-colors">
              Try registering again
            </Link>
          </p>
          
          <p className="text-white/60 text-sm">
            Already verified?{' '}
            <Link href="/auth/login" className="text-green-400 hover:text-green-300 transition-colors">
              Sign in here
            </Link>
          </p>
        </div>

        {/* Back Link */}
        <div className="mt-8 text-center">
          <Link href="/" className="inline-flex items-center text-white/60 hover:text-white text-sm transition-colors">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to home
          </Link>
        </div>
      </div>
    </div>
  )
} 
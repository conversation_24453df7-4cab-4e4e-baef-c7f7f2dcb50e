require('dotenv').config({ path: '.env.local' })

console.log('Environment Test:')
console.log('NODE_ENV:', process.env.NODE_ENV)
console.log('NEXT_PUBLIC_SUPABASE_URL:', !!process.env.NEXT_PUBLIC_SUPABASE_URL)
console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)
console.log('SUPABASE_SERVICE_ROLE_KEY:', !!process.env.SUPABASE_SERVICE_ROLE_KEY)

if (process.env.NEXT_PUBLIC_SUPABASE_URL) {
  console.log('✅ Supabase URL found')
} else {
  console.log('❌ Supabase URL missing')
}

if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('✅ Service role key found')
} else {
  console.log('❌ Service role key missing')
}

console.log('Test completed') 
import { NextResponse } from 'next/server'

export async function GET() {
  return NextResponse.json({
    supabase_url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'set' : 'missing',
    supabase_anon_key: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'set' : 'missing',
    supabase_service_key: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'set' : 'missing',
    node_env: process.env.NODE_ENV,
    url_prefix: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 30) + '...',
    key_prefix: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 30) + '...'
  })
} 
import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { Shield, Users, CheckCircle } from 'lucide-react'
import { RegisterForm } from '@/components/auth/RegisterForm'
import { Logo } from '@/components/ui/Logo'
import { EliteText } from '@/components/ui/EliteText'

export const metadata: Metadata = {
  title: 'Join Prop Bully - Elite Trading Platform',
  description: 'Start your journey to becoming a funded trader. Join thousands of successful traders worldwide at Prop Bully.',
}

export default function RegisterPage() {
  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden py-12">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black">
        <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 via-transparent to-blue-500/5 animate-pulse" />
      </div>

      {/* Floating Background Elements */}
      <div className="absolute top-20 right-20 w-32 h-32 bg-green-500/10 rounded-full blur-xl animate-pulse" />
      <div className="absolute bottom-20 left-20 w-48 h-48 bg-blue-500/10 rounded-full blur-xl animate-pulse delay-1000" />
      <div className="absolute top-1/2 right-10 w-24 h-24 bg-yellow-500/10 rounded-full blur-xl animate-pulse delay-500" />

      {/* Main Content */}
      <div className="relative z-10 w-full max-w-5xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          
          {/* Left Side - Marketing Content */}
          <div className="space-y-8">
            {/* Header */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                {/* Prop Bully Logo - will use PNG if available */}
                <Logo 
                  size="md" 
                  imageSrc="/images/logos/prop-bully-logo.png"
                  alt="Prop Bully Logo"
                />
                <EliteText size="xl" variant="gold">Prop Bully</EliteText>
              </div>
              
              <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4">
                Join the Elite
                <span className="block text-gradient-green">Trading Platform</span>
              </h1>
              <p className="text-xl text-white/70 leading-relaxed">
                Start your journey to becoming a funded trader. Access up to $2M in trading capital with <span className="text-gradient-gold font-semibold">Prop Bully</span>.
              </p>
            </div>

            {/* Benefits */}
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                  <Shield className="h-6 w-6 text-green-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Instant Funding</h3>
                  <p className="text-white/60">Get funded immediately after passing evaluation with capital up to $2M available.</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Elite Community</h3>
                  <p className="text-white/60">Join 15,000+ professional traders with 24/7 support and educational resources.</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-purple-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">No Hidden Fees</h3>
                  <p className="text-white/60">Transparent pricing with profit sharing up to 90%. Keep most of what you earn.</p>
                </div>
              </div>
            </div>

            {/* Social Proof */}
            <div className="glass-card p-6">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-gradient-gold mb-1">$2.4B+</div>
                  <div className="text-white/60 text-sm">Capital Deployed</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-gradient-green mb-1">15,000+</div>
                  <div className="text-white/60 text-sm">Active Traders</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-gradient-red mb-1">98.2%</div>
                  <div className="text-white/60 text-sm">Uptime</div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Registration Form */}
          <div className="w-full max-w-md mx-auto lg:mx-0">
            <div className="glass-card p-8 neon-border">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">
                  Create Your Account
                </h2>
                <p className="text-white/60">
                  Start your trading journey today
                </p>
              </div>

              <RegisterForm />

              {/* Login Link */}
              <div className="mt-6 text-center">
                <p className="text-white/60 text-sm">
                  Already have an account?{' '}
                  <Link 
                    href="/auth/login" 
                    className="text-green-400 hover:text-green-300 font-medium transition-colors"
                  >
                    Sign in here
                  </Link>
                </p>
              </div>
            </div>

            {/* Security Notice */}
            <div className="mt-6 text-center">
              <div className="flex items-center justify-center space-x-6 text-xs text-white/50">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span>256-bit SSL</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span>GDPR Compliant</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span>KYC Protected</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Back to Home */}
        <div className="mt-12 text-center">
          <Link 
            href="/" 
            className="inline-flex items-center text-white/60 hover:text-white text-sm transition-colors"
          >
            ← Back to home
          </Link>
        </div>
      </div>
    </div>
  )
} 
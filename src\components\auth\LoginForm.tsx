'use client'

import { useState, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Mail, Lock, Eye, EyeOff, Loader2 } from 'lucide-react'
import { supabase } from '@/lib/supabase/client'

interface FormData {
  email: string
  password: string
}

interface FormErrors {
  email?: string
  password?: string
  general?: string
}

// Loading component for Suspense fallback
function LoginFormSkeleton() {
  return (
    <div className="space-y-6 animate-pulse">
      <div className="space-y-2">
        <div className="h-4 bg-slate-700 rounded w-24"></div>
        <div className="h-12 bg-slate-700 rounded"></div>
      </div>
      <div className="space-y-2">
        <div className="h-4 bg-slate-700 rounded w-20"></div>
        <div className="h-12 bg-slate-700 rounded"></div>
      </div>
      <div className="h-12 bg-slate-700 rounded"></div>
    </div>
  )
}

// Inner form component that uses useSearchParams
function LoginFormInner() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [formData, setFormData] = useState<FormData>({
    email: '<EMAIL>',
    password: 'password123'
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  // Get redirect parameter from URL
  const redirectTo = searchParams.get('redirect') || '/dashboard'

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.email) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    console.log('=== LOGIN FORM SUBMIT STARTED ===')
    console.log('Form data:', { email: formData.email, password: '***' })
    
    if (!validateForm()) {
      console.log('Form validation failed')
      return
    }

    setIsLoading(true)
    setErrors({})

    try {
      console.log('Starting authentication with Supabase...')
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email: formData.email,
        password: formData.password,
      })

      console.log('=== SUPABASE RESPONSE ===')
      console.log('Error:', error)
      console.log('User exists:', !!data.user)
      console.log('Session exists:', !!data.session)
      if (data.user) {
        console.log('User ID:', data.user.id)
        console.log('User email:', data.user.email)
      }

      if (error) {
        console.error('Authentication error:', error)
        setErrors({ general: `Authentication failed: ${error.message}` })
        return
      }

      if (data.user && data.session) {
        console.log('=== LOGIN SUCCESSFUL ===')
        console.log('Session access token length:', data.session.access_token?.length || 0)
        console.log('Redirect target:', redirectTo)
        
        // Set a flag to indicate successful login
        localStorage.setItem('loginSuccess', 'true')
        localStorage.setItem('loginTime', Date.now().toString())
        
        console.log('Attempting redirect to:', redirectTo)
        
        // CRITICAL: Wait for session to be stored in cookies before redirecting
        console.log('Waiting for session to be stored in cookies...')
        
        // Verify session is actually set
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        // Double-check session exists
        const { data: sessionCheck } = await supabase.auth.getSession()
        console.log('Session verification:', { 
          hasSession: !!sessionCheck.session,
          hasUser: !!sessionCheck.session?.user,
          sessionId: sessionCheck.session?.access_token?.substring(0, 20) + '...'
        })
        
        if (!sessionCheck.session) {
          console.error('Session not found after login - retrying...')
          setErrors({ general: 'Session error - please try again' })
          return
        }
        
        // Try multiple redirect methods to ensure it works
        console.log('Executing redirect...')
        
        // Force a hard refresh to ensure middleware sees the session
        console.log('Force refresh with session...')
        window.location.href = redirectTo
      } else {
        console.error('No user data or session received')
        setErrors({ general: 'Login failed: No user data received' })
      }
    } catch (error) {
      console.error('Login error:', error)
      setErrors({ general: 'An unexpected error occurred. Please try again.' })
    } finally {
      console.log('Setting loading to false')
      setIsLoading(false)
    }
  }

  // Direct login test function
  const testDirectLogin = async () => {
    console.log('=== DIRECT LOGIN TEST ===')
    setIsLoading(true)
    
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'password123',
      })

      console.log('Direct login result:', { error, user: !!data.user, session: !!data.session })
      
      if (data.user && data.session) {
        console.log('Direct login successful, redirecting...')
        localStorage.setItem('loginSuccess', 'true')
        localStorage.setItem('loginTime', Date.now().toString())
        
        // Use same redirect logic as main login
        try {
          router.push('/dashboard')
          router.refresh()
        } catch (routerError) {
          console.error('Router redirect failed:', routerError)
        }
        
        setTimeout(() => {
          window.location.replace('/dashboard')
        }, 500)
        
        setTimeout(() => {
          window.location.href = '/dashboard'
        }, 1000)
      } else {
        console.log('Direct login failed:', error?.message)
        setErrors({ general: error?.message || 'Direct login failed' })
      }
    } catch (error) {
      console.error('Direct login error:', error)
      setErrors({ general: 'Direct login error' })
    } finally {
      setIsLoading(false)
    }
  }

  // Quick test user creation function
  const createTestUser = async () => {
    setIsLoading(true)
    try {
      const { error } = await supabase.auth.signUp({
        email: '<EMAIL>',
        password: 'password123',
        options: {
          data: {
            first_name: 'Demo',
            last_name: 'Trader',
            full_name: 'Demo Trader'
          }
        }
      })

      if (error) {
        setErrors({ general: `Error creating test user: ${error.message}` })
      } else {
        setErrors({ general: 'Test user created! You can now login with: <EMAIL> / password123' })
        setFormData({ email: '<EMAIL>', password: 'password123' })
      }
    } catch (error) {
      console.error('Test user creation error:', error)
      setErrors({ general: 'Failed to create test user' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear specific field error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }))
    }
  }

  const handleForgotPassword = async () => {
    if (!formData.email) {
      setErrors({ email: 'Please enter your email address first' })
      return
    }

    setIsLoading(true)
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(formData.email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      })

      if (error) {
        setErrors({ general: error.message })
      } else {
        setErrors({ general: 'Password reset email sent! Check your inbox.' })
      }
    } catch (error) {
      console.error('Password reset error:', error)
      setErrors({ general: 'Failed to send reset email. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* General Error */}
      {errors.general && (
        <div className="glass-card p-4 border border-red-500/30 bg-red-500/10 rounded-lg">
          <p className="text-danger text-sm text-center">{errors.general}</p>
        </div>
      )}

      {/* Email Field */}
      <div className="space-y-2">
        <label htmlFor="email" className="text-sm font-medium text-primary-content">
          Email Address
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <Mail className="h-5 w-5 text-secondary-content" />
          </div>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={formData.email}
            onChange={handleChange}
            className={`
              w-full pl-12 pr-4 py-3 
              bg-slate-800/60 border rounded-xl
              text-primary-content placeholder-secondary-content
              focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent
              transition-all duration-200
              ${errors.email ? 'border-red-500/50' : 'border-slate-600 hover:border-slate-500'}
            `}
            placeholder="Enter your email"
          />
        </div>
        {errors.email && (
          <p className="text-danger text-sm">{errors.email}</p>
        )}
      </div>

      {/* Password Field */}
      <div className="space-y-2">
        <label htmlFor="password" className="text-sm font-medium text-primary-content">
          Password
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <Lock className="h-5 w-5 text-secondary-content" />
          </div>
          <input
            id="password"
            name="password"
            type={showPassword ? 'text' : 'password'}
            autoComplete="current-password"
            required
            value={formData.password}
            onChange={handleChange}
            className={`
              w-full pl-12 pr-12 py-3 
              bg-slate-800/60 border rounded-xl
              text-primary-content placeholder-secondary-content
              focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent
              transition-all duration-200
              ${errors.password ? 'border-red-500/50' : 'border-slate-600 hover:border-slate-500'}
            `}
            placeholder="Enter your password"
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute inset-y-0 right-0 pr-4 flex items-center"
          >
            {showPassword ? (
              <EyeOff className="h-5 w-5 text-secondary-content hover:text-primary-content transition-colors" />
            ) : (
              <Eye className="h-5 w-5 text-secondary-content hover:text-primary-content transition-colors" />
            )}
          </button>
        </div>
        {errors.password && (
          <p className="text-danger text-sm">{errors.password}</p>
        )}
      </div>

      {/* Forgot Password */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <input
            id="remember-me"
            name="remember-me"
            type="checkbox"
            className="h-4 w-4 bg-slate-800/60 border-slate-600 rounded focus:ring-green-500 focus:ring-2 text-green-500"
          />
          <label htmlFor="remember-me" className="ml-2 text-sm text-secondary-content">
            Remember me
          </label>
        </div>
        <button
          type="button"
          onClick={handleForgotPassword}
          disabled={isLoading}
          className="text-sm text-green-400 hover:text-green-300 transition-colors"
        >
          Forgot password?
        </button>
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={isLoading}
        onClick={handleSubmit}
        style={{ pointerEvents: 'auto', zIndex: 10 }}
        className="w-full buy-button py-3 px-4 rounded-xl font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 relative"
      >
        {isLoading ? (
          <>
            <Loader2 className="h-5 w-5 animate-spin" />
            <span>Signing in...</span>
          </>
        ) : (
          <span>Sign In</span>
        )}
      </button>

      {/* Development Helper - Create Test User */}
      {process.env.NODE_ENV === 'development' && (
        <div className="pt-4 border-t border-slate-700/50">
          <p className="text-xs text-secondary-content text-center mb-3">
            Development Mode: Need a test user?
          </p>
          <button
            type="button"
            onClick={createTestUser}
            disabled={isLoading}
            style={{ pointerEvents: 'auto', zIndex: 10 }}
            className="w-full professional-button py-2 px-4 rounded-xl text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed mb-2 relative"
          >
            Create Test User (<EMAIL>)
          </button>
          
          {/* Test React Event Handler */}
          <button
            type="button"
            onClick={() => {
              console.log('TEST: React event handler is working!');
              alert('React event handler is working!');
            }}
            style={{ pointerEvents: 'auto', zIndex: 10 }}
            className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-xl text-sm font-medium mb-2 relative cursor-pointer"
          >
            Test React Events
          </button>
          
          {/* Direct Login Test */}
          <button
            type="button"
            onClick={testDirectLogin}
            disabled={isLoading}
            style={{ pointerEvents: 'auto', zIndex: 10 }}
            className="w-full bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-xl text-sm font-medium disabled:opacity-50 relative cursor-pointer"
          >
            Direct Login Test
          </button>
        </div>
      )}
    </form>
  )
}

// Main export component with Suspense boundary
export function LoginForm() {
  return (
    <Suspense fallback={<LoginFormSkeleton />}>
      <LoginFormInner />
    </Suspense>
  )
} 
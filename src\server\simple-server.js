// Simple JavaScript server to avoid TypeScript compilation issues
const express = require('express')
const cors = require('cors')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: path.resolve(process.cwd(), '.env.local') })
require('dotenv').config()

const app = express()
const PORT = process.env.API_PORT || 8000

// Basic middleware
app.use(cors({
  origin: [
    process.env.FRONTEND_URL || "http://localhost:3000",
    "http://localhost:3001"
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}))

app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Simple health check
app.get('/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: '1.0.0'
    }
  })
})

// Test endpoints
app.get('/api/test', (req, res) => {
  res.json({ 
    success: true, 
    message: 'API is working',
    timestamp: new Date().toISOString()
  })
})

app.get('/api/analytics/test', (req, res) => {
  res.json({
    success: true,
    data: {
      message: 'Analytics API endpoint working',
      sampleData: {
        totalTrades: 150,
        winRate: 68.5,
        profit: 2847.50,
        drawdown: 5.2
      }
    }
  })
})

app.get('/api/risk/test', (req, res) => {
  res.json({
    success: true,
    data: {
      message: 'Risk management API endpoint working',
      currentRisk: {
        dailyDrawdown: 2.1,
        maxDailyDrawdown: 5.0,
        totalDrawdown: 3.5,
        maxTotalDrawdown: 10.0,
        riskScore: 3.2
      }
    }
  })
})

app.get('/api/users/test', (req, res) => {
  res.json({
    success: true,
    data: {
      message: 'User settings API endpoint working',
      userPreferences: {
        theme: 'dark',
        notifications: true,
        defaultLotSize: 0.1
      }
    }
  })
})

app.get('/api/support/test', (req, res) => {
  res.json({
    success: true,
    data: {
      message: 'Support API endpoint working',
      supportInfo: {
        ticketsOpen: 2,
        avgResponseTime: '4 hours',
        status: 'online'
      }
    }
  })
})

// Database test (simplified)
app.get('/test-db', (req, res) => {
  res.json({
    success: true,
    data: {
      message: 'Database connection test (simulated)',
      sample_data: [
        { id: '1', name: 'Challenge 1' },
        { id: '2', name: 'Challenge 2' }
      ],
      tables_accessible: true
    }
  })
})

// Handle 404 errors
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Route ${req.originalUrl} not found`,
      timestamp: new Date().toISOString()
    }
  })
})

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Simple Prop Firm API Server running on port ${PORT}`)
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`)
  console.log(`🔗 Health check: http://localhost:${PORT}/health`)
  console.log(`🔗 Database test: http://localhost:${PORT}/test-db`)
  console.log(`🧪 API Test: http://localhost:${PORT}/api/test`)
  console.log(`📊 Analytics Test: http://localhost:${PORT}/api/analytics/test`)
  console.log(`🛡️ Risk Test: http://localhost:${PORT}/api/risk/test`)
  console.log(`👤 Users Test: http://localhost:${PORT}/api/users/test`)
  console.log(`❓ Support Test: http://localhost:${PORT}/api/support/test`)
  console.log('🎉 Server ready for frontend connection!')
}) 
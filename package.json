{"name": "prop-bully", "version": "1.0.0", "description": "Prop Bully - Elite Proprietary Trading Platform with MetaTrader 5 Integration", "main": "index.js", "scripts": {"dev": "next dev", "dev:frontend": "next dev", "dev:server": "tsx watch src/server/index.ts", "dev:minimal": "node src/server/minimal-server.js", "dev:simple": "node src/server/simple-server.js", "dev:both": "concurrently \"npm run dev:frontend\" \"npm run dev:server\"", "build": "next build", "build:server": "tsc --project tsconfig.server.json", "start": "next start", "start:server": "node dist/server/index.js", "start:both": "concurrently \"npm run start\" \"npm run start:server\"", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:seed": "tsx src/scripts/seedDatabase.ts", "test:api": "node scripts/test-api.js"}, "keywords": ["prop-bully", "trading", "fintech", "prop-firm", "mt5", "nextjs"], "author": "Prop Bully Team", "license": "ISC", "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-toast": "^1.2.4", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.6", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.0", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/node": "^22.10.1", "@types/nodemailer": "^6.4.17", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "decimal.js": "^10.4.3", "dotenv": "^16.5.0", "eslint": "^8.57.1", "eslint-config-next": "^15.3.3", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.469.0", "morgan": "^1.10.0", "next": "^15.3.3", "nodemailer": "^7.0.3", "postcss": "^8.4.49", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.54.2", "recharts": "^2.14.1", "redis": "^5.1.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "stripe": "^17.1.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.3", "uuid": "^11.1.0", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.4", "concurrently": "^9.1.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tsx": "^4.19.4"}}
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
SUPABASE_JWT_SECRET=your_supabase_jwt_secret

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Application Configuration
NEXTAUTH_SECRET=your_nextauth_secret_32_chars_min
NEXTAUTH_URL=http://localhost:3000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# API Server Configuration
API_PORT=8000
JWT_SECRET=your_jwt_secret_for_api_auth

# MT5 Integration (to be configured)
MT5_SERVER_URL=your_mt5_server_url
MT5_API_KEY=your_mt5_api_key
MT5_SERVER_NAME=PropFirm-Demo

# Redis Configuration (optional for caching)
REDIS_URL=redis://localhost:6379

# Email Service Configuration (optional)
SENDGRID_API_KEY=your_sendgrid_api_key
FROM_EMAIL=<EMAIL>

# Development/Testing Configuration
LOG_LEVEL=debug
ENABLE_API_DOCS=true

# Database Configuration (if using external PostgreSQL)
DATABASE_URL=postgresql://username:password@localhost:5432/propfirm_db

# Monitoring and Analytics (optional)
SENTRY_DSN=your_sentry_dsn
DATADOG_API_KEY=your_datadog_api_key 
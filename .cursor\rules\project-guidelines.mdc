---
description: 
globs: 
alwaysApply: true
---
# Prop Firm Platform - AI Assistant Rules

ALWAYS RUN A VIRTUAL ENVIROMENT FOR python TESTING!!!!

###
CONTEXT7
# MAKE SURE TO USE CONTEXT7 MCP SERVER FOR ANY CODE REFRENCES or ANY  EXAMPLES! FOLLOW THESE GUIDELINES STRICTLY!
#######


## 🔄 Project Awareness & Context
- Always read PLANNING.md at the start of a new conversation to understand the project's architecture, goals, tech stack, and constraints
- Check TASK.md before starting any new work. If the task isn't listed, add it with a brief description and today's date
- Use consistent naming conventions, file structure, and architecture patterns as described in PLANNING.md
- Reference the project vision: building a secure prop firm platform with MT5 integration and custom web trading interface

## 🧱 Code Structure & Modularity  
- Never create a file longer than 500 lines of code. If approaching this limit, refactor by splitting into modules
- Organize code into clearly separated modules grouped by feature (auth, trading, payments, risk-management)
- Use TypeScript strict mode for all files
- Implement barrel exports (index.ts files) for clean imports
- Follow Next.js 14+ App Router conventions for frontend structure

## 🛡️ Security & Compliance (CRITICAL)
- Never hardcode API keys, secrets, or sensitive data - always use environment variables
- Implement proper input validation and sanitization for all user inputs
- Use Supabase Row Level Security (RLS) policies for database access control
- Implement rate limiting on all API endpoints
- Add proper CORS configuration
- Log all trading-related activities for audit purposes
- Encrypt sensitive data before storing in database

## 💰 Financial Data Handling
- Use precise decimal arithmetic for all financial calculations (avoid floating point)
- Implement proper error handling for payment processing
- Add transaction logging for all financial operations  
- Validate all trading parameters against risk rules before execution
- Store all monetary values in smallest currency units (cents for USD)

## 🧪 Testing & Reliability
- Create comprehensive unit tests for all business logic, especially:
  - Trading calculation functions
  - Risk management rules
  - Payment processing logic
  - Authentication and authorization
- Write integration tests for API endpoints
- Include edge cases and error scenarios in tests
- Tests should live in `__tests__` folders alongside source files
- Use Jest and React Testing Library for frontend tests

## ✅ Task Management & Documentation
- Mark completed tasks in TASK.md immediately after finishing them
- Add new sub-tasks or discoveries to TASK.md under "Discovered During Work"
- Update README.md when adding new features, dependencies, or setup steps
- Write JSDoc comments for all functions using Google style format
- Comment complex business logic with "Reason:" explanations

## 🎯 TypeScript & Code Quality
- Use strict TypeScript configuration with no implicit any
- Generate and use Supabase types for database operations
- Implement proper error types and handling
- Use Zod for runtime validation and type inference
- Follow consistent naming: camelCase for variables/functions, PascalCase for components/types

## 🔌 API & Integration Patterns
- Use consistent API response format: `{ success: boolean, data?: any, error?: string }`
- Implement proper error handling middleware
- Use connection pooling for database connections
- Implement retry logic for external API calls (Stripe, MT5)
- Add proper request/response logging

## 🖥️ Frontend Development
- Use Tailwind CSS with consistent design tokens
- Implement proper loading and error states for all async operations
- Use React Query for server state management
- Implement optimistic updates where appropriate
- Ensure mobile-responsive design
- Add proper accessibility attributes

## 📊 Real-time & Performance
- Use Supabase real-time subscriptions for live data updates
- Implement WebSocket connections for trading data
- Add proper connection management and reconnection logic  
- Optimize database queries with proper indexing
- Implement caching strategies for frequently accessed data

## 🧠 AI Behavior Rules
- Never assume missing context - ask questions if project requirements are unclear
- Only use verified packages and APIs - no hallucinated dependencies
- Always confirm file paths and imports exist before referencing them
- When implementing financial/trading logic, double-check calculations and edge cases
- For MT5 integration, research actual MT5 API capabilities before implementation
- Never delete existing code unless explicitly instructed or part of refactoring task

## 📋 Development Workflow
- Focus on one task at a time from TASK.md
- Create feature branches for significant changes
- Write meaningful commit messages
- Update documentation alongside code changes  
- Test thoroughly before marking tasks complete
- Consider security implications for every change

## 🚨 Critical Reminders
- This is a financial platform handling real money - security and accuracy are paramount
- All trading operations must have proper risk controls and logging
- Payment processing must be thoroughly tested and secured
- User data privacy and protection is essential

- Regulatory compliance considerations must be built-in from the start
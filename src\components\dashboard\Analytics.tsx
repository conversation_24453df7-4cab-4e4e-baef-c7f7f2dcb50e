'use client'

import { useState } from 'react'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Target,
  Activity,
  Calendar,
  RefreshCw,
  Eye,
  EyeOff,
  PieChart,
  LineChart,
  MoreHorizontal,
  AlertCircle
} from 'lucide-react'
import type { PerformanceMetrics, ChartDataPoint, TradingAccount } from '@/types'
import { useAnalyticsData, useEquityCurve, useDailyPnL } from '@/hooks/useApiData'
import { apiClient } from '@/lib/api/client'

interface AnalyticsProps {
  account?: TradingAccount
}

const Analytics = ({ account: _account }: AnalyticsProps) => {
  const [timeframe, setTimeframe] = useState('7D')
  const [showPnL, setShowPnL] = useState(true)
  
  // Demo account ID - in real app this would come from user's account
  const accountId = 'demo-account-123'
  
  // Fetch real data from backend
  const { 
    data: performanceMetrics, 
    loading: metricsLoading, 
    error: metricsError,
    refetch: refetchMetrics 
  } = useAnalyticsData(accountId, timeframe)
  
  const { 
    data: equityData, 
    loading: equityLoading, 
    error: equityError 
  } = useEquityCurve(accountId, timeframe)
  
  const { 
    data: dailyPnLApiData, 
    loading: pnlLoading, 
    error: pnlError 
  } = useDailyPnL(accountId, timeframe)

  const isLoading = metricsLoading || equityLoading || pnlLoading
  const hasError = metricsError || equityError || pnlError

  // Fallback data for when API returns empty results
  const fallbackMetrics: PerformanceMetrics = {
    totalTrades: 127,
    winningTrades: 78,
    losingTrades: 49,
    winRate: 61.42,
    avgWin: 85.50,
    avgLoss: -45.25,
    profitFactor: 1.87,
    maxDrawdown: 8.5,
    returnOnInvestment: 12.3,
    sharpeRatio: 1.45,
    period: {
      start: new Date('2024-01-01'),
      end: new Date()
    }
  }

  // Mock data will be defined later based on API availability

  const tradingHours = [
    { hour: '00:00', trades: 5 },
    { hour: '02:00', trades: 12 },
    { hour: '04:00', trades: 8 },
    { hour: '06:00', trades: 15 },
    { hour: '08:00', trades: 24 },
    { hour: '10:00', trades: 31 },
    { hour: '12:00', trades: 28 },
    { hour: '14:00', trades: 35 },
    { hour: '16:00', trades: 29 },
    { hour: '18:00', trades: 18 },
    { hour: '20:00', trades: 22 },
    { hour: '22:00', trades: 14 },
  ]

  const symbolBreakdown = [
    { symbol: 'EUR/USD', trades: 45, profit: 1250.50, winRate: 68.9 },
    { symbol: 'GBP/USD', trades: 32, profit: 890.25, winRate: 62.5 },
    { symbol: 'USD/JPY', trades: 28, profit: -420.75, winRate: 42.9 },
    { symbol: 'AUD/USD', trades: 22, profit: 650.00, winRate: 72.7 },
  ]

  const handleRefresh = async () => {
    // Refresh all data from API
    await Promise.all([
      refetchMetrics(),
      // Add other refetch functions when available
    ])
  }

  const SimpleChart = ({ data, color = '#22c55e', height = 100 }: { 
    data: ChartDataPoint[], 
    color?: string, 
    height?: number 
  }) => {
    const maxValue = Math.max(...data.map(d => d.value))
    const minValue = Math.min(...data.map(d => d.value))
    const range = maxValue - minValue || 1

    const points = data.map((point, index) => {
      const x = (index / (data.length - 1)) * 300
      const y = height - ((point.value - minValue) / range) * height
      return `${x},${y}`
    }).join(' ')

    return (
      <svg className="w-full" style={{ height }} viewBox={`0 0 300 ${height}`}>
        <defs>
          <linearGradient id={`gradient-${color.slice(1)}`} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={color} stopOpacity="0.3" />
            <stop offset="100%" stopColor={color} stopOpacity="0.05" />
          </linearGradient>
        </defs>
        <polyline
          fill={`url(#gradient-${color.slice(1)})`}
          stroke={color}
          strokeWidth="2"
          points={`0,${height} ${points} 300,${height}`}
        />
        <polyline
          fill="none"
          stroke={color}
          strokeWidth="2"
          points={points}
        />
      </svg>
    )
  }

  // Use API data if available, otherwise fallback to demo data
  const metrics = performanceMetrics || fallbackMetrics
  const equityChartData = equityData?.data || [
    { timestamp: new Date('2024-01-01'), value: 10000 },
    { timestamp: new Date('2024-01-02'), value: 10150 },
    { timestamp: new Date('2024-01-03'), value: 10080 },
    { timestamp: new Date('2024-01-04'), value: 10220 },
    { timestamp: new Date('2024-01-05'), value: 10180 },
    { timestamp: new Date('2024-01-06'), value: 10350 },
    { timestamp: new Date('2024-01-07'), value: 10280 },
    { timestamp: new Date('2024-01-08'), value: 10420 },
  ]
  
  const dailyPnLData = dailyPnLApiData?.data || [
    { timestamp: new Date('2024-01-01'), value: 150 },
    { timestamp: new Date('2024-01-02'), value: -70 },
    { timestamp: new Date('2024-01-03'), value: 140 },
    { timestamp: new Date('2024-01-04'), value: -40 },
    { timestamp: new Date('2024-01-05'), value: 170 },
    { timestamp: new Date('2024-01-06'), value: -130 },
    { timestamp: new Date('2024-01-07'), value: 140 },
    { timestamp: new Date('2024-01-08'), value: 185 },
  ]

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="glass-card p-8 text-center">
          <RefreshCw className="h-8 w-8 text-green-500 animate-spin mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-primary-content mb-2">Loading Analytics</h3>
          <p className="text-secondary-content">Fetching your trading performance data...</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (hasError) {
    return (
      <div className="space-y-6">
        <div className="glass-card p-8 text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-primary-content mb-2">Error Loading Data</h3>
          <p className="text-secondary-content mb-4">
            {metricsError || equityError || pnlError || 'Failed to load analytics data'}
          </p>
          <button
            onClick={handleRefresh}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-primary-content">Analytics Dashboard</h1>
          <p className="text-secondary-content mt-1">
            Comprehensive trading performance analysis and insights 
            {performanceMetrics ? ' (Live Data)' : ' (Demo Data)'}
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Timeframe Selector */}
          <div className="flex items-center space-x-1 bg-slate-800 rounded-lg p-1">
            {['1D', '7D', '30D', '90D', 'ALL'].map((tf) => (
              <button
                key={tf}
                onClick={() => setTimeframe(tf)}
                className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                  timeframe === tf 
                    ? 'bg-green-500 text-white' 
                    : 'text-secondary-content hover:text-primary-content'
                }`}
              >
                {tf}
              </button>
            ))}
          </div>

          {/* Toggle P&L Visibility */}
          <button
            onClick={() => setShowPnL(!showPnL)}
            className="p-2 rounded-lg hover:bg-slate-700 transition-colors"
            title={showPnL ? 'Hide P&L' : 'Show P&L'}
          >
            {showPnL ? (
              <Eye className="h-4 w-4 text-secondary-content" />
            ) : (
              <EyeOff className="h-4 w-4 text-secondary-content" />
            )}
          </button>

          {/* Refresh Button */}
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="p-2 rounded-lg hover:bg-slate-700 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 text-secondary-content ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-green-500/20 rounded-lg">
              <TrendingUp className="h-5 w-5 text-green-400" />
            </div>
            <span className="text-xs text-secondary-content">Win Rate</span>
          </div>
          <div className="space-y-1">
            <div className="text-2xl font-bold text-primary-content">
              {metrics.winRate.toFixed(1)}%
            </div>
            <div className="text-xs text-green-400">
              {metrics.winningTrades}/{metrics.totalTrades} trades
            </div>
          </div>
        </div>

        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-blue-500/20 rounded-lg">
              <Target className="h-5 w-5 text-blue-400" />
            </div>
            <span className="text-xs text-secondary-content">Profit Factor</span>
          </div>
          <div className="space-y-1">
            <div className="text-2xl font-bold text-primary-content">
              {metrics.profitFactor.toFixed(2)}
            </div>
            <div className="text-xs text-blue-400">
              Excellent performance
            </div>
          </div>
        </div>

        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-purple-500/20 rounded-lg">
              <Activity className="h-5 w-5 text-purple-400" />
            </div>
            <span className="text-xs text-secondary-content">Avg Win</span>
          </div>
          <div className="space-y-1">
            <div className="text-2xl font-bold text-primary-content">
              {showPnL ? `$${metrics.avgWin.toFixed(2)}` : '••••'}
            </div>
            <div className="text-xs text-purple-400">
              Per winning trade
            </div>
          </div>
        </div>

        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-red-500/20 rounded-lg">
              <TrendingDown className="h-5 w-5 text-red-400" />
            </div>
            <span className="text-xs text-secondary-content">Max Drawdown</span>
          </div>
          <div className="space-y-1">
            <div className="text-2xl font-bold text-primary-content">
              {metrics.maxDrawdown.toFixed(1)}%
            </div>
            <div className="text-xs text-red-400">
              Risk controlled
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Equity Curve */}
        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-primary-content">Equity Curve</h3>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-secondary-content">
                {showPnL ? `$${equityChartData[equityChartData.length - 1].value.toLocaleString()}` : '••••'}
              </span>
              <LineChart className="h-4 w-4 text-secondary-content" />
            </div>
          </div>
          <div className="h-48">
            {showPnL ? (
              <SimpleChart data={equityChartData} color="#22c55e" height={192} />
            ) : (
              <div className="flex items-center justify-center h-full bg-slate-800/50 rounded-lg">
                <div className="text-center">
                  <EyeOff className="h-8 w-8 text-secondary-content mx-auto mb-2" />
                  <p className="text-secondary-content text-sm">P&L Hidden</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Daily P&L */}
        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-primary-content">Daily P&L</h3>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-green-400">+$445 today</span>
              <BarChart3 className="h-4 w-4 text-secondary-content" />
            </div>
          </div>
          <div className="h-48">
            {showPnL ? (
              <SimpleChart data={dailyPnLData} color="#3b82f6" height={192} />
            ) : (
              <div className="flex items-center justify-center h-full bg-slate-800/50 rounded-lg">
                <div className="text-center">
                  <EyeOff className="h-8 w-8 text-secondary-content mx-auto mb-2" />
                  <p className="text-secondary-content text-sm">P&L Hidden</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Trading Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Trading Hours Heatmap */}
        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-primary-content">Trading Hours Analysis</h3>
            <Calendar className="h-5 w-5 text-secondary-content" />
          </div>
          <div className="space-y-3">
            {tradingHours.map((hour) => {
              const maxTrades = Math.max(...tradingHours.map(h => h.trades))
              const intensity = (hour.trades / maxTrades) * 100
              
              return (
                <div key={hour.hour} className="flex items-center space-x-3">
                  <span className="text-xs text-secondary-content w-12">{hour.hour}</span>
                  <div className="flex-1 bg-slate-800 rounded-full h-3 relative overflow-hidden">
                    <div 
                      className="h-full bg-gradient-to-r from-green-500/50 to-green-400 transition-all duration-300"
                      style={{ width: `${intensity}%` }}
                    />
                  </div>
                  <span className="text-xs text-primary-content w-8">{hour.trades}</span>
                </div>
              )
            })}
          </div>
        </div>

        {/* Symbol Performance */}
        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-primary-content">Symbol Performance</h3>
            <PieChart className="h-5 w-5 text-secondary-content" />
          </div>
          <div className="space-y-4">
            {symbolBreakdown.map((symbol) => (
              <div key={symbol.symbol} className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="text-sm font-medium text-primary-content">
                    {symbol.symbol}
                  </div>
                  <div className="text-xs text-secondary-content">
                    {symbol.trades} trades
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="text-xs text-secondary-content">
                    {symbol.winRate.toFixed(1)}% win
                  </div>
                  <div className={`text-sm font-medium ${symbol.profit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {showPnL ? (
                      `${symbol.profit >= 0 ? '+' : ''}$${symbol.profit.toFixed(2)}`
                    ) : '••••'}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Performance Summary */}
      <div className="glass-card p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-primary-content">Performance Summary</h3>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-secondary-content">
              {new Date(performanceMetrics.period.start).toLocaleDateString()} - {new Date(performanceMetrics.period.end).toLocaleDateString()}
            </span>
            <MoreHorizontal className="h-4 w-4 text-secondary-content" />
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary-content mb-1">
              {performanceMetrics.totalTrades}
            </div>
            <div className="text-xs text-secondary-content">Total Trades</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400 mb-1">
              {performanceMetrics.winningTrades}
            </div>
            <div className="text-xs text-secondary-content">Winning</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-red-400 mb-1">
              {performanceMetrics.losingTrades}
            </div>
            <div className="text-xs text-secondary-content">Losing</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400 mb-1">
              {performanceMetrics.profitFactor.toFixed(2)}
            </div>
            <div className="text-xs text-secondary-content">Profit Factor</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400 mb-1">
              {showPnL ? `$${performanceMetrics.avgWin.toFixed(0)}` : '••••'}
            </div>
            <div className="text-xs text-secondary-content">Avg Win</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-400 mb-1">
              {performanceMetrics.returnOnInvestment.toFixed(1)}%
            </div>
            <div className="text-xs text-secondary-content">ROI</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Analytics 
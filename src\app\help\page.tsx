import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { ArrowLeft, Search, HelpCircle, BookOpen, MessageCircle, Download, ExternalLink } from 'lucide-react'
import { Logo } from '@/components/ui/Logo'
import { EliteText } from '@/components/ui/EliteText'

export const metadata: Metadata = {
  title: 'Help Center - Prop Bully',
  description: 'Find answers to your questions, guides, and support resources for Prop Bully trading platform.',
}

export default function HelpPage() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Professional Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black">
        <div className="absolute inset-0 bg-gradient-to-r from-green-500/3 via-transparent to-blue-500/3" />
      </div>

      {/* Navigation */}
      <nav className="relative z-10 flex items-center justify-between p-6 border-b border-slate-700/50">
        <div className="flex items-center space-x-4">
          <Logo size="md" imageSrc="/images/logos/prop-bully-logo.png" alt="Prop Bully Logo" />
          <EliteText size="lg" variant="gold">Prop Bully</EliteText>
        </div>
        <div className="flex items-center space-x-4">
          <Link href="/" className="flex items-center space-x-2 text-white/60 hover:text-white transition-colors">
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Home</span>
          </Link>
          <Link href="/contact">
            <button className="professional-button">Contact Support</button>
          </Link>
        </div>
      </nav>

      <div className="relative z-10 max-w-6xl mx-auto px-6 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            Help <span className="text-gradient-gold">Center</span>
          </h1>
          <p className="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed mb-8">
            Find answers to your questions, guides, and resources to help you succeed on the Prop Bully platform.
          </p>
          
          {/* Search Bar */}
          <div className="max-w-xl mx-auto">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/40" />
              <input
                type="text"
                placeholder="Search for help articles, guides, or topics..."
                className="w-full pl-12 pr-4 py-4 bg-slate-800/60 border border-slate-600 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Quick Links */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div className="glass-card p-6 text-center hover:bg-slate-700/30 transition-colors cursor-pointer">
            <HelpCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Getting Started</h3>
            <p className="text-white/70 text-sm">New to Prop Bully? Start here for the basics</p>
          </div>

          <div className="glass-card p-6 text-center hover:bg-slate-700/30 transition-colors cursor-pointer">
            <BookOpen className="h-12 w-12 text-blue-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Trading Guides</h3>
            <p className="text-white/70 text-sm">Learn trading strategies and best practices</p>
          </div>

          <div className="glass-card p-6 text-center hover:bg-slate-700/30 transition-colors cursor-pointer">
            <MessageCircle className="h-12 w-12 text-purple-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Contact Support</h3>
            <p className="text-white/70 text-sm">Need personal help? Reach out to our team</p>
          </div>
        </div>

        {/* Popular Articles */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8">Popular Articles</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="glass-card p-6 hover:bg-slate-700/30 transition-colors cursor-pointer">
              <h3 className="text-lg font-semibold text-white mb-3">How to Start Your First Challenge</h3>
              <p className="text-white/70 text-sm mb-4">
                Complete step-by-step guide to purchasing and beginning your trading challenge.
              </p>
              <div className="flex items-center justify-between">
                <span className="text-xs text-green-400 bg-green-500/20 px-2 py-1 rounded">Getting Started</span>
                <ExternalLink className="h-4 w-4 text-white/40" />
              </div>
            </div>

            <div className="glass-card p-6 hover:bg-slate-700/30 transition-colors cursor-pointer">
              <h3 className="text-lg font-semibold text-white mb-3">Understanding Risk Management Rules</h3>
              <p className="text-white/70 text-sm mb-4">
                Learn about daily loss limits, drawdown rules, and how to avoid violations.
              </p>
              <div className="flex items-center justify-between">
                <span className="text-xs text-red-400 bg-red-500/20 px-2 py-1 rounded">Risk Management</span>
                <ExternalLink className="h-4 w-4 text-white/40" />
              </div>
            </div>

            <div className="glass-card p-6 hover:bg-slate-700/30 transition-colors cursor-pointer">
              <h3 className="text-lg font-semibold text-white mb-3">MT5 Platform Setup Guide</h3>
              <p className="text-white/70 text-sm mb-4">
                How to download, install, and configure MetaTrader 5 for your challenge account.
              </p>
              <div className="flex items-center justify-between">
                <span className="text-xs text-blue-400 bg-blue-500/20 px-2 py-1 rounded">Platform</span>
                <ExternalLink className="h-4 w-4 text-white/40" />
              </div>
            </div>

            <div className="glass-card p-6 hover:bg-slate-700/30 transition-colors cursor-pointer">
              <h3 className="text-lg font-semibold text-white mb-3">Withdrawal Process & Payouts</h3>
              <p className="text-white/70 text-sm mb-4">
                Everything you need to know about requesting and receiving profit withdrawals.
              </p>
              <div className="flex items-center justify-between">
                <span className="text-xs text-purple-400 bg-purple-500/20 px-2 py-1 rounded">Payments</span>
                <ExternalLink className="h-4 w-4 text-white/40" />
              </div>
            </div>
          </div>
        </section>

        {/* Categories */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8">Browse by Category</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="glass-card p-6 text-center hover:bg-slate-700/30 transition-colors cursor-pointer">
              <div className="w-16 h-16 bg-green-500/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🚀</span>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Getting Started</h3>
              <p className="text-white/60 text-sm">12 articles</p>
            </div>

            <div className="glass-card p-6 text-center hover:bg-slate-700/30 transition-colors cursor-pointer">
              <div className="w-16 h-16 bg-blue-500/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📊</span>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Trading</h3>
              <p className="text-white/60 text-sm">8 articles</p>
            </div>

            <div className="glass-card p-6 text-center hover:bg-slate-700/30 transition-colors cursor-pointer">
              <div className="w-16 h-16 bg-red-500/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🛡️</span>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Risk Management</h3>
              <p className="text-white/60 text-sm">6 articles</p>
            </div>

            <div className="glass-card p-6 text-center hover:bg-slate-700/30 transition-colors cursor-pointer">
              <div className="w-16 h-16 bg-purple-500/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💰</span>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Payments</h3>
              <p className="text-white/60 text-sm">5 articles</p>
            </div>
          </div>
        </section>

        {/* Downloads */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8">Downloads & Resources</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="glass-card p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Download className="h-8 w-8 text-green-400" />
                <div>
                  <h3 className="text-lg font-semibold text-white">Trading Rules PDF</h3>
                  <p className="text-white/60 text-sm">Complete rulebook</p>
                </div>
              </div>
              <button className="w-full professional-button-dark py-2">Download</button>
            </div>

            <div className="glass-card p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Download className="h-8 w-8 text-blue-400" />
                <div>
                  <h3 className="text-lg font-semibold text-white">MT5 Setup Guide</h3>
                  <p className="text-white/60 text-sm">Step-by-step tutorial</p>
                </div>
              </div>
              <button className="w-full professional-button-dark py-2">Download</button>
            </div>

            <div className="glass-card p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Download className="h-8 w-8 text-purple-400" />
                <div>
                  <h3 className="text-lg font-semibold text-white">Risk Calculator</h3>
                  <p className="text-white/60 text-sm">Excel spreadsheet</p>
                </div>
              </div>
              <button className="w-full professional-button-dark py-2">Download</button>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section className="text-center">
          <div className="glass-card p-12">
            <h2 className="text-3xl font-bold text-white mb-4">Still Need Help?</h2>
            <p className="text-xl text-white/70 mb-8 max-w-2xl mx-auto">
              Can't find what you're looking for? Our support team is available 24/7 to help you succeed.
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
              <Link href="/contact">
                <button className="professional-button-dark flex items-center space-x-2 px-8 py-4 text-lg">
                  <MessageCircle className="h-5 w-5" />
                  <span>Contact Support</span>
                </button>
              </Link>
              <button className="professional-button px-8 py-4 text-lg">
                Start Live Chat
              </button>
            </div>

            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 text-center text-sm">
              <div>
                <div className="text-2xl font-bold text-green-400 mb-1">24/7</div>
                <div className="text-white/60">Live chat support</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-400 mb-1">&lt;2hrs</div>
                <div className="text-white/60">Email response time</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-400 mb-1">98%</div>
                <div className="text-white/60">Customer satisfaction</div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
} 
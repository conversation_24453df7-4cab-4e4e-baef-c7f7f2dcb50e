import { Metadata } from 'next'
import Link from 'next/link'
import { ArrowLeft, Mail, Phone, MessageCircle, MapPin, Send } from 'lucide-react'
import { Logo } from '@/components/ui/Logo'
import { EliteText } from '@/components/ui/EliteText'

export const metadata: Metadata = {
  title: 'Contact Us - Prop Bully',
  description: 'Get in touch with our support team. We\'re here to help with your trading journey.',
}

export default function ContactPage() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Professional Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black">
        <div className="absolute inset-0 bg-gradient-to-r from-green-500/3 via-transparent to-blue-500/3" />
      </div>

      {/* Navigation */}
      <nav className="relative z-10 flex items-center justify-between p-6 border-b border-slate-700/50">
        <div className="flex items-center space-x-4">
          <Logo size="md" imageSrc="/images/logos/prop-bully-logo.png" alt="Prop Bully Logo" />
          <EliteText size="lg" variant="gold">Prop Bully</EliteText>
        </div>
        <Link href="/" className="flex items-center space-x-2 text-white/60 hover:text-white transition-colors">
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Home</span>
        </Link>
      </nav>

      <div className="relative z-10 max-w-6xl mx-auto px-6 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            Contact <span className="text-gradient-gold">Our Team</span>
          </h1>
          <p className="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
            Have questions? Need support? Our expert team is here to help you succeed in your trading journey.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            <div className="glass-card p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Get in Touch</h2>
              
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center flex-shrink-0">
                    <MessageCircle className="h-6 w-6 text-green-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Live Chat</h3>
                    <p className="text-white/70 mb-3">Available 24/7 for immediate assistance</p>
                    <button className="professional-button-dark text-sm px-4 py-2">
                      Start Live Chat
                    </button>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Mail className="h-6 w-6 text-blue-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Email Support</h3>
                    <p className="text-white/70 mb-1"><EMAIL></p>
                    <p className="text-white/50 text-sm">Response within 2 hours</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Phone className="h-6 w-6 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Phone Support</h3>
                    <p className="text-white/70 mb-1">+****************</p>
                    <p className="text-white/50 text-sm">Mon-Fri 9AM-6PM EST</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-orange-500/20 rounded-xl flex items-center justify-center flex-shrink-0">
                    <MapPin className="h-6 w-6 text-orange-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Office Location</h3>
                    <p className="text-white/70 mb-1">123 Trading Plaza</p>
                    <p className="text-white/70 mb-1">Financial District</p>
                    <p className="text-white/70">New York, NY 10001</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="glass-card p-8">
              <h3 className="text-xl font-bold text-white mb-4">Support Hours</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-white/70">Live Chat</span>
                  <span className="text-green-400 font-medium">24/7</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white/70">Email Support</span>
                  <span className="text-green-400 font-medium">24/7</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white/70">Phone Support</span>
                  <span className="text-white font-medium">Mon-Fri 9AM-6PM EST</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white/70">Weekend Support</span>
                  <span className="text-white font-medium">Chat & Email Only</span>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="glass-card p-8">
            <h2 className="text-2xl font-bold text-white mb-6">Send us a Message</h2>
            
            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-white mb-2">
                    First Name
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    className="w-full px-4 py-3 bg-slate-800/60 border border-slate-600 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="John"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium text-white mb-2">
                    Last Name
                  </label>
                  <input
                    type="text"
                    id="lastName"
                    className="w-full px-4 py-3 bg-slate-800/60 border border-slate-600 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="Doe"
                    required
                  />
                </div>
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-white mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  className="w-full px-4 py-3 bg-slate-800/60 border border-slate-600 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-white mb-2">
                  Subject
                </label>
                <select
                  id="subject"
                  className="w-full px-4 py-3 bg-slate-800/60 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  required
                >
                  <option value="">Select a subject</option>
                  <option value="general">General Inquiry</option>
                  <option value="support">Technical Support</option>
                  <option value="challenge">Challenge Questions</option>
                  <option value="billing">Billing & Payments</option>
                  <option value="partnership">Partnership Opportunity</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-white mb-2">
                  Message
                </label>
                <textarea
                  id="message"
                  rows={6}
                  className="w-full px-4 py-3 bg-slate-800/60 border border-slate-600 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                  placeholder="How can we help you?"
                  required
                ></textarea>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="newsletter"
                  className="w-4 h-4 text-green-600 bg-slate-800 border-slate-600 rounded focus:ring-green-500"
                />
                <label htmlFor="newsletter" className="text-sm text-white/70">
                  I'd like to receive updates about new features and trading tips
                </label>
              </div>

              <button
                type="submit"
                className="w-full professional-button-dark flex items-center justify-center space-x-2 py-3"
              >
                <Send className="h-4 w-4" />
                <span>Send Message</span>
              </button>
            </form>
          </div>
        </div>

        {/* FAQ Section */}
        <section className="mt-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Quick Answers</h2>
            <p className="text-white/70">Common questions we receive</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold text-white mb-3">How quickly will I get a response?</h3>
              <p className="text-white/70 text-sm">
                Live chat responses are immediate, email responses within 2 hours, and phone calls are answered during business hours.
              </p>
            </div>

            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold text-white mb-3">Can I schedule a call?</h3>
              <p className="text-white/70 text-sm">
                Yes! Contact us through any channel and we'll arrange a convenient time to speak with one of our experts.
              </p>
            </div>

            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold text-white mb-3">Do you provide phone support in other languages?</h3>
              <p className="text-white/70 text-sm">
                Currently, phone support is available in English. However, we offer email support in multiple languages.
              </p>
            </div>

            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold text-white mb-3">Is weekend support available?</h3>
              <p className="text-white/70 text-sm">
                Live chat and email support are available 24/7, including weekends. Phone support is weekdays only.
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
} 
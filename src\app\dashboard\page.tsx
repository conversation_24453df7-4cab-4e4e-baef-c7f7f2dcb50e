'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase/client'
import DashboardContent from '@/components/dashboard/DashboardContent'
import { User } from '@supabase/supabase-js'

export default function DashboardPage() {
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    console.log('Dashboard page - Client-side check starting...')
    
    const checkAuth = async () => {
      try {
        console.log('Getting session...')
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        
        console.log('Session check result:', { 
          hasSession: !!session, 
          hasUser: !!session?.user,
          error: sessionError 
        })
        
        if (sessionError) {
          console.error('Session error:', sessionError)
          setError('Session error: ' + sessionError.message)
          router.push('/auth/login')
          return
        }
        
        if (!session || !session.user) {
          console.log('No session found, redirecting to login')
          router.push('/auth/login')
          return
        }
        
        console.log('User found:', session.user.id)
        setUser(session.user)
      } catch (error) {
        console.error('Auth check error:', error)
        setError('Auth check failed: ' + (error as Error).message)
        router.push('/auth/login')
      } finally {
        setLoading(false)
      }
    }
    
    checkAuth()
  }, [router])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-white">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-400 mb-4">{error}</p>
          <button 
            onClick={() => router.push('/auth/login')}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
          >
            Back to Login
          </button>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <DashboardContent 
      user={{
        id: user.id,
        email: user.email || '',
        user_metadata: user.user_metadata
      }}
    />
  )
} 
'use client'

import { useState } from 'react'
import { 
  BarChart3, 
  <PERSON><PERSON>dingUp, 
  MoreHorizontal,
  Settings as SettingsIcon,
  Bell,
  Home,
  PieChart,
  Wallet as WalletIcon,
  History as HistoryIcon,
  Target,
  Shield,
  HelpCircle,
  Plus,
  RefreshCw,
  Clock
} from 'lucide-react'
import { Logo } from '@/components/ui/Logo'
import { EliteText } from '@/components/ui/EliteText'

// Import all dashboard components
import DashboardOverview from './DashboardOverview'
import Portfolio from './Portfolio'
import WalletComponent from './Wallet'
import HistoryComponent from './History'
import Analytics from './Analytics'
import RiskManagement from './RiskManagement'
import SettingsComponent from './Settings'
import Help from './Help'

interface DashboardProps {
  user: {
    id: string
    email: string
    user_metadata?: {
      full_name?: string
      first_name?: string
      last_name?: string
    }
  }
}

const TradingChart = () => {
  const [timeframe, setTimeframe] = useState('1H')

  // Generate realistic candlestick data
  const generateCandlestickData = () => {
    const data = []
    let basePrice = 1.0845
    
    for (let i = 0; i < 50; i++) {
      const variation = (Math.random() - 0.5) * 0.002
      const open = basePrice
      const close = basePrice + variation
      const high = Math.max(open, close) + Math.random() * 0.001
      const low = Math.min(open, close) - Math.random() * 0.001
      
      data.push({
        x: i * 20,
        open,
        high,
        low,
        close,
        bullish: close > open
      })
      
      basePrice = close
    }
    return data
  }

  const candlestickData = generateCandlestickData()

  return (
    <div className="glass-card p-6 h-full">
      {/* Chart Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <h3 className="text-xl font-bold text-primary-content">EUR/USD</h3>
          <div className="flex items-center space-x-2">
            <span className="text-2xl font-bold text-green-400">1.0862</span>
            <span className="text-green-400 text-sm">+0.0017 (+0.16%)</span>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Timeframe Selector */}
          <div className="flex items-center space-x-1 bg-slate-800 rounded-lg p-1">
            {['1M', '5M', '15M', '1H', '4H', '1D'].map((tf) => (
              <button
                key={tf}
                onClick={() => setTimeframe(tf)}
                className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                  timeframe === tf 
                    ? 'bg-green-500 text-white' 
                    : 'text-secondary-content hover:text-primary-content'
                }`}
              >
                {tf}
              </button>
            ))}
          </div>
          
          {/* Chart Tools */}
          <div className="flex items-center space-x-2">
            <button className="p-2 rounded-lg hover:bg-slate-700 transition-colors">
              <TrendingUp className="h-4 w-4 text-secondary-content" />
            </button>
            <button className="p-2 rounded-lg hover:bg-slate-700 transition-colors">
              <Target className="h-4 w-4 text-secondary-content" />
            </button>
            <button className="p-2 rounded-lg hover:bg-slate-700 transition-colors">
              <RefreshCw className="h-4 w-4 text-secondary-content" />
            </button>
          </div>
        </div>
      </div>

      {/* Chart Canvas */}
      <div className="h-96 relative bg-slate-900 rounded-lg overflow-hidden">
        <svg className="w-full h-full" viewBox="0 0 1000 400">
          <defs>
            <linearGradient id="priceGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgba(34, 197, 94, 0.2)" />
              <stop offset="100%" stopColor="rgba(34, 197, 94, 0.02)" />
            </linearGradient>
          </defs>
          
          {/* Grid */}
          {[...Array(8)].map((_, i) => (
            <line
              key={`h-${i}`}
              x1="0"
              y1={i * 50}
              x2="1000"
              y2={i * 50}
              stroke="rgba(148, 163, 184, 0.1)"
              strokeWidth="1"
            />
          ))}
          
          {[...Array(10)].map((_, i) => (
            <line
              key={`v-${i}`}
              x1={i * 100}
              y1="0"
              x2={i * 100}
              y2="400"
              stroke="rgba(148, 163, 184, 0.1)"
              strokeWidth="1"
            />
          ))}
          
          {/* Candlesticks */}
          {candlestickData.map((candle, i) => {
            const yScale = 2000
            const yOffset = 200
            
            const openY = yOffset - (candle.open - 1.0800) * yScale
            const closeY = yOffset - (candle.close - 1.0800) * yScale
            const highY = yOffset - (candle.high - 1.0800) * yScale
            const lowY = yOffset - (candle.low - 1.0800) * yScale
            
            return (
              <g key={i}>
                {/* Wick */}
                <line
                  x1={candle.x}
                  y1={highY}
                  x2={candle.x}
                  y2={lowY}
                  stroke={candle.bullish ? '#22c55e' : '#ef4444'}
                  strokeWidth="1"
                />
                {/* Body */}
                <rect
                  x={candle.x - 8}
                  y={Math.min(openY, closeY)}
                  width="16"
                  height={Math.abs(closeY - openY)}
                  fill={candle.bullish ? '#22c55e' : '#ef4444'}
                  opacity="0.8"
                />
              </g>
            )
          })}
        </svg>
        
        {/* Price Line */}
        <div className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-green-500 text-white px-2 py-1 rounded text-xs font-mono">
          1.0862
        </div>
      </div>

      {/* Chart Tools */}
      <div className="mt-4 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button className="flex items-center space-x-2 px-3 py-1 bg-slate-800 rounded-lg text-sm hover:bg-slate-700 transition-colors">
            <Plus className="h-4 w-4" />
            <span>Add Indicator</span>
          </button>
          <button className="flex items-center space-x-2 px-3 py-1 bg-slate-800 rounded-lg text-sm hover:bg-slate-700 transition-colors">
            <Target className="h-4 w-4" />
            <span>Drawing Tools</span>
          </button>
        </div>
        
        <div className="flex items-center space-x-2 text-xs text-secondary-content">
          <Clock className="h-4 w-4" />
          <span>Last updated: 2 seconds ago</span>
        </div>
      </div>
    </div>
  )
}

const OrderEntry = () => {
  const [orderType, setOrderType] = useState('market')
  const [orderSide, setOrderSide] = useState('buy')
  const [volume, setVolume] = useState('0.1')
  const [stopLoss, setStopLoss] = useState('')
  const [takeProfit, setTakeProfit] = useState('')

  return (
    <div className="glass-card p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-primary-content">Place Order</h3>
        <div className="live-indicator">
          <div className="live-dot" />
          <span className="text-xs text-green-400">LIVE</span>
        </div>
      </div>

      <div className="space-y-4">
        {/* Order Type */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-secondary-content">Order Type</label>
          <div className="flex space-x-1 bg-slate-800 rounded-lg p-1">
            {['market', 'limit', 'stop'].map((type) => (
              <button
                key={type}
                onClick={() => setOrderType(type)}
                className={`flex-1 px-3 py-2 rounded text-sm font-medium transition-colors capitalize ${
                  orderType === type 
                    ? 'bg-green-500 text-white' 
                    : 'text-secondary-content hover:text-primary-content'
                }`}
              >
                {type}
              </button>
            ))}
          </div>
        </div>

        {/* Buy/Sell Toggle */}
        <div className="grid grid-cols-2 gap-3">
          <button 
            onClick={() => setOrderSide('buy')}
            className={`py-3 px-4 rounded-xl font-semibold transition-colors ${
              orderSide === 'buy' 
                ? 'buy-button' 
                : 'bg-slate-700 text-secondary-content hover:bg-slate-600'
            }`}
          >
            BUY 1.0862
          </button>
          <button 
            onClick={() => setOrderSide('sell')}
            className={`py-3 px-4 rounded-xl font-semibold transition-colors ${
              orderSide === 'sell' 
                ? 'sell-button' 
                : 'bg-slate-700 text-secondary-content hover:bg-slate-600'
            }`}
          >
            SELL 1.0860
          </button>
        </div>

        {/* Volume */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-secondary-content">Quantity (Lots)</label>
          <input
            type="number"
            value={volume}
            onChange={(e) => setVolume(e.target.value)}
            className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-primary-content focus:border-green-500 focus:outline-none"
            step="0.01"
            min="0.01"
          />
        </div>

        {/* Stop Loss */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-secondary-content">Stop Loss</label>
          <input
            type="number"
            value={stopLoss}
            onChange={(e) => setStopLoss(e.target.value)}
            placeholder="Optional"
            className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-primary-content focus:border-green-500 focus:outline-none"
            step="0.00001"
          />
        </div>

        {/* Take Profit */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-secondary-content">Take Profit</label>
          <input
            type="number"
            value={takeProfit}
            onChange={(e) => setTakeProfit(e.target.value)}
            placeholder="Optional"
            className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-primary-content focus:border-green-500 focus:outline-none"
            step="0.00001"
          />
        </div>

        {/* Submit Button */}
        <button className={`w-full py-3 px-4 rounded-xl font-semibold transition-colors ${
          orderSide === 'buy' ? 'buy-button' : 'sell-button'
        }`}>
          {orderSide === 'buy' ? 'BUY' : 'SELL'} EUR/USD
        </button>

        {/* Risk Info */}
        <div className="bg-slate-800/60 rounded-lg p-3 text-sm">
          <div className="flex justify-between mb-1">
            <span className="text-secondary-content">Margin Required:</span>
            <span className="text-primary-content">$500.62</span>
          </div>
          <div className="flex justify-between mb-1">
            <span className="text-secondary-content">Pip Value:</span>
            <span className="text-primary-content">$1.00</span>
          </div>
          <div className="flex justify-between">
            <span className="text-secondary-content">Spread:</span>
            <span className="text-primary-content">0.2 pips</span>
          </div>
        </div>
      </div>
    </div>
  )
}

const PositionsPanel = () => {
  const [activeTab, setActiveTab] = useState('positions')

  const openPositions = [
    {
      id: 1,
      symbol: 'EUR/USD',
      type: 'BUY',
      lots: 0.1,
      openPrice: 1.0845,
      currentPrice: 1.0862,
      pnl: 17.00,
      pnlPercent: 1.57,
      swap: -0.25,
      time: '14:32:15'
    },
    {
      id: 2,
      symbol: 'GBP/USD',
      type: 'SELL',
      lots: 0.05,
      openPrice: 1.2634,
      currentPrice: 1.2621,
      pnl: 6.50,
      pnlPercent: 0.52,
      swap: 0.15,
      time: '13:45:22'
    }
  ]

  const pendingOrders = [
    {
      id: 1,
      symbol: 'USD/JPY',
      type: 'BUY LIMIT',
      lots: 0.1,
      orderPrice: 149.50,
      currentPrice: 149.82,
      time: '12:15:30'
    }
  ]

  const tradeHistory = [
    {
      id: 1,
      symbol: 'EUR/USD',
      type: 'BUY',
      lots: 0.1,
      openPrice: 1.0823,
      closePrice: 1.0845,
      pnl: 22.00,
      time: '2024-01-15 11:30:15',
      duration: '2h 15m'
    },
    {
      id: 2,
      symbol: 'GBP/USD',
      type: 'SELL',
      lots: 0.15,
      openPrice: 1.2650,
      closePrice: 1.2634,
      pnl: 24.00,
      time: '2024-01-15 09:45:22',
      duration: '1h 32m'
    }
  ]

  return (
    <div className="glass-card p-6">
      {/* Tabs */}
      <div className="flex space-x-1 mb-6 bg-slate-800 rounded-lg p-1">
        {[
          { id: 'positions', label: 'Positions' },
          { id: 'orders', label: 'Orders' },
          { id: 'history', label: 'History' }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeTab === tab.id 
                ? 'bg-green-500 text-white' 
                : 'text-secondary-content hover:text-primary-content'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Open Positions */}
      {activeTab === 'positions' && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-semibold text-primary-content">Open Positions</h4>
            <span className="text-sm text-secondary-content">{openPositions.length} positions</span>
          </div>
          
          {openPositions.map((position) => (
            <div key={position.id} className="bg-slate-800 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-3">
                  <span className="font-mono font-bold text-primary-content">{position.symbol}</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    position.type === 'BUY' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                  }`}>
                    {position.type}
                  </span>
                  <span className="text-sm text-secondary-content">{position.lots} lots</span>
                </div>
                <button className="p-1 rounded hover:bg-slate-700 transition-colors">
                  <MoreHorizontal className="h-4 w-4 text-secondary-content" />
                </button>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-secondary-content">Open: </span>
                  <span className="text-primary-content font-mono">{position.openPrice}</span>
                </div>
                <div>
                  <span className="text-secondary-content">Current: </span>
                  <span className="text-primary-content font-mono">{position.currentPrice}</span>
                </div>
                <div>
                  <span className="text-secondary-content">P&L: </span>
                  <span className={position.pnl >= 0 ? 'text-green-400' : 'text-red-400'}>
                    ${position.pnl.toFixed(2)} ({position.pnlPercent >= 0 ? '+' : ''}{position.pnlPercent}%)
                  </span>
                </div>
                <div>
                  <span className="text-secondary-content">Time: </span>
                  <span className="text-primary-content">{position.time}</span>
                </div>
              </div>
              
              <div className="flex space-x-2 mt-3">
                <button className="flex-1 bg-red-500 hover:bg-red-600 text-white py-2 rounded text-sm font-medium transition-colors">
                  Close
                </button>
                <button className="px-4 bg-slate-700 hover:bg-slate-600 text-secondary-content py-2 rounded text-sm transition-colors">
                  Modify
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pending Orders */}
      {activeTab === 'orders' && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-semibold text-primary-content">Pending Orders</h4>
            <span className="text-sm text-secondary-content">{pendingOrders.length} orders</span>
          </div>
          
          {pendingOrders.map((order) => (
            <div key={order.id} className="bg-slate-800 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-3">
                  <span className="font-mono font-bold text-primary-content">{order.symbol}</span>
                  <span className="px-2 py-1 rounded text-xs font-medium bg-blue-500/20 text-blue-400">
                    {order.type}
                  </span>
                  <span className="text-sm text-secondary-content">{order.lots} lots</span>
                </div>
                <button className="p-1 rounded hover:bg-slate-700 transition-colors">
                  <MoreHorizontal className="h-4 w-4 text-secondary-content" />
                </button>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-secondary-content">Order Price: </span>
                  <span className="text-primary-content font-mono">{order.orderPrice}</span>
                </div>
                <div>
                  <span className="text-secondary-content">Current: </span>
                  <span className="text-primary-content font-mono">{order.currentPrice}</span>
                </div>
                <div className="col-span-2">
                  <span className="text-secondary-content">Time: </span>
                  <span className="text-primary-content">{order.time}</span>
                </div>
              </div>
              
              <div className="flex space-x-2 mt-3">
                <button className="flex-1 bg-red-500 hover:bg-red-600 text-white py-2 rounded text-sm font-medium transition-colors">
                  Cancel
                </button>
                <button className="px-4 bg-slate-700 hover:bg-slate-600 text-secondary-content py-2 rounded text-sm transition-colors">
                  Modify
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Trade History */}
      {activeTab === 'history' && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-semibold text-primary-content">Recent Trades</h4>
            <span className="text-sm text-secondary-content">{tradeHistory.length} trades</span>
          </div>
          
          {tradeHistory.map((trade) => (
            <div key={trade.id} className="bg-slate-800 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-3">
                  <span className="font-mono font-bold text-primary-content">{trade.symbol}</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    trade.type === 'BUY' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                  }`}>
                    {trade.type}
                  </span>
                  <span className="text-sm text-secondary-content">{trade.lots} lots</span>
                </div>
                <span className={`font-semibold ${trade.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  ${trade.pnl.toFixed(2)}
                </span>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-secondary-content">Open: </span>
                  <span className="text-primary-content font-mono">{trade.openPrice}</span>
                </div>
                <div>
                  <span className="text-secondary-content">Close: </span>
                  <span className="text-primary-content font-mono">{trade.closePrice}</span>
                </div>
                <div>
                  <span className="text-secondary-content">Duration: </span>
                  <span className="text-primary-content">{trade.duration}</span>
                </div>
                <div>
                  <span className="text-secondary-content">Time: </span>
                  <span className="text-primary-content">{trade.time}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

function DashboardContent({ user }: DashboardProps) {
  const [activeSection, setActiveSection] = useState('dashboard')
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Home },
    { id: 'trading', label: 'Trading', icon: TrendingUp },
    { id: 'portfolio', label: 'Portfolio', icon: PieChart },
    { id: 'wallet', label: 'Wallet', icon: WalletIcon },
    { id: 'history', label: 'History', icon: HistoryIcon },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'risk', label: 'Risk Management', icon: Shield },
    { id: 'settings', label: 'Settings', icon: SettingsIcon },
    { id: 'help', label: 'Help', icon: HelpCircle }
  ]

  const accountStats = [
    { label: 'Balance', value: '$127,420.50', change: '+$2,847.30', positive: true },
    { label: 'Equity', value: '$127,443.50', change: '+$2,870.30', positive: true },
    { label: 'Free Margin', value: '$126,891.50', change: '', positive: true },
    { label: 'Margin Level', value: '23,456%', change: '', positive: true }
  ]

  // Function to render the appropriate component based on activeSection
  const renderSectionContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return <DashboardOverview />
      case 'trading':
        return (
          <div className="flex-1 p-6 overflow-auto">
            <div className="grid grid-cols-12 gap-6 h-full">
              {/* Chart Area */}
              <div className="col-span-8">
                <TradingChart />
              </div>

              {/* Right Panel */}
              <div className="col-span-4 space-y-6">
                {/* Order Entry */}
                <OrderEntry />
              </div>

              {/* Bottom Panel */}
              <div className="col-span-12">
                <PositionsPanel />
              </div>
            </div>
          </div>
        )
      case 'portfolio':
        return <Portfolio />
      case 'wallet':
        return <WalletComponent />
      case 'history':
        return <HistoryComponent />
      case 'analytics':
        return <Analytics />
      case 'risk':
        return <RiskManagement />
      case 'settings':
        return <SettingsComponent />
      case 'help':
        return <Help />
      default:
        return <DashboardOverview />
    }
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Professional Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black">
        <div className="absolute inset-0 bg-gradient-to-r from-green-500/2 via-transparent to-blue-500/2" />
      </div>

      <div className="relative z-10 flex h-screen">
        {/* Left Sidebar */}
        <div className={`${sidebarCollapsed ? 'w-16' : 'w-64'} bg-gray-900/95 backdrop-blur-xl border-r border-slate-700/50 transition-all duration-300 relative`}>
          <div className="p-4">
            {/* Logo - Now clickable to toggle sidebar */}
            <button 
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className={`flex items-center mb-8 w-full hover:bg-slate-800/60 p-2 rounded-lg transition-colors duration-200 ${
                sidebarCollapsed ? 'justify-center' : 'space-x-3'
              }`}
              title={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            >
              {/* Prop Bully Logo - will use PNG if available */}
              <Logo 
                size="sm" 
                imageSrc="/images/logos/prop-bully-logo.png"
                alt="Prop Bully Logo"
              />
              {!sidebarCollapsed && (
                <div className="flex flex-col">
                  <EliteText size="md" variant="gold">Prop Bully</EliteText>
                  <span className="text-xs text-slate-400">Trading Platform</span>
                </div>
              )}
            </button>

            {/* User Info */}
            {!sidebarCollapsed && (
              <div className="mb-8 p-4 glass-card rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold">
                      {(user.user_metadata?.first_name || user.email)?.[0]?.toUpperCase()}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-semibold text-primary-content truncate">
                      {user.user_metadata?.full_name || user.user_metadata?.first_name || 'Trader'}
                    </div>
                    <div className="text-xs text-green-400">Active Trader</div>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation */}
            <nav className="space-y-2">
              {navItems.map((item) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveSection(item.id)}
                    className={`w-full flex items-center px-3 py-3 rounded-lg transition-all duration-200 ${
                      activeSection === item.id
                        ? 'bg-green-500 text-white shadow-lg transform scale-[1.02]'
                        : 'text-secondary-content hover:text-primary-content hover:bg-slate-800/60'
                    } ${sidebarCollapsed ? 'justify-center' : 'space-x-3'}`}
                    title={sidebarCollapsed ? item.label : ''}
                  >
                    <Icon className="h-5 w-5 flex-shrink-0" />
                    {!sidebarCollapsed && <span className="font-medium">{item.label}</span>}
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Top Header */}
          <header className="h-16 bg-gray-900/95 backdrop-blur-xl border-b border-slate-700/50 flex items-center justify-between px-6">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-bold text-primary-content capitalize">{activeSection}</h1>
              <div className="live-indicator">
                <div className="live-dot" />
                <span className="text-sm font-medium text-green-400">Markets Open</span>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Account Stats */}
              <div className="hidden md:flex items-center space-x-6">
                {accountStats.map((stat, index) => (
                  <div key={index} className="text-right">
                    <div className="text-xs text-secondary-content">{stat.label}</div>
                    <div className="font-bold text-primary-content">{stat.value}</div>
                    {stat.change && (
                      <div className={`text-xs ${stat.positive ? 'text-green-400' : 'text-red-400'}`}>
                        {stat.change}
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <div className="flex items-center space-x-2">
                <button className="p-2 rounded-lg hover:bg-slate-700 transition-colors">
                  <Bell className="h-5 w-5 text-secondary-content" />
                </button>
                <button className="p-2 rounded-lg hover:bg-slate-700 transition-colors">
                  <SettingsIcon className="h-5 w-5 text-secondary-content" />
                </button>
              </div>
            </div>
          </header>

          {/* Dynamic Content */}
          <div className="flex-1 overflow-auto">
            {activeSection === 'trading' ? (
              renderSectionContent()
            ) : (
              <div className="p-6">
                {renderSectionContent()}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// Export both as default and named for flexibility
export default DashboardContent
export { DashboardContent } 
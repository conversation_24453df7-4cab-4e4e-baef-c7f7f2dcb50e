'use client'

import { useState } from 'react'
import { 
  Wallet as WalletIcon, 
  CreditCard, 
  ArrowUpRight, 
  ArrowDownLeft,
  Plus,
  Minus,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye,
  EyeOff,
  RefreshCw,
  Download,
  DollarSign,
  TrendingUp
} from 'lucide-react'

interface Transaction {
  id: string
  type: 'deposit' | 'withdrawal' | 'transfer' | 'fee' | 'profit'
  amount: number
  currency: string
  status: 'completed' | 'pending' | 'failed' | 'processing'
  description: string
  method?: string
  date: string
  fee?: number
}

interface PaymentMethod {
  id: string
  type: 'card' | 'bank' | 'crypto' | 'paypal'
  name: string
  details: string
  isDefault: boolean
  status: 'active' | 'expired' | 'pending'
}

const Wallet = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'transactions' | 'methods'>('overview')
  const [showBalance, setShowBalance] = useState(true)
  const [selectedFilter, setSelectedFilter] = useState('all')

  const walletSummary = {
    totalBalance: 127420.50,
    availableBalance: 115680.25,
    pendingBalance: 11740.25,
    monthlyProfit: 8750.80,
    monthlyProfitPercent: 7.36
  }

  const transactions: Transaction[] = [
    {
      id: '1',
      type: 'deposit',
      amount: 10000,
      currency: 'USD',
      status: 'completed',
      description: 'Challenge Fee Payment',
      method: 'Visa •••• 4532',
      date: '2024-01-15 14:30:15',
      fee: 0
    },
    {
      id: '2',
      type: 'profit',
      amount: 1234.50,
      currency: 'USD',
      status: 'completed',
      description: 'Trading Profit - EUR/USD',
      date: '2024-01-15 11:45:22'
    },
    {
      id: '3',
      type: 'withdrawal',
      amount: 2500,
      currency: 'USD',
      status: 'processing',
      description: 'Profit Withdrawal',
      method: 'Bank Transfer',
      date: '2024-01-14 16:20:08',
      fee: 25
    },
    {
      id: '4',
      type: 'fee',
      amount: 15,
      currency: 'USD',
      status: 'completed',
      description: 'Platform Fee',
      date: '2024-01-14 09:15:33'
    },
    {
      id: '5',
      type: 'deposit',
      amount: 5000,
      currency: 'USD',
      status: 'completed',
      description: 'Account Funding',
      method: 'Crypto - BTC',
      date: '2024-01-13 20:45:12',
      fee: 50
    }
  ]

  const paymentMethods: PaymentMethod[] = [
    {
      id: '1',
      type: 'card',
      name: 'Visa Debit',
      details: '•••• •••• •••• 4532',
      isDefault: true,
      status: 'active'
    },
    {
      id: '2',
      type: 'bank',
      name: 'Bank Account',
      details: 'Chase Bank •••• 8901',
      isDefault: false,
      status: 'active'
    },
    {
      id: '3',
      type: 'crypto',
      name: 'Bitcoin Wallet',
      details: '******************************************',
      isDefault: false,
      status: 'active'
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'pending':
      case 'processing':
        return <Clock className="h-4 w-4 text-yellow-400" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-400" />
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-400" />
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'deposit':
        return <ArrowDownLeft className="h-4 w-4 text-green-400" />
      case 'withdrawal':
        return <ArrowUpRight className="h-4 w-4 text-blue-400" />
      case 'profit':
        return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'fee':
        return <Minus className="h-4 w-4 text-red-400" />
      default:
        return <DollarSign className="h-4 w-4 text-gray-400" />
    }
  }

  const filteredTransactions = transactions.filter(transaction => {
    if (selectedFilter === 'all') return true
    return transaction.type === selectedFilter
  })

  return (
    <div className="space-y-6">
      {/* Wallet Overview */}
      <div className="glass-card p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-primary-content">Wallet Overview</h2>
            <p className="text-secondary-content">Manage your funds and transactions</p>
          </div>
          <div className="flex items-center space-x-2">
            <button 
              onClick={() => setShowBalance(!showBalance)}
              className="glass-card p-2 hover:bg-slate-700 transition-colors"
            >
              {showBalance ? (
                <Eye className="h-5 w-5 text-secondary-content" />
              ) : (
                <EyeOff className="h-5 w-5 text-secondary-content" />
              )}
            </button>
            <button className="glass-card p-2 hover:bg-slate-700 transition-colors">
              <RefreshCw className="h-5 w-5 text-secondary-content" />
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-gradient-to-br from-green-500/20 to-green-600/20 rounded-xl p-6 border border-green-500/30">
            <div className="flex items-center justify-between mb-4">
              <WalletIcon className="h-8 w-8 text-green-400" />
              <span className="text-xs text-green-400 bg-green-400/10 px-2 py-1 rounded">TOTAL</span>
            </div>
            <div className="text-3xl font-bold text-primary-content mb-1">
              {showBalance ? `$${walletSummary.totalBalance.toLocaleString()}` : '••••••'}
            </div>
            <div className="text-sm text-secondary-content">Total Balance</div>
          </div>

          <div className="bg-slate-800/60 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <CheckCircle className="h-8 w-8 text-blue-400" />
              <span className="text-xs text-blue-400 bg-blue-400/10 px-2 py-1 rounded">AVAILABLE</span>
            </div>
            <div className="text-2xl font-bold text-primary-content mb-1">
              {showBalance ? `$${walletSummary.availableBalance.toLocaleString()}` : '••••••'}
            </div>
            <div className="text-sm text-secondary-content">Available</div>
          </div>

          <div className="bg-slate-800/60 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <Clock className="h-8 w-8 text-yellow-400" />
              <span className="text-xs text-yellow-400 bg-yellow-400/10 px-2 py-1 rounded">PENDING</span>
            </div>
            <div className="text-2xl font-bold text-primary-content mb-1">
              {showBalance ? `$${walletSummary.pendingBalance.toLocaleString()}` : '••••••'}
            </div>
            <div className="text-sm text-secondary-content">Pending</div>
          </div>

          <div className="bg-slate-800/60 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <TrendingUp className="h-8 w-8 text-green-400" />
              <span className="text-xs text-green-400 bg-green-400/10 px-2 py-1 rounded">+{walletSummary.monthlyProfitPercent}%</span>
            </div>
            <div className="text-2xl font-bold text-green-400 mb-1">
              {showBalance ? `+$${walletSummary.monthlyProfit.toLocaleString()}` : '••••••'}
            </div>
            <div className="text-sm text-secondary-content">This Month</div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <button className="glass-card p-6 hover:bg-slate-800/60 transition-all duration-300 group">
          <div className="flex items-center space-x-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-green-500/10 group-hover:bg-green-500/20 transition-colors">
              <Plus className="h-6 w-6 text-green-400" />
            </div>
            <div className="text-left">
              <div className="font-bold text-primary-content">Deposit Funds</div>
              <div className="text-sm text-secondary-content">Add money to your account</div>
            </div>
          </div>
        </button>

        <button className="glass-card p-6 hover:bg-slate-800/60 transition-all duration-300 group">
          <div className="flex items-center space-x-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-blue-500/10 group-hover:bg-blue-500/20 transition-colors">
              <ArrowUpRight className="h-6 w-6 text-blue-400" />
            </div>
            <div className="text-left">
              <div className="font-bold text-primary-content">Withdraw</div>
              <div className="text-sm text-secondary-content">Transfer to your bank</div>
            </div>
          </div>
        </button>

        <button className="glass-card p-6 hover:bg-slate-800/60 transition-all duration-300 group">
          <div className="flex items-center space-x-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-purple-500/10 group-hover:bg-purple-500/20 transition-colors">
              <CreditCard className="h-6 w-6 text-purple-400" />
            </div>
            <div className="text-left">
              <div className="font-bold text-primary-content">Payment Methods</div>
              <div className="text-sm text-secondary-content">Manage your cards & banks</div>
            </div>
          </div>
        </button>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-slate-800 rounded-lg p-1 w-fit">
        {[
          { id: 'overview' as const, label: 'Overview' },
          { id: 'transactions' as const, label: 'Transactions' },
          { id: 'methods' as const, label: 'Payment Methods' }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeTab === tab.id 
                ? 'bg-green-500 text-white' 
                : 'text-secondary-content hover:text-primary-content'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content based on active tab */}
      {activeTab === 'transactions' && (
        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-primary-content">Transaction History</h3>
            <div className="flex items-center space-x-2">
              <select 
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
                className="bg-slate-800 text-primary-content px-3 py-2 rounded-lg text-sm border border-slate-600 focus:border-green-500 focus:outline-none"
              >
                <option value="all">All Transactions</option>
                <option value="deposit">Deposits</option>
                <option value="withdrawal">Withdrawals</option>
                <option value="profit">Profits</option>
                <option value="fee">Fees</option>
              </select>
              <button className="glass-card p-2 hover:bg-slate-700 transition-colors">
                <Download className="h-5 w-5 text-secondary-content" />
              </button>
            </div>
          </div>

          <div className="space-y-3">
            {filteredTransactions.map((transaction) => (
              <div key={transaction.id} className="bg-slate-800/60 rounded-lg p-4 hover:bg-slate-700/60 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-slate-700">
                      {getTypeIcon(transaction.type)}
                    </div>
                    <div>
                      <div className="font-semibold text-primary-content">{transaction.description}</div>
                      <div className="text-sm text-secondary-content">
                        {transaction.date} {transaction.method && `• ${transaction.method}`}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center space-x-2">
                      <div className={`font-bold ${
                        transaction.type === 'deposit' || transaction.type === 'profit' 
                          ? 'text-green-400' 
                          : 'text-red-400'
                      }`}>
                        {transaction.type === 'deposit' || transaction.type === 'profit' ? '+' : '-'}
                        ${transaction.amount.toLocaleString()}
                      </div>
                      {getStatusIcon(transaction.status)}
                    </div>
                    {transaction.fee && (
                      <div className="text-xs text-secondary-content">
                        Fee: ${transaction.fee}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'methods' && (
        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-primary-content">Payment Methods</h3>
            <button className="buy-button py-2 px-4 rounded-lg font-semibold flex items-center space-x-2">
              <Plus className="h-4 w-4" />
              <span>Add Method</span>
            </button>
          </div>

          <div className="space-y-4">
            {paymentMethods.map((method) => (
              <div key={method.id} className="bg-slate-800/60 rounded-lg p-4 hover:bg-slate-700/60 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-slate-700">
                      <CreditCard className="h-6 w-6 text-secondary-content" />
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-semibold text-primary-content">{method.name}</span>
                        {method.isDefault && (
                          <span className="text-xs text-green-400 bg-green-400/10 px-2 py-1 rounded">DEFAULT</span>
                        )}
                      </div>
                      <div className="text-sm text-secondary-content">{method.details}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span className={`text-xs px-2 py-1 rounded ${
                      method.status === 'active' 
                        ? 'text-green-400 bg-green-400/10'
                        : method.status === 'expired'
                        ? 'text-red-400 bg-red-400/10'
                        : 'text-yellow-400 bg-yellow-400/10'
                    }`}>
                      {method.status.toUpperCase()}
                    </span>
                    <button className="text-secondary-content hover:text-primary-content p-1">
                      <span className="text-sm">Edit</span>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Transactions */}
          <div className="glass-card p-6">
            <h3 className="text-xl font-bold text-primary-content mb-6">Recent Activity</h3>
            <div className="space-y-3">
              {transactions.slice(0, 5).map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-3 bg-slate-800/60 rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getTypeIcon(transaction.type)}
                    <div>
                      <div className="text-sm font-medium text-primary-content">{transaction.description}</div>
                      <div className="text-xs text-secondary-content">{transaction.date}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`text-sm font-bold ${
                      transaction.type === 'deposit' || transaction.type === 'profit' 
                        ? 'text-green-400' 
                        : 'text-red-400'
                    }`}>
                      {transaction.type === 'deposit' || transaction.type === 'profit' ? '+' : '-'}
                      ${transaction.amount.toLocaleString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Spending Analytics */}
          <div className="glass-card p-6">
            <h3 className="text-xl font-bold text-primary-content mb-6">Monthly Summary</h3>
            <div className="space-y-4">
              <div className="bg-slate-800/60 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-secondary-content">Total Deposits</span>
                  <span className="font-bold text-green-400">+$15,000</span>
                </div>
                <div className="w-full bg-slate-700 rounded-full h-2">
                  <div className="bg-green-400 h-2 rounded-full" style={{ width: '75%' }} />
                </div>
              </div>
              
              <div className="bg-slate-800/60 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-secondary-content">Withdrawals</span>
                  <span className="font-bold text-blue-400">-$2,500</span>
                </div>
                <div className="w-full bg-slate-700 rounded-full h-2">
                  <div className="bg-blue-400 h-2 rounded-full" style={{ width: '25%' }} />
                </div>
              </div>
              
              <div className="bg-slate-800/60 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-secondary-content">Trading Profits</span>
                  <span className="font-bold text-green-400">+$8,750</span>
                </div>
                <div className="w-full bg-slate-700 rounded-full h-2">
                  <div className="bg-green-400 h-2 rounded-full" style={{ width: '65%' }} />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Wallet 
# Backend API Specification - Prop Firm Platform

## Overview
This document outlines all backend APIs, WebSocket connections, authentication systems, payment processing, and MT5 integration required to fully integrate the dashboard components (Analytics, Risk Management, Settings, Help & Support).

## Table of Contents
1. [Authentication & Security](#authentication--security)
2. [Analytics APIs](#analytics-apis)
3. [Risk Management APIs](#risk-management-apis)
4. [User Settings APIs](#user-settings-apis)
5. [Help & Support APIs](#help--support-apis)
6. [WebSocket Connections](#websocket-connections)
7. [Payment Processing](#payment-processing)
8. [MT5 Integration](#mt5-integration)
9. [Database Schema](#database-schema)
10. [Error Handling](#error-handling)

## Authentication & Security

### JWT Token Management
- **Endpoint**: `POST /api/auth/token/refresh`
- **Purpose**: Refresh expired JWT tokens
- **Request**: `{ "refresh_token": "string" }`
- **Response**: `{ "access_token": "string", "refresh_token": "string", "expires_in": number }`

### API Key Management
- **Endpoint**: `POST /api/auth/api-key/generate`
- **Purpose**: Generate API key for external access
- **Headers**: `Authorization: Bearer <jwt_token>`
- **Request**: `{ "name": "string", "permissions": ["read", "trade"] }`
- **Response**: `{ "api_key": "string", "secret": "string", "expires_at": "ISO_date" }`

### Rate Limiting
- **Implementation**: Express rate limiter middleware
- **Limits**: 100 requests/minute per user, 1000 requests/hour per IP
- **Headers**: `X-RateLimit-Remaining`, `X-RateLimit-Reset`

## Analytics APIs

### Performance Metrics
```typescript
// GET /api/analytics/performance/:accountId
interface PerformanceRequest {
  accountId: string
  timeframe?: '1D' | '7D' | '30D' | '90D' | 'ALL'
  startDate?: string
  endDate?: string
}

interface PerformanceResponse {
  totalTrades: number
  winningTrades: number
  losingTrades: number
  winRate: number
  avgWin: number
  avgLoss: number
  profitFactor: number
  sharpeRatio: number
  maxDrawdown: number
  returnOnInvestment: number
  period: {
    start: Date
    end: Date
  }
}
```

### Equity Curve Data
```typescript
// GET /api/analytics/equity-curve/:accountId
interface EquityCurveResponse {
  data: Array<{
    timestamp: Date
    value: number
    balance: number
    equity: number
  }>
  totalReturn: number
  maxDrawdown: number
  volatility: number
}
```

### Daily P&L Analysis
```typescript
// GET /api/analytics/daily-pnl/:accountId
interface DailyPnLResponse {
  data: Array<{
    date: Date
    profit: number
    trades: number
    winRate: number
  }>
  averageDailyReturn: number
  bestDay: { date: Date, profit: number }
  worstDay: { date: Date, profit: number }
}
```

### Trading Hours Analysis
```typescript
// GET /api/analytics/trading-hours/:accountId
interface TradingHoursResponse {
  hourlyStats: Array<{
    hour: string
    trades: number
    profit: number
    winRate: number
  }>
  bestTradingHours: string[]
  worstTradingHours: string[]
}
```

### Symbol Performance Breakdown
```typescript
// GET /api/analytics/symbol-breakdown/:accountId
interface SymbolBreakdownResponse {
  symbols: Array<{
    symbol: string
    trades: number
    profit: number
    winRate: number
    avgHoldTime: number
    volume: number
  }>
  topPerformers: string[]
  worstPerformers: string[]
}
```

## Risk Management APIs

### Current Risk Metrics
```typescript
// GET /api/risk/current-metrics/:accountId
interface RiskMetricsResponse {
  dailyDrawdown: number
  maxDailyDrawdownLimit: number
  totalDrawdown: number
  maxTotalDrawdownLimit: number
  positionSize: number
  maxPositionSizeLimit: number
  openPositions: number
  maxPositionsLimit: number
  marginLevel: number
  freeMargin: number
  equity: number
  balance: number
  riskScore: number // 0-10 scale
  lastUpdate: Date
}
```

### Risk Rules Management
```typescript
// GET /api/risk/rules/:accountId
interface RiskRulesResponse {
  rules: Array<{
    id: string
    name: string
    description: string
    type: 'max_daily_loss' | 'max_total_loss' | 'max_position_size' | 'max_positions'
    value: number
    enabled: boolean
    priority: number
  }>
}

// PUT /api/risk/rules/:ruleId
interface UpdateRiskRuleRequest {
  value?: number
  enabled?: boolean
  priority?: number
}
```

### Risk Alerts
```typescript
// GET /api/risk/alerts/:accountId
interface RiskAlertsResponse {
  alerts: Array<{
    id: string
    type: 'warning' | 'danger' | 'info'
    title: string
    message: string
    timestamp: Date
    acknowledged: boolean
    severity: number // 1-10 scale
  }>
  unreadCount: number
}

// POST /api/risk/alerts/:alertId/acknowledge
interface AcknowledgeAlertRequest {
  note?: string
}
```

### Emergency Actions
```typescript
// POST /api/risk/emergency/close-all/:accountId
interface EmergencyCloseAllRequest {
  reason: string
  confirmationCode: string
}

// POST /api/risk/emergency/pause-trading/:accountId
interface PauseTradingRequest {
  duration?: number // minutes, 0 = indefinite
  reason: string
}
```

## User Settings APIs

### Profile Management
```typescript
// GET /api/users/profile
interface UserProfileResponse {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  country: string
  timezone: string
  language: string
  dateOfBirth: string
  kycStatus: 'pending' | 'verified' | 'rejected'
  accountStatus: 'active' | 'suspended' | 'closed'
  createdAt: Date
  updatedAt: Date
}

// PUT /api/users/profile
interface UpdateProfileRequest {
  firstName?: string
  lastName?: string
  phone?: string
  country?: string
  timezone?: string
  language?: string
}
```

### Trading Preferences
```typescript
// GET /api/users/trading-preferences
interface TradingPreferencesResponse {
  defaultLotSize: number
  maxSlippage: number
  closeOnFriday: boolean
  autoStopLoss: boolean
  defaultStopLoss: number
  defaultTakeProfit: number
  riskPerTrade: number
  tradingHours: {
    start: string
    end: string
  }
  allowedInstruments: string[]
}

// PUT /api/users/trading-preferences
interface UpdateTradingPreferencesRequest {
  defaultLotSize?: number
  maxSlippage?: number
  closeOnFriday?: boolean
  autoStopLoss?: boolean
  defaultStopLoss?: number
  defaultTakeProfit?: number
  riskPerTrade?: number
  tradingHours?: {
    start: string
    end: string
  }
}
```

### Notification Settings
```typescript
// GET /api/users/notification-settings
interface NotificationSettingsResponse {
  email: {
    tradeAlerts: boolean
    riskAlerts: boolean
    marketNews: boolean
    accountUpdates: boolean
    weeklyReports: boolean
  }
  push: {
    tradeAlerts: boolean
    riskAlerts: boolean
    marketNews: boolean
    accountUpdates: boolean
  }
  sms: {
    riskAlerts: boolean
    accountUpdates: boolean
  }
}

// PUT /api/users/notification-settings
interface UpdateNotificationSettingsRequest {
  email?: Partial<NotificationSettingsResponse['email']>
  push?: Partial<NotificationSettingsResponse['push']>
  sms?: Partial<NotificationSettingsResponse['sms']>
}
```

### Security Settings
```typescript
// GET /api/users/security-settings
interface SecuritySettingsResponse {
  twoFactorEnabled: boolean
  loginAlerts: boolean
  sessionTimeout: number
  apiAccess: boolean
  ipWhitelist: string[]
  lastPasswordChange: Date
  activeSessions: Array<{
    id: string
    ipAddress: string
    userAgent: string
    location: string
    lastActivity: Date
    current: boolean
  }>
}

// PUT /api/users/security-settings
interface UpdateSecuritySettingsRequest {
  loginAlerts?: boolean
  sessionTimeout?: number
  apiAccess?: boolean
  ipWhitelist?: string[]
}

// POST /api/users/2fa/enable
interface Enable2FARequest {
  phone?: string
  method: 'sms' | 'app'
}

// POST /api/users/password/change
interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}
```

## Help & Support APIs

### FAQ Management
```typescript
// GET /api/support/faq
interface FAQRequest {
  category?: 'all' | 'challenges' | 'trading' | 'risk' | 'payments' | 'technical'
  search?: string
  limit?: number
  offset?: number
}

interface FAQResponse {
  faqs: Array<{
    id: string
    question: string
    answer: string
    category: string
    helpful: number
    notHelpful: number
    tags: string[]
    lastUpdated: Date
  }>
  total: number
  categories: string[]
}

// POST /api/support/faq/:faqId/helpful
interface FAQFeedbackRequest {
  helpful: boolean
  comment?: string
}
```

### Support Tickets
```typescript
// GET /api/support/tickets
interface SupportTicketsResponse {
  tickets: Array<{
    id: string
    subject: string
    status: 'open' | 'pending' | 'resolved' | 'closed'
    priority: 'low' | 'medium' | 'high' | 'urgent'
    category: string
    createdAt: Date
    lastUpdate: Date
    messagesCount: number
  }>
  total: number
}

// POST /api/support/tickets
interface CreateTicketRequest {
  subject: string
  category: 'general' | 'technical' | 'billing' | 'trading'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  description: string
  attachments?: File[]
}

// GET /api/support/tickets/:ticketId
interface TicketDetailsResponse {
  ticket: {
    id: string
    subject: string
    status: 'open' | 'pending' | 'resolved' | 'closed'
    priority: 'low' | 'medium' | 'high' | 'urgent'
    category: string
    createdAt: Date
    lastUpdate: Date
  }
  messages: Array<{
    id: string
    sender: 'user' | 'support'
    message: string
    timestamp: Date
    attachments: Array<{
      id: string
      filename: string
      url: string
      size: number
    }>
  }>
}
```

### Contact & Guides
```typescript
// POST /api/support/contact
interface ContactFormRequest {
  name: string
  email: string
  subject: string
  message: string
  category: string
}

// GET /api/support/guides
interface GuidesResponse {
  guides: Array<{
    id: string
    title: string
    description: string
    type: 'PDF' | 'Video' | 'Web'
    category: string
    url: string
    downloadUrl?: string
    size?: number
    duration?: number
    lastUpdated: Date
  }>
  categories: string[]
}
```

## WebSocket Connections

### Connection Setup
```typescript
// WebSocket URL: wss://api.propfirm.com/ws
// Authentication: JWT token in query parameter or header

interface WebSocketAuth {
  token: string
  accountId?: string
}
```

### Real-time Streams
```typescript
// Market Data Stream
interface MarketDataEvent {
  type: 'market-data'
  symbol: string
  bid: number
  ask: number
  last: number
  volume: number
  timestamp: Date
}

// Account Updates Stream
interface AccountUpdateEvent {
  type: 'account-update'
  accountId: string
  balance: number
  equity: number
  freeMargin: number
  marginLevel: number
  timestamp: Date
}

// Trade Updates Stream
interface TradeUpdateEvent {
  type: 'trade-update'
  accountId: string
  action: 'opened' | 'modified' | 'closed'
  trade: {
    id: string
    symbol: string
    type: 'buy' | 'sell'
    volume: number
    openPrice: number
    currentPrice?: number
    profit?: number
    stopLoss?: number
    takeProfit?: number
    status: 'open' | 'closed' | 'pending'
  }
  timestamp: Date
}

// Risk Alerts Stream
interface RiskAlertEvent {
  type: 'risk-alert'
  accountId: string
  alert: {
    id: string
    type: 'warning' | 'danger' | 'info'
    title: string
    message: string
    severity: number
  }
  timestamp: Date
}

// Analytics Updates Stream
interface AnalyticsUpdateEvent {
  type: 'analytics-update'
  accountId: string
  metrics: {
    dailyPnL: number
    totalPnL: number
    winRate: number
    drawdown: number
    riskScore: number
  }
  timestamp: Date
}
```

## Payment Processing (Stripe Integration)

### Payment Intents
```typescript
// POST /api/payments/create-intent
interface CreatePaymentIntentRequest {
  challengeId: string
  amount: number
  currency: string
  paymentMethod?: string
}

interface CreatePaymentIntentResponse {
  clientSecret: string
  paymentIntentId: string
  amount: number
  currency: string
}

// POST /api/payments/confirm
interface ConfirmPaymentRequest {
  paymentIntentId: string
  paymentMethodId: string
}
```

### Payment History
```typescript
// GET /api/payments/history
interface PaymentHistoryResponse {
  payments: Array<{
    id: string
    amount: number
    currency: string
    status: 'pending' | 'succeeded' | 'failed' | 'refunded'
    description: string
    challengeId?: string
    accountId?: string
    createdAt: Date
    processedAt?: Date
  }>
  total: number
}
```

### Webhooks
```typescript
// POST /api/webhooks/stripe
interface StripeWebhookEvent {
  type: string
  data: {
    object: any
  }
}
```

## MT5 Integration

### Account Management
```typescript
// POST /api/mt5/accounts/create
interface CreateMT5AccountRequest {
  challengeId: string
  accountType: 'demo' | 'live'
  leverage: number
  currency: string
}

interface CreateMT5AccountResponse {
  login: string
  password: string
  server: string
  balance: number
  currency: string
}

// GET /api/mt5/accounts/:login/info
interface MT5AccountInfoResponse {
  login: string
  balance: number
  equity: number
  margin: number
  freeMargin: number
  marginLevel: number
  profit: number
  currency: string
  leverage: number
  server: string
  lastUpdate: Date
}
```

### Trading Operations
```typescript
// POST /api/mt5/trades/open
interface OpenTradeRequest {
  accountLogin: string
  symbol: string
  type: 'buy' | 'sell' | 'buy_limit' | 'sell_limit' | 'buy_stop' | 'sell_stop'
  volume: number
  price?: number
  stopLoss?: number
  takeProfit?: number
  comment?: string
}

interface OpenTradeResponse {
  ticket: number
  symbol: string
  type: string
  volume: number
  openPrice: number
  stopLoss?: number
  takeProfit?: number
  openTime: Date
}

// PUT /api/mt5/trades/:ticket/modify
interface ModifyTradeRequest {
  stopLoss?: number
  takeProfit?: number
  price?: number
}

// DELETE /api/mt5/trades/:ticket/close
interface CloseTradeRequest {
  volume?: number
  price?: number
}
```

### Market Data
```typescript
// GET /api/mt5/market-data/:symbol
interface MarketDataResponse {
  symbol: string
  bid: number
  ask: number
  last: number
  volume: number
  high: number
  low: number
  change: number
  changePercent: number
  timestamp: Date
}

// GET /api/mt5/symbols
interface SymbolsResponse {
  symbols: Array<{
    name: string
    description: string
    currency: string
    digits: number
    point: number
    spread: number
    minVolume: number
    maxVolume: number
    volumeStep: number
  }>
}
```

## Database Schema

### Key Tables Structure
```sql
-- Performance metrics aggregation
CREATE TABLE performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID REFERENCES trading_accounts(id),
  period_start TIMESTAMP WITH TIME ZONE,
  period_end TIMESTAMP WITH TIME ZONE,
  total_trades INTEGER DEFAULT 0,
  winning_trades INTEGER DEFAULT 0,
  losing_trades INTEGER DEFAULT 0,
  win_rate DECIMAL(5,2) DEFAULT 0,
  avg_win DECIMAL(15,2) DEFAULT 0,
  avg_loss DECIMAL(15,2) DEFAULT 0,
  profit_factor DECIMAL(10,4) DEFAULT 0,
  sharpe_ratio DECIMAL(10,4) DEFAULT 0,
  max_drawdown DECIMAL(5,2) DEFAULT 0,
  return_on_investment DECIMAL(5,2) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Risk alerts table
CREATE TABLE risk_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID REFERENCES trading_accounts(id),
  type VARCHAR(20) NOT NULL CHECK (type IN ('warning', 'danger', 'info')),
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  severity INTEGER DEFAULT 1 CHECK (severity >= 1 AND severity <= 10),
  acknowledged BOOLEAN DEFAULT FALSE,
  acknowledged_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Support tickets table
CREATE TABLE support_tickets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  subject VARCHAR(255) NOT NULL,
  category VARCHAR(50) NOT NULL,
  priority VARCHAR(20) NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  status VARCHAR(20) NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'pending', 'resolved', 'closed')),
  description TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User preferences table
CREATE TABLE user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) UNIQUE,
  trading_preferences JSONB DEFAULT '{}',
  notification_settings JSONB DEFAULT '{}',
  security_settings JSONB DEFAULT '{}',
  ui_preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Error Handling

### Standard Error Response Format
```typescript
interface ApiError {
  success: false
  error: {
    code: string
    message: string
    details?: any
    timestamp: Date
    requestId: string
  }
}

// Error Codes
enum ErrorCodes {
  // Authentication
  INVALID_TOKEN = 'AUTH_001',
  TOKEN_EXPIRED = 'AUTH_002',
  INSUFFICIENT_PERMISSIONS = 'AUTH_003',
  
  // Validation
  INVALID_INPUT = 'VAL_001',
  MISSING_REQUIRED_FIELD = 'VAL_002',
  INVALID_FORMAT = 'VAL_003',
  
  // Trading
  INSUFFICIENT_MARGIN = 'TRADE_001',
  MARKET_CLOSED = 'TRADE_002',
  INVALID_SYMBOL = 'TRADE_003',
  RISK_LIMIT_EXCEEDED = 'TRADE_004',
  
  // Account
  ACCOUNT_SUSPENDED = 'ACC_001',
  ACCOUNT_CLOSED = 'ACC_002',
  DRAWDOWN_LIMIT_EXCEEDED = 'ACC_003',
  
  // System
  INTERNAL_ERROR = 'SYS_001',
  SERVICE_UNAVAILABLE = 'SYS_002',
  RATE_LIMIT_EXCEEDED = 'SYS_003'
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `429` - Too Many Requests (rate limiting)
- `500` - Internal Server Error
- `503` - Service Unavailable

## Security Considerations

### API Security
1. **Rate Limiting**: Implement per-user and per-IP rate limiting
2. **Input Validation**: Validate all inputs using Zod schemas
3. **SQL Injection**: Use parameterized queries and ORM
4. **XSS Protection**: Sanitize all user inputs
5. **CORS**: Configure strict CORS policies
6. **Headers**: Use security headers (Helmet.js)

### Authentication Security
1. **JWT Tokens**: Short-lived access tokens (15 minutes)
2. **Refresh Tokens**: Secure refresh token rotation
3. **API Keys**: Scoped permissions and IP whitelisting
4. **2FA**: Multi-factor authentication for sensitive operations
5. **Session Management**: Secure session handling

### Trading Security
1. **Risk Validation**: Server-side risk rule validation
2. **Order Validation**: Validate all trading orders
3. **Position Limits**: Enforce position and exposure limits
4. **Audit Logging**: Log all trading activities
5. **Emergency Controls**: Circuit breakers and emergency stops

## Performance Optimization

### Caching Strategy
1. **Redis Cache**: Cache frequently accessed data
2. **Response Caching**: Cache API responses with appropriate TTL
3. **Database Indexing**: Optimize database queries with proper indexes
4. **Connection Pooling**: Use database connection pooling

### Real-time Performance
1. **WebSocket Optimization**: Efficient message batching and compression
2. **Data Streaming**: Optimize real-time data streams
3. **Memory Management**: Proper memory cleanup for long-running connections
4. **Load Balancing**: Distribute WebSocket connections across servers

## Monitoring & Observability

### API Monitoring
1. **Response Times**: Monitor API response times
2. **Error Rates**: Track error rates and types
3. **Throughput**: Monitor request volume and patterns
4. **Availability**: Track API uptime and availability

### Business Metrics
1. **Trading Volume**: Monitor trading activity
2. **User Engagement**: Track user activity and retention
3. **Performance Metrics**: Monitor trading performance
4. **Risk Metrics**: Track risk exposure and violations

This comprehensive API specification provides the foundation for implementing all backend services needed to fully integrate the dashboard components with real-time data, secure authentication, payment processing, and MT5 trading integration. 
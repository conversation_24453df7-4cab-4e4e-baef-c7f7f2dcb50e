import { <PERSON>ada<PERSON> } from 'next'
import Link from 'next/link'
import { ArrowRight, Shield, BarChart3, Globe } from 'lucide-react'
import { Logo } from '@/components/ui/Logo'
import { EliteText } from '@/components/ui/EliteText'

export const metadata: Metadata = {
  title: 'Prop Bully - Elite Trading Platform',
  description: 'Experience institutional-grade proprietary trading with advanced analytics, real-time execution, and professional risk management tools. Join the elite traders at Prop Bully.',
}

export default function HomePage() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Professional Background - Darker to match auth pages */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black">
        <div className="absolute inset-0 bg-gradient-to-r from-green-500/3 via-transparent to-blue-500/3" />
      </div>

      {/* Floating Background Elements - More subtle */}
      <div className="absolute top-20 left-20 w-32 h-32 bg-green-500/5 rounded-full blur-xl animate-pulse" />
      <div className="absolute bottom-20 right-20 w-48 h-48 bg-blue-500/5 rounded-full blur-xl animate-pulse delay-1000" />
      <div className="absolute top-1/2 left-10 w-24 h-24 bg-amber-500/5 rounded-full blur-xl animate-pulse delay-500" />

      {/* Navigation */}
      <nav className="relative z-10 flex items-center justify-between p-6">
        <div className="flex items-center space-x-3">
          {/* Prop Bully Logo - will use PNG if available */}
          <Logo 
            size="md" 
            imageSrc="/images/logos/prop-bully-logo.png"
            alt="Prop Bully Logo"
          />
          <EliteText size="lg" variant="gold">Prop Bully</EliteText>
        </div>

        <div className="flex items-center space-x-6">
          <div className="live-indicator">
            <div className="live-dot" />
            <span className="text-sm font-medium text-green-400">Markets Open</span>
          </div>
          <Link href="/auth/login">
            <button className="professional-button">
              Sign In
            </button>
          </Link>
          <Link href="/auth/register">
            <button className="professional-button-dark">
              Get Started
            </button>
          </Link>
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 py-20">
        {/* Hero Section */}
        <div className="text-center mb-20 animate-fade-in-up">
          <h1 className="text-5xl lg:text-7xl font-bold text-primary-content mb-6">
            Elite Trading
            <span className="block text-gradient-green">Platform</span>
          </h1>
          <p className="text-xl text-secondary-content max-w-3xl mx-auto mb-12 leading-relaxed">
            Join thousands of professional traders at <span className="text-gradient-gold font-semibold">Prop Bully</span> and access up to $2M in trading capital. 
            Our comprehensive evaluation process ensures only the most skilled traders advance to funded accounts.
          </p>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
            <Link href="/auth/register">
              <button className="professional-button-dark flex items-center space-x-2 px-8 py-4 text-lg">
                <span>Start Challenge</span>
                <ArrowRight className="h-5 w-5" />
              </button>
            </Link>
            <Link href="/learn-more">
              <button className="professional-button px-8 py-4 text-lg">
                Learn More
              </button>
            </Link>
          </div>
        </div>

        {/* Platform Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-20">
          <div className="glass-card p-6 text-center neon-border">
            <div className="text-3xl font-bold text-gradient-gold mb-2">$2.4B+</div>
            <div className="text-secondary-content">Capital Deployed</div>
          </div>
          <div className="glass-card p-6 text-center neon-border">
            <div className="text-3xl font-bold text-gradient-green mb-2">15,000+</div>
            <div className="text-secondary-content">Active Traders</div>
          </div>
          <div className="glass-card p-6 text-center neon-border">
            <div className="text-3xl font-bold text-gradient-blue mb-2">98.7%</div>
            <div className="text-secondary-content">Success Rate</div>
          </div>
          <div className="glass-card p-6 text-center neon-border">
            <div className="text-3xl font-bold text-gradient-purple mb-2">24/7</div>
            <div className="text-secondary-content">Support</div>
          </div>
        </div>

        {/* Live Market Ticker */}
        <div className="glass-card p-4 mb-20 neon-border relative overflow-hidden">
          {/* Fade gradients at edges */}
          <div className="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-gray-900 via-gray-900/60 to-transparent z-10 pointer-events-none"></div>
          <div className="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-gray-900 via-gray-900/60 to-transparent z-10 pointer-events-none"></div>
          
          <div className="overflow-hidden">
            <div className="price-ticker-container">
              <div className="price-item">
                <span className="price-symbol">EUR/USD</span>
                <span className="price-value">1.0847</span>
                <span className="price-change-positive">+0.0012</span>
              </div>
              <div className="price-item">
                <span className="price-symbol">GBP/USD</span>
                <span className="price-value">1.2634</span>
                <span className="price-change-negative">-0.0023</span>
              </div>
              <div className="price-item">
                <span className="price-symbol">USD/JPY</span>
                <span className="price-value">149.82</span>
                <span className="price-change-positive">+0.24</span>
              </div>
              <div className="price-item">
                <span className="price-symbol">GOLD</span>
                <span className="price-value">2,045.30</span>
                <span className="price-change-positive">+12.80</span>
              </div>
              <div className="price-item">
                <span className="price-symbol">BTC/USD</span>
                <span className="price-value">43,250</span>
                <span className="price-change-positive">+850</span>
              </div>
              <div className="price-item">
                <span className="price-symbol">CRUDE</span>
                <span className="price-value">74.85</span>
                <span className="price-change-negative">-0.32</span>
              </div>
            </div>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20">
          <div className="glass-card p-8 neon-border animate-fade-in-up">
            <div className="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center mb-6">
              <Shield className="h-6 w-6 text-green-400" />
            </div>
            <h3 className="text-xl font-semibold text-primary-content mb-4">Instant Funding</h3>
            <p className="text-secondary-content">
              Get funded immediately after passing our evaluation. Access capital up to $2M with profit sharing up to 90%.
            </p>
          </div>

          <div className="glass-card p-8 neon-border animate-fade-in-up">
            <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mb-6">
              <BarChart3 className="h-6 w-6 text-blue-400" />
            </div>
            <h3 className="text-xl font-semibold text-primary-content mb-4">Advanced Analytics</h3>
            <p className="text-secondary-content">
              Comprehensive performance tracking with real-time risk metrics and detailed trading analytics dashboard.
            </p>
          </div>

          <div className="glass-card p-8 neon-border animate-fade-in-up">
            <div className="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center mb-6">
              <Globe className="h-6 w-6 text-purple-400" />
            </div>
            <h3 className="text-xl font-semibold text-primary-content mb-4">Global Markets</h3>
            <p className="text-secondary-content">
              Trade Forex, Indices, Commodities, and Cryptocurrencies with institutional-grade execution and liquidity.
            </p>
          </div>
        </div>

        {/* Challenge Process */}
        <div className="glass-card p-12 neon-border mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-primary-content mb-4">Challenge Process</h2>
            <p className="text-xl text-secondary-content">Simple steps to become a funded trader</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-lg font-semibold text-primary-content mb-2">Evaluation Phase</h3>
              <p className="text-secondary-content">Demonstrate your trading skills in our comprehensive evaluation process</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-lg font-semibold text-primary-content mb-2">Verification</h3>
              <p className="text-secondary-content">Complete verification phase with consistent trading performance</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-lg font-semibold text-primary-content mb-2">Get Funded</h3>
              <p className="text-secondary-content">Receive your funded account and start earning with our capital</p>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="glass-card p-12 neon-border">
            <h2 className="text-3xl font-bold text-primary-content mb-4">Ready to Start Trading?</h2>
            <p className="text-xl text-secondary-content mb-8">
              Join elite traders worldwide and access institutional capital
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
              <Link href="/auth/register">
                <button className="professional-button-dark flex items-center space-x-2 px-8 py-4 text-lg">
                  <span>Start Your Challenge</span>
                  <ArrowRight className="h-5 w-5" />
                </button>
              </Link>
              <Link href="/auth/login">
                <button className="professional-button px-8 py-4 text-lg">
                  Sign In
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="relative z-10 border-t border-slate-700/50 mt-20">
        <div className="max-w-7xl mx-auto px-6 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <Logo 
                  size="sm" 
                  imageSrc="/images/logos/prop-bully-logo.png"
                  alt="Prop Bully Logo"
                />
                <EliteText size="md" variant="gold">Prop Bully</EliteText>
              </div>
              <p className="text-secondary-content text-sm">
                Elite proprietary trading platform connecting skilled traders with institutional capital.
              </p>
            </div>
            
            <div>
              <h4 className="text-primary-content font-semibold mb-4">Platform</h4>
              <ul className="space-y-2 text-sm text-secondary-content">
                <li><Link href="/challenges" className="hover:text-primary-content transition-colors">Challenges</Link></li>
                <li><Link href="/features" className="hover:text-primary-content transition-colors">Features</Link></li>
                <li><Link href="/pricing" className="hover:text-primary-content transition-colors">Pricing</Link></li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-primary-content font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-sm text-secondary-content">
                <li><Link href="/help" className="hover:text-primary-content transition-colors">Help Center</Link></li>
                <li><Link href="/contact" className="hover:text-primary-content transition-colors">Contact</Link></li>
                <li><Link href="/docs" className="hover:text-primary-content transition-colors">Documentation</Link></li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-primary-content font-semibold mb-4">Legal</h4>
              <ul className="space-y-2 text-sm text-secondary-content">
                <li><Link href="/privacy" className="hover:text-primary-content transition-colors">Privacy Policy</Link></li>
                <li><Link href="/terms" className="hover:text-primary-content transition-colors">Terms of Service</Link></li>
                <li><Link href="/risk" className="hover:text-primary-content transition-colors">Risk Disclosure</Link></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-slate-700/50 mt-8 pt-8 text-center">
            <p className="text-secondary-content text-sm">
              © 2024 Prop Bully. All rights reserved. Trading involves risk and may not be suitable for all investors.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
} 
'use client'

import { useState, useEffect } from 'react'
import { 
  Activity, 
  BarChart3,
  ArrowUpRight,
  ArrowDownRight,
  Target,
  Clock,
  Zap,
  Shield,
  AlertTriangle,
  RefreshCw,
  Settings,
  Volume2
} from 'lucide-react'

interface MarketData {
  symbol: string
  bid: number
  ask: number
  last: number
  change: number
  changePercent: number
  volume: number
  high: number
  low: number
  spread: number
}

interface OrderBookEntry {
  price: number
  size: number
  total: number
}

interface Trade {
  price: number
  size: number
  time: string
  side: 'buy' | 'sell'
}

const TradingInterface = () => {
  const [selectedPair, setSelectedPair] = useState('EUR/USD')
  const [orderType, setOrderType] = useState<'market' | 'limit' | 'stop'>('market')
  const [orderSide, setOrderSide] = useState<'buy' | 'sell'>('buy')
  const [orderSize, setOrderSize] = useState('0.1')
  const [limitPrice, setLimitPrice] = useState('')
  const [stopPrice, setStopPrice] = useState('')
  const [isConnected] = useState(true)

  // Mock market data
  const [marketData, setMarketData] = useState<MarketData>({
    symbol: 'EUR/USD',
    bid: 1.08423,
    ask: 1.08435,
    last: 1.08429,
    change: 0.0023,
    changePercent: 0.21,
    volume: 2400000000,
    high: 1.08455,
    low: 1.08381,
    spread: 1.2
  })

  // Mock order book data
  const [orderBook] = useState({
    bids: [
      { price: 1.08423, size: 2.5, total: 2.5 },
      { price: 1.08422, size: 1.8, total: 4.3 },
      { price: 1.08421, size: 3.2, total: 7.5 },
      { price: 1.08420, size: 0.9, total: 8.4 },
      { price: 1.08419, size: 2.1, total: 10.5 }
    ] as OrderBookEntry[],
    asks: [
      { price: 1.08435, size: 1.9, total: 1.9 },
      { price: 1.08436, size: 2.3, total: 4.2 },
      { price: 1.08437, size: 1.5, total: 5.7 },
      { price: 1.08438, size: 3.1, total: 8.8 },
      { price: 1.08439, size: 0.8, total: 9.6 }
    ] as OrderBookEntry[]
  })

  // Mock recent trades
  const [recentTrades] = useState<Trade[]>([
    { price: 1.08429, size: 0.5, time: '14:32:15', side: 'buy' },
    { price: 1.08427, size: 0.3, time: '14:32:12', side: 'sell' },
    { price: 1.08431, size: 0.8, time: '14:32:08', side: 'buy' },
    { price: 1.08425, size: 0.2, time: '14:32:03', side: 'sell' },
    { price: 1.08428, size: 1.2, time: '14:31:58', side: 'buy' }
  ])

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setMarketData(prev => ({
        ...prev,
        bid: prev.bid + (Math.random() - 0.5) * 0.0002,
        ask: prev.ask + (Math.random() - 0.5) * 0.0002,
        last: prev.last + (Math.random() - 0.5) * 0.0002,
        change: prev.change + (Math.random() - 0.5) * 0.0001,
        changePercent: ((prev.change + (Math.random() - 0.5) * 0.0001) / prev.last) * 100
      }))
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  const handlePlaceOrder = () => {
    // Order placement logic would go here
    console.log('Placing order:', {
      pair: selectedPair,
      type: orderType,
      side: orderSide,
      size: orderSize,
      limitPrice,
      stopPrice
    })
  }

  return (
    <div className="grid grid-cols-12 gap-6 h-screen p-6">
      {/* Market Data Header */}
      <div className="col-span-12 glass-premium p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            <div className="flex items-center space-x-4">
              <select 
                value={selectedPair}
                onChange={(e) => setSelectedPair(e.target.value)}
                className="bg-transparent text-2xl font-bold text-white border-none outline-none cursor-pointer"
              >
                <option value="EUR/USD" className="bg-charcoal">EUR/USD</option>
                <option value="GBP/USD" className="bg-charcoal">GBP/USD</option>
                <option value="USD/JPY" className="bg-charcoal">USD/JPY</option>
                <option value="GOLD" className="bg-charcoal">GOLD</option>
              </select>
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${
                marketData.changePercent >= 0 
                  ? 'bg-green-500/20 text-green-400' 
                  : 'bg-red-500/20 text-red-400'
              }`}>
                {marketData.changePercent >= 0 ? (
                  <ArrowUpRight className="h-4 w-4" />
                ) : (
                  <ArrowDownRight className="h-4 w-4" />
                )}
                <span className="font-bold">
                  {marketData.changePercent >= 0 ? '+' : ''}{marketData.changePercent.toFixed(2)}%
                </span>
              </div>
            </div>
            
            <div className="grid grid-cols-4 gap-8 text-sm">
              <div>
                <div className="text-secondary-content">BID</div>
                <div className="text-xl font-mono font-bold text-red-400">
                  {marketData.bid.toFixed(5)}
                </div>
              </div>
              <div>
                <div className="text-secondary-content">ASK</div>
                <div className="text-xl font-mono font-bold text-green-400">
                  {marketData.ask.toFixed(5)}
                </div>
              </div>
              <div>
                <div className="text-secondary-content">SPREAD</div>
                <div className="text-lg font-mono font-bold text-gold">
                  {marketData.spread.toFixed(1)} pips
                </div>
              </div>
              <div>
                <div className="text-secondary-content">VOLUME</div>
                <div className="text-lg font-bold text-white">
                  {(marketData.volume / 1000000000).toFixed(1)}B
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              isConnected ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                isConnected ? 'bg-green-400 glow-pulse' : 'bg-red-400'
              }`} />
              <span className="text-sm font-semibold">
                {isConnected ? 'CONNECTED' : 'DISCONNECTED'}
              </span>
            </div>
            <button className="glass-card p-2 hover:bg-white/10 transition-colors">
              <RefreshCw className="h-5 w-5 text-secondary-content" />
            </button>
            <button className="glass-card p-2 hover:bg-white/10 transition-colors">
              <Settings className="h-5 w-5 text-secondary-content" />
            </button>
          </div>
        </div>
      </div>

      {/* Chart Area */}
      <div className="col-span-8 glass-premium p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-white">Price Chart</h3>
          <div className="flex items-center space-x-2">
            {['1M', '5M', '15M', '1H', '4H', '1D'].map((timeframe) => (
              <button
                key={timeframe}
                className="px-3 py-1 rounded bg-white/5 hover:bg-white/10 text-sm text-secondary-content hover:text-white transition-colors"
              >
                {timeframe}
              </button>
            ))}
          </div>
        </div>
        
        {/* Advanced Chart Placeholder */}
        <div className="h-96 bg-gradient-to-br from-charcoal/30 to-smoke/20 rounded-xl border border-white/5 flex items-center justify-center">
          <div className="text-center">
            <BarChart3 className="h-16 w-16 text-secondary-content mx-auto mb-4" />
            <p className="text-secondary-content">Advanced TradingView Chart</p>
            <p className="text-sm text-secondary-content/70">Integration Coming Soon</p>
          </div>
        </div>
      </div>

      {/* Order Book */}
      <div className="col-span-4 glass-premium p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-white flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Order Book</span>
          </h3>
          <Volume2 className="h-5 w-5 text-secondary-content" />
        </div>
        
        <div className="space-y-4">
          {/* Asks */}
          <div>
            <div className="grid grid-cols-3 gap-2 text-xs text-secondary-content mb-2">
              <div>PRICE</div>
              <div className="text-right">SIZE</div>
              <div className="text-right">TOTAL</div>
            </div>
            {orderBook.asks.reverse().map((ask, index) => (
              <div key={index} className="grid grid-cols-3 gap-2 text-sm hover:bg-red-500/10 p-1 rounded cursor-pointer">
                <div className="font-mono text-red-400 font-semibold">
                  {ask.price.toFixed(5)}
                </div>
                <div className="text-right font-mono text-white">
                  {ask.size.toFixed(1)}
                </div>
                <div className="text-right font-mono text-secondary-content">
                  {ask.total.toFixed(1)}
                </div>
              </div>
            ))}
          </div>
          
          {/* Spread */}
          <div className="text-center py-2 border-y border-white/10">
            <div className="text-gold font-bold">
              SPREAD: {marketData.spread.toFixed(1)} pips
            </div>
          </div>
          
          {/* Bids */}
          <div>
            {orderBook.bids.map((bid, index) => (
              <div key={index} className="grid grid-cols-3 gap-2 text-sm hover:bg-green-500/10 p-1 rounded cursor-pointer">
                <div className="font-mono text-green-400 font-semibold">
                  {bid.price.toFixed(5)}
                </div>
                <div className="text-right font-mono text-white">
                  {bid.size.toFixed(1)}
                </div>
                <div className="text-right font-mono text-secondary-content">
                  {bid.total.toFixed(1)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Trading Panel */}
      <div className="col-span-4 glass-premium p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-bold text-white flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Place Order</span>
          </h3>
          <Shield className="h-5 w-5 text-green-400" />
        </div>
        
        <div className="space-y-4">
          {/* Order Type Tabs */}
          <div className="flex space-x-1 bg-white/5 p-1 rounded-lg">
            {(['market', 'limit', 'stop'] as const).map((type) => (
              <button
                key={type}
                onClick={() => setOrderType(type)}
                className={`flex-1 py-2 px-3 rounded text-sm font-semibold transition-all ${
                  orderType === type
                    ? 'bg-green-500/20 text-green-400'
                    : 'text-secondary-content hover:text-white'
                }`}
              >
                {type.toUpperCase()}
              </button>
            ))}
          </div>

          {/* Buy/Sell Tabs */}
          <div className="flex space-x-1 bg-white/5 p-1 rounded-lg">
            <button
              onClick={() => setOrderSide('buy')}
              className={`flex-1 py-3 px-4 rounded font-semibold transition-all ${
                orderSide === 'buy'
                  ? 'bg-green-500 text-white'
                  : 'text-secondary-content hover:text-white'
              }`}
            >
              BUY {marketData.ask.toFixed(5)}
            </button>
            <button
              onClick={() => setOrderSide('sell')}
              className={`flex-1 py-3 px-4 rounded font-semibold transition-all ${
                orderSide === 'sell'
                  ? 'bg-red-500 text-white'
                  : 'text-secondary-content hover:text-white'
              }`}
            >
              SELL {marketData.bid.toFixed(5)}
            </button>
          </div>

          {/* Order Size */}
          <div>
            <label className="block text-sm text-secondary-content mb-2">Size (Lots)</label>
            <input
              type="number"
              value={orderSize}
              onChange={(e) => setOrderSize(e.target.value)}
              step="0.01"
              min="0.01"
              className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-3 text-white font-mono focus:border-green-500/50 focus:outline-none"
              placeholder="0.10"
            />
          </div>

          {/* Conditional Fields */}
          {orderType === 'limit' && (
            <div>
              <label className="block text-sm text-secondary-content mb-2">Limit Price</label>
              <input
                type="number"
                value={limitPrice}
                onChange={(e) => setLimitPrice(e.target.value)}
                step="0.00001"
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-3 text-white font-mono focus:border-green-500/50 focus:outline-none"
                placeholder={marketData.last.toFixed(5)}
              />
            </div>
          )}

          {orderType === 'stop' && (
            <div>
              <label className="block text-sm text-secondary-content mb-2">Stop Price</label>
              <input
                type="number"
                value={stopPrice}
                onChange={(e) => setStopPrice(e.target.value)}
                step="0.00001"
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-3 text-white font-mono focus:border-green-500/50 focus:outline-none"
                placeholder={marketData.last.toFixed(5)}
              />
            </div>
          )}

          {/* Order Summary */}
          <div className="glass-card p-4 space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-secondary-content">Est. Margin:</span>
              <span className="text-white font-mono">
                ${(parseFloat(orderSize) * 100000 * 0.02).toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-secondary-content">Pip Value:</span>
              <span className="text-white font-mono">
                ${(parseFloat(orderSize) * 10).toFixed(2)}
              </span>
            </div>
          </div>

          {/* Place Order Button */}
          <button
            onClick={handlePlaceOrder}
            className={`w-full py-4 rounded-lg font-bold text-lg transition-all ${
              orderSide === 'buy'
                ? 'btn-premium text-black'
                : 'bg-red-500 hover:bg-red-600 text-white'
            }`}
          >
            PLACE {orderSide.toUpperCase()} ORDER
          </button>
        </div>
      </div>

      {/* Recent Trades */}
      <div className="col-span-4 glass-premium p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-white">Recent Trades</h3>
          <Clock className="h-5 w-5 text-secondary-content" />
        </div>
        
        <div className="space-y-2">
          <div className="grid grid-cols-4 gap-2 text-xs text-secondary-content pb-2 border-b border-white/10">
            <div>TIME</div>
            <div className="text-right">PRICE</div>
            <div className="text-right">SIZE</div>
            <div className="text-right">SIDE</div>
          </div>
          
          {recentTrades.map((trade, index) => (
            <div key={index} className="grid grid-cols-4 gap-2 text-sm hover:bg-white/5 p-2 rounded">
              <div className="font-mono text-secondary-content text-xs">
                {trade.time}
              </div>
              <div className={`text-right font-mono font-semibold ${
                trade.side === 'buy' ? 'text-green-400' : 'text-red-400'
              }`}>
                {trade.price.toFixed(5)}
              </div>
              <div className="text-right font-mono text-white">
                {trade.size.toFixed(1)}
              </div>
              <div className={`text-right text-xs font-bold ${
                trade.side === 'buy' ? 'text-green-400' : 'text-red-400'
              }`}>
                {trade.side.toUpperCase()}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Market Analysis */}
      <div className="col-span-4 glass-premium p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-white">Market Analysis</h3>
          <Target className="h-5 w-5 text-secondary-content" />
        </div>
        
        <div className="space-y-4">
          {/* Price Levels */}
          <div>
            <h4 className="text-sm font-semibold text-secondary-content mb-3">Key Levels</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center p-2 bg-red-500/10 rounded">
                <span className="text-red-400 text-sm">Resistance</span>
                <span className="font-mono text-red-400 font-bold">1.08455</span>
              </div>
              <div className="flex justify-between items-center p-2 bg-green-500/10 rounded">
                <span className="text-green-400 text-sm">Support</span>
                <span className="font-mono text-green-400 font-bold">1.08381</span>
              </div>
            </div>
          </div>

          {/* Market Sentiment */}
          <div>
            <h4 className="text-sm font-semibold text-secondary-content mb-3">Market Sentiment</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-secondary-content">Bullish</span>
                <span className="text-green-400 font-bold">68%</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-2">
                <div className="bg-green-400 h-2 rounded-full" style={{ width: '68%' }} />
              </div>
            </div>
          </div>

          {/* Risk Warning */}
          <div className="flex items-start space-x-3 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-yellow-400 flex-shrink-0 mt-0.5" />
            <div>
              <div className="text-yellow-400 font-semibold text-sm">High Volatility</div>
              <div className="text-xs text-secondary-content mt-1">
                Economic news expected at 15:30 UTC
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TradingInterface 
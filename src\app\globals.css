@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=JetBrains+Mono:wght@100..800&family=Orbitron:wght@400;700;900&family=Exo+2:wght@400;600;700;800;900&family=Rajdhani:wght@400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Professional Gold Theme */
  --background: 6 8 12;
  --foreground: 248 250 252;
  --card: 10 12 16;
  --card-foreground: 248 250 252;
  --popover: 10 12 16;
  --popover-foreground: 248 250 252;
  --primary: 251 191 36;
  --primary-foreground: 6 8 12;
  --secondary: 148 163 184;
  --secondary-foreground: 6 8 12;
  --muted: 24 26 32;
  --muted-foreground: 161 161 170;
  --accent: 24 26 32;
  --accent-foreground: 248 250 252;
  --destructive: 239 68 68;
  --destructive-foreground: 248 250 252;
  --border: 30 34 42;
  --input: 30 34 42;
  --ring: 251 191 36;
  --radius: 0.75rem;

  /* Professional Color Palette */
  --gold: 251 191 36;
  --gold-light: 252 211 77;
  --gold-secondary: 255 215 0;
  --green-secondary: 34 197 94;
  --silver: 148 163 184;
  --platinum: 203 213 225;
  --obsidian: 6 8 12;
  --charcoal: 15 18 24;
  --smoke: 30 34 42;
  --accent-blue: 59 130 246;
  --accent-purple: 147 51 234;
}

* {
  border-color: hsl(var(--border));
}

body {
  background: 
    linear-gradient(135deg, rgb(var(--obsidian)) 0%, rgb(var(--charcoal)) 35%, rgb(var(--obsidian)) 100%),
    radial-gradient(ellipse at top, rgba(251, 191, 36, 0.02) 0%, transparent 60%),
    radial-gradient(ellipse at bottom right, rgba(148, 163, 184, 0.015) 0%, transparent 50%);
  color: hsl(var(--foreground));
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  overflow-x: hidden;
  min-height: 100vh;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(251, 191, 36, 0.03) 0%, transparent 35%),
    radial-gradient(circle at 80% 80%, rgba(148, 163, 184, 0.02) 0%, transparent 35%),
    radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.015) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Premium Glass Components - Gold Theme */
.glass-premium {
  background: linear-gradient(135deg, 
    rgba(251, 191, 36, 0.04) 0%, 
    rgba(255, 255, 255, 0.015) 30%, 
    rgba(148, 163, 184, 0.025) 70%,
    rgba(251, 191, 36, 0.03) 100%);
  backdrop-filter: blur(24px) saturate(1.2);
  -webkit-backdrop-filter: blur(24px) saturate(1.2);
  border: 1px solid rgba(251, 191, 36, 0.08);
  border-radius: 1.25rem;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.08),
    0 0 60px rgba(251, 191, 36, 0.04);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.glass-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(251, 191, 36, 0.3) 50%, 
    transparent 100%);
}

.glass-premium:hover {
  border-color: rgba(251, 191, 36, 0.15);
  box-shadow: 
    0 16px 50px rgba(0, 0, 0, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.12),
    0 0 80px rgba(251, 191, 36, 0.08);
  transform: translateY(-3px);
}

.glass-card {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.04) 0%, 
    rgba(255, 255, 255, 0.015) 50%, 
    rgba(251, 191, 36, 0.025) 100%);
  backdrop-filter: blur(20px) saturate(1.1);
  -webkit-backdrop-filter: blur(20px) saturate(1.1);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1rem;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.06);
  transition: all 0.3s ease;
}

.glass-card:hover {
  border-color: rgba(251, 191, 36, 0.12);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 0 50px rgba(251, 191, 36, 0.06);
  transform: translateY(-1px);
}

/* Enhanced Premium Buttons */
.btn-premium {
  background: linear-gradient(135deg, 
    rgb(251, 191, 36) 0%, 
    rgb(255, 215, 0) 50%,
    rgb(251, 191, 36) 100%);
  color: rgb(6, 8, 12);
  font-weight: 700;
  padding: 1rem 2.5rem;
  border-radius: 1rem;
  border: none;
  box-shadow: 
    0 8px 24px rgba(251, 191, 36, 0.35),
    inset 0 1px 0 rgba(255, 255, 255, 0.25),
    0 0 30px rgba(251, 191, 36, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent);
  transition: left 0.6s ease;
}

.btn-premium:hover::before {
  left: 100%;
}

.btn-premium:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 
    0 12px 32px rgba(251, 191, 36, 0.45),
    inset 0 1px 0 rgba(255, 255, 255, 0.35),
    0 0 50px rgba(251, 191, 36, 0.3);
}

.btn-secondary {
  background: linear-gradient(135deg, 
    rgba(148, 163, 184, 0.08) 0%, 
    rgba(203, 213, 225, 0.06) 50%,
    rgba(148, 163, 184, 0.08) 100%);
  color: rgb(248, 250, 252);
  font-weight: 600;
  padding: 1rem 2.5rem;
  border-radius: 1rem;
  border: 1px solid rgba(148, 163, 184, 0.15);
  backdrop-filter: blur(16px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(251, 191, 36, 0.08) 0%, 
    transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.btn-secondary:hover::before {
  opacity: 1;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, 
    rgba(148, 163, 184, 0.12) 0%, 
    rgba(203, 213, 225, 0.1) 50%,
    rgba(148, 163, 184, 0.12) 100%);
  border-color: rgba(251, 191, 36, 0.25);
  transform: translateY(-2px);
  box-shadow: 
    0 8px 24px rgba(251, 191, 36, 0.1),
    0 0 40px rgba(251, 191, 36, 0.05);
}

/* Enhanced Typography Gradients */
.text-gradient-gold {
  background: linear-gradient(135deg, 
    rgb(251, 191, 36) 0%, 
    rgb(252, 211, 77) 30%, 
    rgb(255, 215, 0) 70%,
    rgb(251, 191, 36) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 0 20px rgba(251, 191, 36, 0.25));
}

.text-gradient-silver {
  background: linear-gradient(135deg, 
    rgb(148, 163, 184) 0%, 
    rgb(203, 213, 225) 50%, 
    rgb(241, 245, 249) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-green {
  background: linear-gradient(135deg, 
    rgb(34, 197, 94) 0%, 
    rgb(16, 185, 129) 50%, 
    rgb(5, 150, 105) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 0 16px rgba(34, 197, 94, 0.3));
}

/* Advanced Animations */
@keyframes float {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg); 
  }
  50% { 
    transform: translateY(-12px) rotate(1deg); 
  }
}

@keyframes glow-pulse {
  0%, 100% { 
    opacity: 0.8;
    box-shadow: 0 0 15px rgba(251, 191, 36, 0.4);
  }
  50% { 
    opacity: 1;
    box-shadow: 0 0 25px rgba(251, 191, 36, 0.7);
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes chart-rise {
  0% { 
    transform: scaleY(0);
    opacity: 0;
  }
  100% { 
    transform: scaleY(1);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scroll-left {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

@keyframes status-pulse {
  0%, 100% { 
    opacity: 1;
    transform: scale(1);
  }
  50% { 
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* Enhanced Component Classes */
.floating {
  animation: float 6s ease-in-out infinite;
}

.glow-pulse {
  animation: glow-pulse 2s ease-in-out infinite;
}

.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.1), 
    transparent);
  animation: shimmer 3s infinite;
}

.interactive-glow {
  position: relative;
  z-index: 1;
}

.interactive-glow::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, 
    rgba(251, 191, 36, 0.15), 
    rgba(148, 163, 184, 0.1), 
    rgba(59, 130, 246, 0.1));
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
  filter: blur(8px);
}

.interactive-glow:hover::after {
  opacity: 1;
}

.price-ticker {
  animation: scroll-left 60s linear infinite;
  display: flex;
  align-items: center;
}

.status-live {
  animation: status-pulse 2s ease-in-out infinite;
  position: relative;
}

.status-live::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background: rgba(251, 191, 36, 0.3);
  animation: pulse-ring 2s ease-in-out infinite;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
}

/* Enhanced Backgrounds */
.backdrop-glow {
  background: 
    radial-gradient(ellipse at center, rgba(251, 191, 36, 0.05) 0%, transparent 70%),
    linear-gradient(135deg, rgba(255, 255, 255, 0.01) 0%, rgba(251, 191, 36, 0.02) 100%);
}

.border-glow {
  border-color: rgba(251, 191, 36, 0.2);
  box-shadow: 0 0 20px rgba(251, 191, 36, 0.1);
}

/* Text Color Classes */
.text-primary-content {
  color: rgb(248, 250, 252);
}

.text-secondary-content {
  color: rgb(148, 163, 184);
}

.text-success {
  color: rgb(34, 197, 94);
}

.text-danger {
  color: rgb(239, 68, 68);
}

.text-warning {
  color: rgb(251, 191, 36);
}

.text-info {
  color: rgb(59, 130, 246);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(30, 34, 42, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, 
    rgba(251, 191, 36, 0.6) 0%, 
    rgba(255, 215, 0, 0.6) 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, 
    rgba(251, 191, 36, 0.8) 0%, 
    rgba(255, 215, 0, 0.8) 100%);
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
  .glass-premium,
  .glass-card {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }
  
  .btn-premium,
  .btn-secondary {
    padding: 0.875rem 2rem;
    font-size: 0.95rem;
  }
  
  .price-ticker {
    animation-duration: 40s;
  }
}

/* Fix Potential Click Blocking Issues */
button, a, [role="button"], .clickable {
  pointer-events: auto !important;
  position: relative;
  z-index: 1;
}

.professional-button,
.professional-button-dark,
.btn-premium,
.btn-secondary,
.buy-button,
.sell-button,
.neutral-button {
  pointer-events: auto !important;
  cursor: pointer;
  z-index: 10;
  position: relative;
}

/* Ensure interactive elements are above pseudo-elements */
*:hover,
*:focus,
*:active {
  z-index: 2;
}

/* Debug: Highlight clickable elements in development */
@media (prefers-color-scheme: dark) {
  button:hover,
  a:hover,
  [role="button"]:hover {
    outline: 2px solid rgba(34, 197, 94, 0.5) !important;
    outline-offset: 2px;
  }
}

/* Professional Chart Elements */
.chart-container {
  background: 
    linear-gradient(135deg, 
      rgba(251, 191, 36, 0.02) 0%, 
      rgba(255, 255, 255, 0.01) 50%,
      rgba(251, 191, 36, 0.015) 100%);
  border: 1px solid rgba(251, 191, 36, 0.1);
  border-radius: 1rem;
  position: relative;
  overflow: hidden;
}

.chart-grid {
  stroke: rgba(148, 163, 184, 0.1);
  stroke-width: 0.5;
}

.chart-line {
  stroke: rgb(251, 191, 36);
  stroke-width: 3;
  fill: none;
  filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.3));
}

.chart-fill {
  fill: url(#chartGradient);
}

.chart-point {
  fill: rgb(251, 191, 36);
  filter: drop-shadow(0 0 6px rgba(251, 191, 36, 0.5));
  animation: chart-rise 0.8s ease-out forwards;
}

/* Enhanced Status Indicators */
.live-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(251, 191, 36, 0.1);
  border: 1px solid rgba(251, 191, 36, 0.2);
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.live-dot {
  width: 8px;
  height: 8px;
  background: rgb(251, 191, 36);
  border-radius: 50%;
  animation: status-pulse 2s ease-in-out infinite;
}

/* Enhanced Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.3),
    0 0 60px rgba(251, 191, 36, 0.1);
}

.hover-glow {
  transition: all 0.3s ease;
  position: relative;
}

.hover-glow:hover {
  box-shadow: 
    0 0 30px rgba(251, 191, 36, 0.3),
    inset 0 0 20px rgba(251, 191, 36, 0.05);
}

/* Enhanced Professional Elements */
.professional-stat-card {
  background: linear-gradient(135deg, 
    rgba(251, 191, 36, 0.05) 0%, 
    rgba(255, 255, 255, 0.02) 50%,
    rgba(148, 163, 184, 0.03) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(251, 191, 36, 0.1);
  border-radius: 1.25rem;
  padding: 2rem;
  transition: all 0.3s ease;
}

.professional-stat-card:hover {
  border-color: rgba(251, 191, 36, 0.2);
  transform: translateY(-2px);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.4),
    0 0 50px rgba(251, 191, 36, 0.08);
}

.neon-accent {
  text-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
  color: rgb(251, 191, 36);
}

.gradient-border {
  position: relative;
  border-radius: 1rem;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(148, 163, 184, 0.05));
  padding: 1px;
}

.gradient-border-inner {
  background: rgb(var(--obsidian));
  border-radius: calc(1rem - 1px);
  height: 100%;
  width: 100%;
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    background: linear-gradient(135deg, 
      hsl(var(--background)) 0%, 
      rgb(1, 4, 15) 25%, 
      hsl(var(--background)) 50%, 
      rgb(3, 7, 18) 75%, 
      hsl(var(--background)) 100%);
    background-size: 400% 400%;
    animation: gradientShift 20s ease infinite;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-weight: 400;
    line-height: 1.6;
  }

  h1, h2, h3, h4, h5, h6 {
    color: hsl(var(--foreground));
    font-weight: 600;
    line-height: 1.2;
  }

  p {
    color: hsl(var(--muted-foreground));
  }
}

@layer components {
  /* Professional Glass Cards */
  .glass-card {
    @apply backdrop-blur-xl border rounded-xl shadow-2xl;
    background: linear-gradient(135deg, 
      rgba(51, 65, 85, 0.95) 0%, 
      rgba(30, 41, 59, 0.98) 100%);
    border-color: hsl(var(--border));
    box-shadow: 
      0 25px 50px -12px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(148, 163, 184, 0.1),
      inset 0 1px 0 rgba(248, 250, 252, 0.1);
  }
  
  .glass-card-light {
    @apply backdrop-blur-xl border rounded-xl shadow-xl;
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.95) 0%, 
      rgba(248, 250, 252, 0.98) 100%);
    border-color: hsl(var(--border));
    box-shadow: 
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  .professional-button {
    @apply px-6 py-3 rounded-lg font-semibold transition-all duration-200;
    @apply bg-white text-slate-900 border border-slate-200;
    @apply hover:bg-slate-50 hover:border-slate-300 hover:shadow-lg;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
    @apply active:scale-95;
  }
  
  .professional-button-dark {
    @apply px-6 py-3 rounded-lg font-semibold transition-all duration-200;
    @apply bg-slate-800 text-white border border-slate-700;
    @apply hover:bg-slate-700 hover:border-slate-600 hover:shadow-lg;
    @apply focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-slate-900;
    @apply active:scale-95;
  }
  
  .neon-border {
    border: 1px solid transparent;
    background: linear-gradient(135deg, 
      rgba(30, 41, 59, 0.95), 
      rgba(15, 23, 42, 0.98)) padding-box,
      linear-gradient(135deg, 
        rgba(34, 197, 94, 0.6), 
        rgba(59, 130, 246, 0.4)) border-box;
    position: relative;
  }

  .neon-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 1px;
    background: linear-gradient(135deg, 
      rgba(34, 197, 94, 0.2), 
      rgba(59, 130, 246, 0.1));
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask-composite: xor;
    animation: neonPulse 3s ease-in-out infinite;
  }

  /* Trading-specific components */
  .price-ticker {
    @apply flex items-center space-x-6 backdrop-blur-sm border rounded-lg px-6 py-3;
    background: rgba(15, 23, 42, 0.9);
    border-color: hsl(var(--border));
    animation: slidePrice 15s linear infinite;
  }
  
  .price-ticker-container {
    @apply flex items-center space-x-8;
    animation: scroll-ticker 30s linear infinite;
    width: max-content;
  }
  
  .price-item {
    @apply flex items-center space-x-3 font-mono text-sm whitespace-nowrap;
    min-width: max-content;
  }
  
  .price-symbol {
    @apply text-slate-300 font-medium;
  }
  
  .price-value {
    @apply text-white font-bold;
  }
  
  .price-change-positive {
    @apply text-green-400 font-medium;
  }
  
  .price-change-negative {
    @apply text-red-400 font-medium;
  }

  /* Professional Trading Buttons */
  .buy-button {
    @apply bg-gradient-to-r from-green-600 to-green-500 text-white font-semibold shadow-lg;
    @apply hover:from-green-500 hover:to-green-400 hover:shadow-xl;
    @apply focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-slate-900;
    @apply active:scale-95 transition-all duration-200;
    box-shadow: 0 10px 20px rgba(34, 197, 94, 0.3);
  }
  
  .sell-button {
    @apply bg-gradient-to-r from-red-600 to-red-500 text-white font-semibold shadow-lg;
    @apply hover:from-red-500 hover:to-red-400 hover:shadow-xl;
    @apply focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-slate-900;
    @apply active:scale-95 transition-all duration-200;
    box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
  }

  .neutral-button {
    @apply bg-gradient-to-r from-slate-700 to-slate-600 text-white font-semibold shadow-lg;
    @apply hover:from-slate-600 hover:to-slate-500 hover:shadow-xl;
    @apply focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 focus:ring-offset-slate-900;
    @apply active:scale-95 transition-all duration-200;
    box-shadow: 0 10px 20px rgba(100, 116, 139, 0.3);
  }

  /* Professional Text Styles */
  .text-primary-content {
    color: hsl(var(--foreground));
  }
  
  .text-secondary-content {
    color: hsl(var(--muted-foreground));
  }
  
  .text-success {
    @apply text-green-400;
  }
  
  .text-danger {
    @apply text-red-400;
  }
  
  .text-warning {
    @apply text-amber-400;
  }
  
  .text-info {
    @apply text-blue-400;
  }

  /* Live Elements */
  .live-indicator {
    @apply flex items-center space-x-2;
  }
  
  .live-dot {
    @apply w-2 h-2 bg-green-400 rounded-full animate-pulse;
  }
  
  .live-text {
    @apply text-green-400 text-sm font-medium;
  }

  /* Chart Elements */
  .candlestick {
    @apply relative rounded-sm transition-all duration-200;
    animation: candleGlow 2s ease-in-out infinite alternate;
  }
  
  .candlestick.bullish {
    @apply bg-green-500;
    box-shadow: 0 0 15px rgba(34, 197, 94, 0.4);
  }
  
  .candlestick.bearish {
    @apply bg-red-500;
    box-shadow: 0 0 15px rgba(239, 68, 68, 0.4);
  }

  /* Status Indicators */
  .status-online {
    @apply bg-green-500 text-white;
  }
  
  .status-offline {
    @apply bg-red-500 text-white;
  }
  
  .status-warning {
    @apply bg-amber-500 text-white;
  }

  /* Floating Elements */
  .floating:nth-child(2) {
    animation-delay: -2s;
  }
  
  .floating:nth-child(3) {
    animation-delay: -4s;
  }
}

@layer utilities {
  /* Professional Text Gradients */
  .text-gradient-gold {
    @apply bg-gradient-to-r from-amber-400 via-amber-500 to-amber-600 bg-clip-text text-transparent;
  }
  
  .text-gradient-green {
    @apply bg-gradient-to-r from-green-400 to-green-500 bg-clip-text text-transparent;
  }
  
  .text-gradient-red {
    @apply bg-gradient-to-r from-red-400 to-red-500 bg-clip-text text-transparent;
  }
  
  .text-gradient-blue {
    @apply bg-gradient-to-r from-blue-400 to-blue-500 bg-clip-text text-transparent;
  }
  
  .text-gradient-purple {
    @apply bg-gradient-to-r from-purple-400 to-purple-500 bg-clip-text text-transparent;
  }
  
  /* Hide scrollbars */
  .scrollbar-hidden {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hidden::-webkit-scrollbar {
    display: none;
  }

  /* Professional shadows */
  .shadow-professional {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-professional-dark {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
  }
}

/* Professional Animations */
@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes neonPulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

@keyframes slidePrice {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

@keyframes scroll-ticker {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

@keyframes candleGlow {
  0% { opacity: 0.8; }
  100% { opacity: 1; }
}

/* Chart animations */
.chart-bar {
  animation: chartPulse 2s ease-in-out infinite;
}

@keyframes chartPulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.chart-bar:nth-child(2) { animation-delay: 0.2s; }
.chart-bar:nth-child(3) { animation-delay: 0.4s; }
.chart-bar:nth-child(4) { animation-delay: 0.6s; }
.chart-bar:nth-child(5) { animation-delay: 0.8s; }

/* Mobile optimizations */
@media (max-width: 768px) {
  .glass-premium,
  .glass-card {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }
  
  .price-ticker {
    @apply px-4 py-2 text-xs;
  }
  
  .professional-button, .professional-button-dark {
    @apply px-4 py-2 text-sm;
  }
} 
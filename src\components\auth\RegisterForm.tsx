'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { User, Mail, Lock, Eye, EyeOff, Loader2, Globe, Calendar } from 'lucide-react'
import { supabase } from '@/lib/supabase/client'

interface FormData {
  firstName: string
  lastName: string
  email: string
  password: string
  confirmPassword: string
  country: string
  dateOfBirth: string
  acceptTerms: boolean
  acceptMarketing: boolean
}

interface FormErrors {
  firstName?: string
  lastName?: string
  email?: string
  password?: string
  confirmPassword?: string
  country?: string
  dateOfBirth?: string
  acceptTerms?: string
  general?: string
}

const countries = [
  'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 
  'France', 'Netherlands', 'Switzerland', 'Singapore', 'Japan',
  'Other'
]

export function RegisterForm() {
  const router = useRouter()
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    country: '',
    dateOfBirth: '',
    acceptTerms: false,
    acceptMarketing: false
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required'
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required'
    }

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters'
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'Password must contain uppercase, lowercase, and numbers'
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    // Country validation
    if (!formData.country) {
      newErrors.country = 'Please select your country'
    }

    // Date of birth validation
    if (!formData.dateOfBirth) {
      newErrors.dateOfBirth = 'Date of birth is required'
    } else {
      const birthDate = new Date(formData.dateOfBirth)
      const today = new Date()
      const age = today.getFullYear() - birthDate.getFullYear()
      if (age < 18) {
        newErrors.dateOfBirth = 'You must be at least 18 years old'
      }
    }

    // Terms acceptance validation
    if (!formData.acceptTerms) {
      newErrors.acceptTerms = 'You must accept the terms and conditions'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setIsLoading(true)
    setErrors({})

    try {
      // Register user with Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            first_name: formData.firstName,
            last_name: formData.lastName,
            country: formData.country,
            date_of_birth: formData.dateOfBirth,
            accept_marketing: formData.acceptMarketing
          }
        }
      })

      if (authError) {
        if (authError.message.includes('already been registered')) {
          setErrors({ email: 'An account with this email already exists' })
        } else {
          setErrors({ general: authError.message })
        }
        return
      }

      if (authData.user) {
        // Create user profile in our database
        const { error: profileError } = await supabase
          .from('users')
          .insert({
            id: authData.user.id,
            email: formData.email,
            first_name: formData.firstName,
            last_name: formData.lastName,
            country: formData.country,
            date_of_birth: formData.dateOfBirth,
            kyc_status: 'pending',
            account_status: 'pending'
          })

        if (profileError) {
          console.error('Profile creation error:', profileError)
          // User was created in auth but profile failed - should handle this gracefully
        }

        // Show success message and redirect
        setErrors({ general: 'Registration successful! Please check your email to verify your account.' })
        
        // Redirect after a short delay
        setTimeout(() => {
          router.push('/auth/verify-email')
        }, 2000)
      }
    } catch (error) {
      console.error('Registration error:', error)
      setErrors({ general: 'An unexpected error occurred. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    const checked = (e.target as HTMLInputElement).checked

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
    
    // Clear specific field error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }))
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* General Messages */}
      {errors.general && (
        <div className={`glass-card p-4 border ${
          errors.general.includes('successful') 
            ? 'border-green-500/30 bg-green-500/10' 
            : 'border-red-500/30 bg-red-500/10'
        }`}>
          <p className={`text-sm text-center ${
            errors.general.includes('successful') ? 'text-green-400' : 'text-red-400'
          }`}>
            {errors.general}
          </p>
        </div>
      )}

      {/* Name Fields */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-1">
          <label htmlFor="firstName" className="text-sm font-medium text-white/80">
            First Name
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <User className="h-4 w-4 text-white/40" />
            </div>
            <input
              id="firstName"
              name="firstName"
              type="text"
              required
              value={formData.firstName}
              onChange={handleChange}
              className={`
                w-full pl-10 pr-3 py-2 text-sm
                bg-black/60 border rounded-lg
                text-white placeholder-white/40
                focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent
                transition-all duration-200
                ${errors.firstName ? 'border-red-500/50' : 'border-white/20 hover:border-white/30'}
              `}
              placeholder="John"
            />
          </div>
          {errors.firstName && (
            <p className="text-red-400 text-xs">{errors.firstName}</p>
          )}
        </div>

        <div className="space-y-1">
          <label htmlFor="lastName" className="text-sm font-medium text-white/80">
            Last Name
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <User className="h-4 w-4 text-white/40" />
            </div>
            <input
              id="lastName"
              name="lastName"
              type="text"
              required
              value={formData.lastName}
              onChange={handleChange}
              className={`
                w-full pl-10 pr-3 py-2 text-sm
                bg-black/60 border rounded-lg
                text-white placeholder-white/40
                focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent
                transition-all duration-200
                ${errors.lastName ? 'border-red-500/50' : 'border-white/20 hover:border-white/30'}
              `}
              placeholder="Doe"
            />
          </div>
          {errors.lastName && (
            <p className="text-red-400 text-xs">{errors.lastName}</p>
          )}
        </div>
      </div>

      {/* Email Field */}
      <div className="space-y-1">
        <label htmlFor="email" className="text-sm font-medium text-white/80">
          Email Address
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Mail className="h-4 w-4 text-white/40" />
          </div>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={formData.email}
            onChange={handleChange}
            className={`
              w-full pl-10 pr-3 py-2 text-sm
              bg-black/60 border rounded-lg
              text-white placeholder-white/40
              focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent
              transition-all duration-200
              ${errors.email ? 'border-red-500/50' : 'border-white/20 hover:border-white/30'}
            `}
            placeholder="<EMAIL>"
          />
        </div>
        {errors.email && (
          <p className="text-red-400 text-xs">{errors.email}</p>
        )}
      </div>

      {/* Password Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-1">
          <label htmlFor="password" className="text-sm font-medium text-white/80">
            Password
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Lock className="h-4 w-4 text-white/40" />
            </div>
            <input
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              required
              value={formData.password}
              onChange={handleChange}
              className={`
                w-full pl-10 pr-10 py-2 text-sm
                bg-black/60 border rounded-lg
                text-white placeholder-white/40
                focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent
                transition-all duration-200
                ${errors.password ? 'border-red-500/50' : 'border-white/20 hover:border-white/30'}
              `}
              placeholder="••••••••"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-white/40 hover:text-white/60" />
              ) : (
                <Eye className="h-4 w-4 text-white/40 hover:text-white/60" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="text-red-400 text-xs">{errors.password}</p>
          )}
        </div>

        <div className="space-y-1">
          <label htmlFor="confirmPassword" className="text-sm font-medium text-white/80">
            Confirm Password
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Lock className="h-4 w-4 text-white/40" />
            </div>
            <input
              id="confirmPassword"
              name="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              required
              value={formData.confirmPassword}
              onChange={handleChange}
              className={`
                w-full pl-10 pr-10 py-2 text-sm
                bg-black/60 border rounded-lg
                text-white placeholder-white/40
                focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent
                transition-all duration-200
                ${errors.confirmPassword ? 'border-red-500/50' : 'border-white/20 hover:border-white/30'}
              `}
              placeholder="••••••••"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              {showConfirmPassword ? (
                <EyeOff className="h-4 w-4 text-white/40 hover:text-white/60" />
              ) : (
                <Eye className="h-4 w-4 text-white/40 hover:text-white/60" />
              )}
            </button>
          </div>
          {errors.confirmPassword && (
            <p className="text-red-400 text-xs">{errors.confirmPassword}</p>
          )}
        </div>
      </div>

      {/* Country and Date of Birth */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-1">
          <label htmlFor="country" className="text-sm font-medium text-white/80">
            Country
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Globe className="h-4 w-4 text-white/40" />
            </div>
            <select
              id="country"
              name="country"
              required
              value={formData.country}
              onChange={handleChange}
              className={`
                w-full pl-10 pr-3 py-2 text-sm
                bg-black/60 border rounded-lg
                text-white
                focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent
                transition-all duration-200
                ${errors.country ? 'border-red-500/50' : 'border-white/20 hover:border-white/30'}
              `}
            >
              <option value="">Select country</option>
              {countries.map((country) => (
                <option key={country} value={country} className="bg-black text-white">
                  {country}
                </option>
              ))}
            </select>
          </div>
          {errors.country && (
            <p className="text-red-400 text-xs">{errors.country}</p>
          )}
        </div>

        <div className="space-y-1">
          <label htmlFor="dateOfBirth" className="text-sm font-medium text-white/80">
            Date of Birth
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Calendar className="h-4 w-4 text-white/40" />
            </div>
            <input
              id="dateOfBirth"
              name="dateOfBirth"
              type="date"
              required
              value={formData.dateOfBirth}
              onChange={handleChange}
              className={`
                w-full pl-10 pr-3 py-2 text-sm
                bg-black/60 border rounded-lg
                text-white
                focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent
                transition-all duration-200
                ${errors.dateOfBirth ? 'border-red-500/50' : 'border-white/20 hover:border-white/30'}
              `}
            />
          </div>
          {errors.dateOfBirth && (
            <p className="text-red-400 text-xs">{errors.dateOfBirth}</p>
          )}
        </div>
      </div>

      {/* Checkboxes */}
      <div className="space-y-4">
        <div className="flex items-start space-x-3">
          <input
            id="acceptTerms"
            name="acceptTerms"
            type="checkbox"
            checked={formData.acceptTerms}
            onChange={handleChange}
            style={{ 
              pointerEvents: 'auto',
              zIndex: 10,
              position: 'relative'
            }}
            className="mt-1 h-4 w-4 bg-black/60 border-white/20 rounded focus:ring-green-500 focus:ring-2 cursor-pointer accent-green-500"
          />
          <label 
            htmlFor="acceptTerms" 
            className="text-sm text-white/80 leading-relaxed cursor-pointer"
            style={{ 
              pointerEvents: 'auto',
              zIndex: 10,
              position: 'relative'
            }}
          >
            I agree to the{' '}
            <a 
              href="/terms" 
              className="text-green-400 hover:text-green-300 underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              Terms of Service
            </a>
            {' '}and{' '}
            <a 
              href="/privacy" 
              className="text-green-400 hover:text-green-300 underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              Privacy Policy
            </a>
          </label>
        </div>
        {errors.acceptTerms && (
          <p className="text-red-400 text-xs ml-7">{errors.acceptTerms}</p>
        )}

        <div className="flex items-start space-x-3">
          <input
            id="acceptMarketing"
            name="acceptMarketing"
            type="checkbox"
            checked={formData.acceptMarketing}
            onChange={handleChange}
            style={{ 
              pointerEvents: 'auto',
              zIndex: 10,
              position: 'relative'
            }}
            className="mt-1 h-4 w-4 bg-black/60 border-white/20 rounded focus:ring-green-500 focus:ring-2 cursor-pointer accent-green-500"
          />
          <label 
            htmlFor="acceptMarketing" 
            className="text-sm text-white/60 leading-relaxed cursor-pointer"
            style={{ 
              pointerEvents: 'auto',
              zIndex: 10,
              position: 'relative'
            }}
          >
            I would like to receive trading tips, market updates, and promotional emails
          </label>
        </div>
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={isLoading}
        className="w-full buy-button py-3 px-4 rounded-xl font-semibold text-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 mt-6"
      >
        {isLoading ? (
          <>
            <Loader2 className="h-5 w-5 animate-spin" />
            <span>Creating Account...</span>
          </>
        ) : (
          <span>Create Account</span>
        )}
      </button>
    </form>
  )
} 
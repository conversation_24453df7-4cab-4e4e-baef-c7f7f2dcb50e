import { Challenge, ChallengeInsert, ChallengeUpdate, ApiResponse } from '@/types/database'
import { supabaseAdmin } from '@/lib/supabase/server'

/**
 * Get all active challenges
 */
export async function getChallenges(): Promise<ApiResponse<Challenge[]>> {
  try {
    const { data, error } = await supabaseAdmin
      .from('challenges')
      .select('*')
      .eq('is_active', true)
      .order('price', { ascending: true })

    if (error) {
      console.error('Error fetching challenges:', error)
      return { success: false, error: 'Failed to fetch challenges' }
    }

    return { success: true, data: data || [] }
  } catch (error) {
    console.error('Unexpected error fetching challenges:', error)
    return { success: false, error: 'Unexpected error occurred' }
  }
}

/**
 * Get a specific challenge by ID
 */
export async function getChallengeById(id: string): Promise<ApiResponse<Challenge>> {
  try {
    const { data, error } = await supabaseAdmin
      .from('challenges')
      .select('*')
      .eq('id', id)
      .eq('is_active', true)
      .single()

    if (error) {
      console.error('Error fetching challenge:', error)
      return { success: false, error: 'Challenge not found' }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Unexpected error fetching challenge:', error)
    return { success: false, error: 'Unexpected error occurred' }
  }
}

/**
 * Create a new challenge (admin only)
 */
export async function createChallenge(challengeData: ChallengeInsert): Promise<ApiResponse<Challenge>> {
  try {
    const { data, error } = await supabaseAdmin
      .from('challenges')
      .insert(challengeData)
      .select()
      .single()

    if (error) {
      console.error('Error creating challenge:', error)
      return { success: false, error: 'Failed to create challenge' }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Unexpected error creating challenge:', error)
    return { success: false, error: 'Unexpected error occurred' }
  }
}

/**
 * Update a challenge (admin only)
 */
export async function updateChallenge(id: string, updateData: ChallengeUpdate): Promise<ApiResponse<Challenge>> {
  try {
    const { data, error } = await supabaseAdmin
      .from('challenges')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating challenge:', error)
      return { success: false, error: 'Failed to update challenge' }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Unexpected error updating challenge:', error)
    return { success: false, error: 'Unexpected error occurred' }
  }
}

/**
 * Deactivate a challenge (admin only)
 */
export async function deactivateChallenge(id: string): Promise<ApiResponse<Challenge>> {
  try {
    const { data, error } = await supabaseAdmin
      .from('challenges')
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error deactivating challenge:', error)
      return { success: false, error: 'Failed to deactivate challenge' }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Unexpected error deactivating challenge:', error)
    return { success: false, error: 'Unexpected error occurred' }
  }
} 
import { Request, Response, NextFunction } from 'express'
import { v4 as uuidv4 } from 'uuid'

export interface ApiError extends Error {
  statusCode?: number
  code?: string
  isOperational?: boolean
  details?: any
}

export class AppError extends Error implements ApiError {
  public readonly statusCode: number
  public readonly code: string
  public readonly isOperational: boolean
  public readonly details?: any

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'SYS_001',
    isOperational: boolean = true,
    details?: any
  ) {
    super(message)
    
    this.statusCode = statusCode
    this.code = code
    this.isOperational = isOperational
    this.details = details

    Error.captureStackTrace(this, this.constructor)
  }
}

export const errorHandler = (
  error: ApiError,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  const requestId = uuidv4()
  const timestamp = new Date().toISOString()

  // Default error values
  let statusCode = error.statusCode || 500
  let code = error.code || 'SYS_001'
  let message = error.message || 'Internal server error'
  let details = error.details

  // Log the error
  console.error(`[${timestamp}] [${requestId}] Error:`, {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: (req as any).user?.id
  })

  // Handle specific error types
  if (error.name === 'ValidationError') {
    statusCode = 400
    code = 'VAL_001'
    message = 'Validation failed'
    details = error.details || error.message
  }

  if (error.name === 'CastError') {
    statusCode = 400
    code = 'VAL_003'
    message = 'Invalid format provided'
  }

  if (error.name === 'MongoError' && (error as any).code === 11000) {
    statusCode = 409
    code = 'VAL_002'
    message = 'Duplicate field value entered'
  }

  if (error.name === 'JsonWebTokenError') {
    statusCode = 401
    code = 'AUTH_001'
    message = 'Invalid token'
  }

  if (error.name === 'TokenExpiredError') {
    statusCode = 401
    code = 'AUTH_002'
    message = 'Token expired'
  }

  // Handle Supabase errors
  if (error.message?.includes('JWT')) {
    statusCode = 401
    code = 'AUTH_001'
    message = 'Authentication failed'
  }

  // Handle trading-specific errors
  if (error.message?.includes('insufficient margin')) {
    statusCode = 400
    code = 'TRADE_001'
    message = 'Insufficient margin for trade'
  }

  if (error.message?.includes('market closed')) {
    statusCode = 400
    code = 'TRADE_002'
    message = 'Market is currently closed'
  }

  if (error.message?.includes('risk limit')) {
    statusCode = 400
    code = 'TRADE_004'
    message = 'Trade exceeds risk limits'
  }

  // Don't expose sensitive information in production
  if (process.env.NODE_ENV === 'production' && statusCode === 500) {
    message = 'Internal server error'
    details = undefined
  }

  // Send error response
  res.status(statusCode).json({
    success: false,
    error: {
      code,
      message,
      ...(details && { details }),
      timestamp,
      requestId,
      ...(process.env.NODE_ENV === 'development' && { 
        stack: error.stack,
        path: req.path 
      })
    }
  })

  // Don't continue to next middleware
  return
}

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

// Create specific error types
export const createValidationError = (message: string, details?: any): AppError => {
  return new AppError(message, 400, 'VAL_001', true, details)
}

export const createAuthError = (message: string = 'Authentication failed'): AppError => {
  return new AppError(message, 401, 'AUTH_001', true)
}

export const createForbiddenError = (message: string = 'Access forbidden'): AppError => {
  return new AppError(message, 403, 'AUTH_003', true)
}

export const createNotFoundError = (message: string = 'Resource not found'): AppError => {
  return new AppError(message, 404, 'NOT_FOUND', true)
}

export const createTradingError = (message: string, code: string = 'TRADE_001'): AppError => {
  return new AppError(message, 400, code, true)
}

export const createRiskError = (message: string): AppError => {
  return new AppError(message, 400, 'TRADE_004', true)
}

export const createInternalError = (message: string = 'Internal server error'): AppError => {
  return new AppError(message, 500, 'SYS_001', false)
} 
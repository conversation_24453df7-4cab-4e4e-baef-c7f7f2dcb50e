const API_BASE_URL = 'http://localhost:8000'

interface TestResult {
  endpoint: string
  method: string
  status: number
  success: boolean
  error?: string
  responseTime: number
}

async function testEndpoint(
  endpoint: string, 
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
  body?: any,
  headers?: Record<string, string>
): Promise<TestResult> {
  const startTime = Date.now()
  
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: body ? JSON.stringify(body) : null
    })

    const responseTime = Date.now() - startTime
    
    return {
      endpoint,
      method,
      status: response.status,
      success: response.ok,
      responseTime
    }
  } catch (error) {
    const responseTime = Date.now() - startTime
    return {
      endpoint,
      method,
      status: 0,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      responseTime
    }
  }
}

async function runAPITests() {
  console.log('🧪 Starting API endpoint tests...\n')

  const tests: Array<{
    name: string
    endpoint: string
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
    body?: any
    headers?: Record<string, string>
  }> = [
    // Health check
    { name: 'Health Check', endpoint: '/health' },
    
    // Analytics endpoints (will fail without auth, but should return 401 not 500)
    { name: 'Analytics Performance', endpoint: '/api/analytics/performance/test-account' },
    { name: 'Analytics Equity Curve', endpoint: '/api/analytics/equity-curve/test-account' },
    { name: 'Analytics Daily P&L', endpoint: '/api/analytics/daily-pnl/test-account' },
    
    // Risk management endpoints
    { name: 'Risk Current Metrics', endpoint: '/api/risk/current-metrics/test-account' },
    { name: 'Risk Rules', endpoint: '/api/risk/rules/test-account' },
    { name: 'Risk Alerts', endpoint: '/api/risk/alerts/test-account' },
    
    // User endpoints
    { name: 'User Profile', endpoint: '/api/users/profile' },
    { name: 'Trading Preferences', endpoint: '/api/users/trading-preferences' },
    { name: 'Notification Settings', endpoint: '/api/users/notification-settings' },
    
    // Support endpoints
    { name: 'Support FAQ', endpoint: '/api/support/faq' },
    { name: 'Support Tickets', endpoint: '/api/support/tickets' },
    { name: 'Support Guides', endpoint: '/api/support/guides' },
    
    // Payment endpoints
    { name: 'Payment History', endpoint: '/api/payments/history' },
    
    // MT5 endpoints
    { name: 'MT5 Symbols', endpoint: '/api/mt5/symbols' },
    { name: 'MT5 Market Data', endpoint: '/api/mt5/market-data/EURUSD' },
  ]

  const results: TestResult[] = []

  for (const test of tests) {
    process.stdout.write(`Testing ${test.name}... `)
    
    const result = await testEndpoint(
      test.endpoint,
      test.method,
      test.body,
      test.headers
    )
    
    results.push(result)
    
    if (result.success) {
      console.log(`✅ ${result.status} (${result.responseTime}ms)`)
    } else if (result.status === 401) {
      console.log(`🔒 ${result.status} - Auth required (${result.responseTime}ms)`)
    } else if (result.status === 0) {
      console.log(`❌ Connection failed - ${result.error}`)
    } else {
      console.log(`⚠️  ${result.status} (${result.responseTime}ms)`)
    }
  }

  // Summary
  console.log('\n📊 Test Summary:')
  console.log('================')
  
  const successful = results.filter(r => r.success).length
  const authRequired = results.filter(r => r.status === 401).length
  const connectionFailed = results.filter(r => r.status === 0).length
  const otherErrors = results.filter(r => !r.success && r.status !== 401 && r.status !== 0).length
  
  console.log(`✅ Successful: ${successful}`)
  console.log(`🔒 Auth Required (Expected): ${authRequired}`)
  console.log(`❌ Connection Failed: ${connectionFailed}`)
  console.log(`⚠️  Other Errors: ${otherErrors}`)
  
  const avgResponseTime = results
    .filter(r => r.status !== 0)
    .reduce((sum, r) => sum + r.responseTime, 0) / results.filter(r => r.status !== 0).length
  
  console.log(`⏱️  Average Response Time: ${avgResponseTime.toFixed(0)}ms`)
  
  if (connectionFailed > 0) {
    console.log('\n❌ Backend server appears to be down or not accessible')
    console.log('   Make sure to run: npm run dev:server')
  } else if (successful > 0 || authRequired > 0) {
    console.log('\n✅ Backend server is running and responding!')
    if (authRequired > 0) {
      console.log('   Most endpoints require authentication (this is expected)')
    }
  }

  // Detailed error report
  const errors = results.filter(r => !r.success && r.status !== 401)
  if (errors.length > 0) {
    console.log('\n🔍 Detailed Error Report:')
    console.log('========================')
    errors.forEach(error => {
      console.log(`${error.endpoint} (${error.method}): ${error.status} - ${error.error || 'Unknown error'}`)
    })
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAPITests()
    .then(() => {
      console.log('\nAPI testing completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('API testing failed:', error)
      process.exit(1)
    })
}

export { runAPITests }
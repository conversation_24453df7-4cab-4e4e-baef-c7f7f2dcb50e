'use client'

import Link from 'next/link'
import { TrendingUp, Home, ArrowLeft } from 'lucide-react'

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black">
        <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 via-transparent to-blue-500/5 animate-pulse" />
      </div>

      {/* Floating Background Elements */}
      <div className="absolute top-20 left-20 w-32 h-32 bg-green-500/10 rounded-full blur-xl animate-pulse" />
      <div className="absolute bottom-20 right-20 w-48 h-48 bg-blue-500/10 rounded-full blur-xl animate-pulse delay-1000" />
      <div className="absolute top-1/2 left-10 w-24 h-24 bg-yellow-500/10 rounded-full blur-xl animate-pulse delay-500" />

      {/* Main Content */}
      <div className="relative z-10 text-center max-w-lg mx-auto px-6">
        {/* Logo */}
        <div className="flex items-center justify-center space-x-3 mb-8">
          <div className="flex h-12 w-12 items-center justify-center rounded-xl glossy-white">
            <TrendingUp className="h-8 w-8 text-black" />
          </div>
          <span className="text-3xl font-bold text-gradient-gold">PropFirm</span>
        </div>

        {/* 404 Display */}
        <div className="glass-card p-12 neon-border">
          <h1 className="text-6xl font-bold text-white mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-white mb-4">Page Not Found</h2>
          <p className="text-white/70 mb-8">
            The page you&apos;re looking for doesn&apos;t exist or has been moved.
          </p>
          
          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <Link href="/">
              <button className="professional-button flex items-center space-x-2 px-6 py-3">
                <Home className="h-5 w-5" />
                <span>Go Home</span>
              </button>
            </Link>
            <button 
              onClick={() => window.history.back()}
              className="professional-button-dark flex items-center space-x-2 px-6 py-3"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Go Back</span>
            </button>
          </div>
        </div>

        {/* Help Text */}
        <div className="mt-8">
          <p className="text-white/50 text-sm">
            If you believe this is an error, please{' '}
            <Link href="/contact" className="text-green-400 hover:text-green-300 transition-colors">
              contact support
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
} 
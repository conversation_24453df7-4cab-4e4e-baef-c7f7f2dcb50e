import { Router, Response } from 'express'
import { z } from 'zod'
import { supabase } from '../../lib/supabase/server'
import { validateRequest, ValidationRequest } from '../middleware/validation'
import { asyncHandler, createValidationError, createNotFoundError, createInternalError } from '../middleware/errorHandler'
import { AuthenticatedRequest } from '../middleware/auth'

const router = Router()

// Validation schemas
const createMT5AccountSchema = z.object({
  challengeId: z.string().uuid('Invalid challenge ID format'),
  accountType: z.enum(['demo', 'live']),
  leverage: z.number().min(1).max(1000),
  currency: z.string().length(3, 'Currency must be a 3-letter ISO code')
})

const mt5LoginSchema = z.object({
  login: z.string().min(1, 'Login is required')
})

const openTradeSchema = z.object({
  accountLogin: z.string().min(1, 'Account login is required'),
  symbol: z.string().min(1, 'Symbol is required'),
  type: z.enum(['buy', 'sell', 'buy_limit', 'sell_limit', 'buy_stop', 'sell_stop']),
  volume: z.number().min(0.01).max(100),
  price: z.number().min(0).optional(),
  stopLoss: z.number().min(0).optional(),
  takeProfit: z.number().min(0).optional(),
  comment: z.string().max(100).optional()
})

const modifyTradeSchema = z.object({
  stopLoss: z.number().min(0).optional(),
  takeProfit: z.number().min(0).optional(),
  price: z.number().min(0).optional()
})

const closeTradeSchema = z.object({
  volume: z.number().min(0.01).optional(),
  price: z.number().min(0).optional()
})

const ticketSchema = z.object({
  ticket: z.string().min(1, 'Ticket number is required')
})

const symbolSchema = z.object({
  symbol: z.string().min(1, 'Symbol is required')
})

// Type definitions for MT5 responses
interface MT5TradeResponse {
  ticket: number;
  openPrice: number;
}

interface MT5MarketData {
  bid: number;
  ask: number;
  last: number;
  volume: number;
  high: number;
  low: number;
}

interface MT5CloseResponse {
  closePrice: number;
  profit: number;
}

// Helper function to verify account ownership
const verifyMT5AccountOwnership = async (login: string, userId: string) => {
  const { data: account, error } = await supabase
    .from('trading_accounts')
    .select('*')
    .eq('mt5_login', login)
    .eq('user_id', userId)
    .single()

  if (error || !account) {
    throw createNotFoundError('MT5 account not found or access denied')
  }

  return account
}

// Helper function to generate MT5 account credentials
const generateMT5Credentials = () => {
  const login = Math.floor(Math.random() * 900000) + 100000 // 6-digit number
  const password = Math.random().toString(36).slice(-8).toUpperCase()
  const server = process.env.MT5_SERVER_NAME || 'PropFirm-Demo'
  
  return { login: login.toString(), password, server }
}

// Helper function to simulate MT5 API calls (replace with real MT5 integration)
const simulateMT5Call = async (operation: string, params: any): Promise<any> => {
  // In a real implementation, this would connect to MT5 server
  // For now, we simulate the responses
  console.log(`MT5 Operation: ${operation}`, params)
  
  switch (operation) {
    case 'create_account':
      return generateMT5Credentials()
    case 'get_account_info':
      return {
        balance: 10000,
        equity: 10000,
        margin: 0,
        freeMargin: 10000,
        marginLevel: 0,
        profit: 0
      }
    case 'open_trade':
      return {
        ticket: Math.floor(Math.random() * 1000000),
        openPrice: params.symbol === 'EURUSD' ? 1.1234 : 1.0000
      }
    case 'close_trade':
      return {
        closePrice: params.symbol === 'EURUSD' ? 1.1235 : 1.0001,
        profit: Math.random() * 100 - 50 // Random profit/loss
      }
    case 'get_market_data':
      return {
        bid: 1.1230,
        ask: 1.1235,
        last: 1.1232,
        volume: 1000,
        high: 1.1250,
        low: 1.1200
      }
    default:
      return { success: true }
  }
}

// POST /api/mt5/accounts/create
router.post('/accounts/create',
  validateRequest({ body: createMT5AccountSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { challengeId, accountType, leverage, currency } = req.validatedBody
    const userId = req.user!.id

    try {
      // Verify user owns the trading account
      const { data: tradingAccount, error: accountError } = await supabase
        .from('trading_accounts')
        .select('*')
        .eq('id', challengeId)
        .eq('user_id', userId)
        .single()

      if (accountError || !tradingAccount) {
        throw createNotFoundError('Trading account not found or access denied')
      }

      // Check if MT5 account already exists for this trading account
      const { data: existingMT5 } = await supabase
        .from('trading_accounts')
        .select('mt5_login')
        .eq('id', challengeId)
        .single()

      if (existingMT5 && existingMT5.mt5_login) {
        throw createValidationError('MT5 account already exists for this trading account')
      }

      // Create MT5 account via API
      const mt5Response = await simulateMT5Call('create_account', {
        accountType,
        leverage,
        currency,
        balance: tradingAccount.balance
      }) as { login: string; password: string; server: string }

      // Store MT5 account details in trading_accounts table
      const { error: updateError } = await supabase
        .from('trading_accounts')
        .update({
          mt5_login: mt5Response.login,
          mt5_password: mt5Response.password,
          mt5_server: mt5Response.server
        })
        .eq('id', challengeId)

      if (updateError) {
        throw createInternalError('Failed to update trading account with MT5 credentials')
      }

      // Create audit log
      await supabase
        .from('audit_logs')
        .insert({
          user_id: userId,
          account_id: challengeId,
          action: 'mt5_account_created',
          details: {
            mt5_login: mt5Response.login,
            account_type: accountType,
            leverage,
            currency
          }
        })

      res.status(201).json({
        success: true,
        data: {
          login: mt5Response.login,
          password: mt5Response.password, // Only returned once
          server: mt5Response.server,
          balance: tradingAccount.balance,
          currency
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/mt5/accounts/:login/info
router.get('/accounts/:login/info',
  validateRequest({ params: mt5LoginSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { login } = req.validatedParams
    const userId = req.user!.id

    try {
      // Verify account ownership
      const mt5Account = await verifyMT5AccountOwnership(login, userId)

      // Get account info from MT5
      const accountInfo = await simulateMT5Call('get_account_info', { login }) as {
        balance: number;
        equity: number;
        margin: number;
        freeMargin: number;
        marginLevel: number;
        profit: number;
      }

      // Update our database with latest info
      await supabase
        .from('trading_accounts')
        .update({
          balance: accountInfo.balance,
          equity: accountInfo.equity,
          free_margin: accountInfo.freeMargin,
          margin_level: accountInfo.marginLevel,
          profit: accountInfo.profit,
          updated_at: new Date().toISOString()
        })
        .eq('mt5_login', login)

      res.json({
        success: true,
        data: {
          login,
          balance: accountInfo.balance,
          equity: accountInfo.equity,
          margin: 0, // Calculated field
          freeMargin: accountInfo.freeMargin,
          marginLevel: accountInfo.marginLevel,
          profit: accountInfo.profit,
          currency: 'USD', // Default currency
          leverage: 100, // Default leverage
          server: mt5Account.mt5_server,
          lastUpdate: new Date().toISOString()
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// POST /api/mt5/trades/open
router.post('/trades/open',
  validateRequest({ body: openTradeSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { accountLogin, symbol, type, volume, price, stopLoss, takeProfit, comment } = req.validatedBody
    const userId = req.user!.id

    try {
      // Verify account ownership
      await verifyMT5AccountOwnership(accountLogin, userId)

      // Open trade via MT5 API
      const tradeResponse = await simulateMT5Call('open_trade', {
        login: accountLogin,
        symbol,
        type,
        volume,
        price,
        stopLoss,
        takeProfit,
        comment
      }) as MT5TradeResponse

      // Store trade record
      const { data: account } = await supabase
        .from('trading_accounts')
        .select('id')
        .eq('mt5_login', accountLogin)
        .single()

      if (!account) {
        throw createNotFoundError('Trading account not found')
      }

      const { error: tradeError } = await supabase
        .from('trades')
        .insert({
          account_id: account.id,
          mt5_ticket: parseInt(tradeResponse.ticket.toString()),
          symbol,
          type,
          volume,
          open_price: tradeResponse.openPrice,
          stop_loss: stopLoss,
          take_profit: takeProfit,
          comment: comment || '',
          status: 'open',
          open_time: new Date().toISOString()
        })

      if (tradeError) {
        console.error('Failed to store trade record:', tradeError)
        // Don't throw error as MT5 trade was successful
      }

      res.status(201).json({
        success: true,
        data: {
          ticket: tradeResponse.ticket,
          symbol,
          type,
          volume,
          openPrice: tradeResponse.openPrice,
          stopLoss,
          takeProfit,
          openTime: new Date().toISOString()
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// PUT /api/mt5/trades/:ticket/modify
router.put('/trades/:ticket/modify',
  validateRequest({ 
    params: ticketSchema,
    body: modifyTradeSchema 
  }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { ticket } = req.validatedParams
    const { stopLoss, takeProfit, price } = req.validatedBody
    const userId = req.user!.id

    try {
      // Verify trade ownership
      const { data: trade, error: tradeError } = await supabase
        .from('trades')
        .select(`
          *,
          trading_accounts!inner(user_id)
        `)
        .eq('mt5_ticket', ticket)
        .single()

      if (tradeError || !trade || trade.trading_accounts.user_id !== userId) {
        throw createNotFoundError('Trade not found or access denied')
      }

      // Modify trade via MT5 API
      await simulateMT5Call('modify_trade', {
        ticket,
        stopLoss,
        takeProfit,
        price
      })

      // Update trade record
      const { error: updateError } = await supabase
        .from('trades')
        .update({
          stop_loss: stopLoss,
          take_profit: takeProfit,
          updated_at: new Date().toISOString()
        })
        .eq('mt5_ticket', ticket)

      if (updateError) {
        console.error('Failed to update trade record:', updateError)
      }

      res.json({
        success: true,
        data: {
          ticket,
          stopLoss,
          takeProfit,
          price,
          message: 'Trade modified successfully'
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// DELETE /api/mt5/trades/:ticket/close
router.delete('/trades/:ticket/close',
  validateRequest({ 
    params: ticketSchema,
    body: closeTradeSchema 
  }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { ticket } = req.validatedParams
    const { volume, price } = req.validatedBody
    const userId = req.user!.id

    try {
      // Verify trade ownership
      const { data: trade, error: tradeError } = await supabase
        .from('trades')
        .select(`
          *,
          trading_accounts!inner(user_id)
        `)
        .eq('mt5_ticket', ticket)
        .single()

      if (tradeError || !trade || trade.trading_accounts.user_id !== userId) {
        throw createNotFoundError('Trade not found or access denied')
      }

      // Close trade via MT5 API
      const closeResponse = await simulateMT5Call('close_trade', {
        ticket,
        volume: volume || trade.volume,
        price
      }) as MT5CloseResponse

      // Update trade record
      const { error: updateError } = await supabase
        .from('trades')
        .update({
          status: 'closed',
          close_price: closeResponse.closePrice || price || trade.open_price,
          close_time: new Date().toISOString(),
          profit: closeResponse.profit || 0,
          updated_at: new Date().toISOString()
        })
        .eq('mt5_ticket', ticket)

      if (updateError) {
        console.error('Failed to update trade record:', updateError)
      }

      res.json({
        success: true,
        data: {
          ticket,
          closePrice: closeResponse.closePrice || price || trade.open_price,
          profit: closeResponse.profit || 0,
          message: 'Trade closed successfully'
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/mt5/trades/:login/positions
router.get('/trades/:login/positions',
  validateRequest({ params: mt5LoginSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { login } = req.validatedParams
    const userId = req.user!.id

    try {
      // Verify account ownership
      await verifyMT5AccountOwnership(login, userId)

      // Get open positions
      const { data: positions, error } = await supabase
        .from('trades')
        .select('*')
        .eq('account_id', login) // In real implementation, map login to account_id
        .eq('status', 'open')
        .order('open_time', { ascending: false })

      if (error) {
        throw createInternalError('Failed to fetch open positions')
      }

      res.json({
        success: true,
        data: { positions: positions || [] }
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/mt5/trades/:login/history
router.get('/trades/:login/history',
  validateRequest({ params: mt5LoginSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { login } = req.validatedParams
    const userId = req.user!.id

    try {
      // Verify account ownership
      await verifyMT5AccountOwnership(login, userId)

      // Get trade history
      const { data: history, error } = await supabase
        .from('trades')
        .select('*')
        .eq('account_id', login) // In real implementation, map login to account_id
        .eq('status', 'closed')
        .order('close_time', { ascending: false })
        .limit(100) // Limit to last 100 trades

      if (error) {
        throw createInternalError('Failed to fetch trade history')
      }

      res.json({
        success: true,
        data: { history: history || [] }
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/mt5/market-data/:symbol
router.get('/market-data/:symbol',
  validateRequest({ params: symbolSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { symbol } = req.validatedParams

    try {
      // Get market data from MT5
      const marketData = await simulateMT5Call('get_market_data', { symbol }) as MT5MarketData

      res.json({
        success: true,
        data: {
          symbol,
          bid: marketData.bid,
          ask: marketData.ask,
          last: marketData.last,
          volume: marketData.volume,
          high: marketData.high,
          low: marketData.low,
          change: 0.0012,
          changePercent: 0.11,
          timestamp: new Date().toISOString()
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/mt5/symbols
router.get('/symbols',
  asyncHandler(async (_req: AuthenticatedRequest, res: Response) => {
    try {
      // Get available symbols (in real implementation, fetch from MT5)
      const symbols = [
        {
          name: 'EURUSD',
          description: 'Euro vs US Dollar',
          currency: 'USD',
          digits: 5,
          point: 0.00001,
          spread: 1.5,
          minVolume: 0.01,
          maxVolume: 100,
          volumeStep: 0.01
        },
        {
          name: 'GBPUSD',
          description: 'British Pound vs US Dollar',
          currency: 'USD',
          digits: 5,
          point: 0.00001,
          spread: 2.0,
          minVolume: 0.01,
          maxVolume: 100,
          volumeStep: 0.01
        },
        {
          name: 'USDJPY',
          description: 'US Dollar vs Japanese Yen',
          currency: 'JPY',
          digits: 3,
          point: 0.001,
          spread: 1.8,
          minVolume: 0.01,
          maxVolume: 100,
          volumeStep: 0.01
        },
        {
          name: 'AUDUSD',
          description: 'Australian Dollar vs US Dollar',
          currency: 'USD',
          digits: 5,
          point: 0.00001,
          spread: 2.2,
          minVolume: 0.01,
          maxVolume: 100,
          volumeStep: 0.01
        }
      ]

      res.json({
        success: true,
        data: { symbols }
      })

    } catch (error) {
      throw error
    }
  })
)

export default router 
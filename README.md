# Prop Bully - Elite Trading Platform

A comprehensive proprietary trading firm platform that connects traders to MetaTrader 5 while providing a unique web-based trading interface. Traders progress through challenge phases before gaining access to live capital.

## 🚀 Project Status

✅ **FOUNDATION COMPLETE** - Dark, sharp, polished Next.js application with TypeScript
- Modern, responsive design with professional trading aesthetics
- Complete project structure with all essential dependencies
- Development server running on http://localhost:3000

## 🏗️ Tech Stack

- **Frontend**: Next.js 15+ with TypeScript and App Router
- **Styling**: Tailwind CSS with custom dark theme and trading-specific design system
- **Backend**: Node.js API with planned Supabase integration
- **Database**: Supabase (PostgreSQL with real-time subscriptions) - *Coming Soon*
- **Payments**: Stripe integration - *Coming Soon*
- **Trading**: MetaTrader 5 integration via custom bridge - *Planned*
- **Authentication**: Supabase Auth with multi-factor authentication - *Coming Soon*

## 🎨 Design Features

### Dark Theme Optimized for Trading
- Professional dark color scheme designed for extended trading sessions
- Trading-specific colors: profit green, loss red, warning amber
- Glass morphism effects and subtle animations
- Responsive grid layouts optimized for trading data
- Custom CSS classes for trading components

### UI Components
- Reusable button component with trading-specific variants (buy/sell)
- Comprehensive utility functions for class name management
- Scalable design system with consistent spacing and typography

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm/yarn
- Supabase account and project
- Stripe account (test mode for development)
- MetaTrader 5 (for trading integration)

### Environment Setup

1. **Clone and install dependencies**:
```bash
git clone <repository-url>
cd prop-bully-platform
npm install
```

2. **Environment variables** (create `.env.local`):
```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Application
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
NODE_ENV=development

# MT5 Integration (to be configured)
MT5_SERVER_URL=your_mt5_server
MT5_API_KEY=your_mt5_api_key
```

3. **Database setup**:
```bash
# Apply schema to Supabase (see Database Setup section above)
# Then seed development data
npm run db:seed
```

4. **Start development server**:
```bash
npm run dev:both
```

### Backend Development Setup

The backend API server runs separately from the Next.js frontend. Here's how to set it up:

1. **Backend Server Setup**:
```bash
# Start backend server only
npm run dev:server

# Start both frontend and backend
npm run dev:both
```

2. **Backend Environment Variables**:
Add these additional variables to your `.env.local`:
```env
# API Server Configuration
API_PORT=8000
JWT_SECRET=your_jwt_secret_for_api_auth

# Redis Configuration (optional for caching)
REDIS_URL=redis://localhost:6379

# Email Service Configuration (optional)
SENDGRID_API_KEY=your_sendgrid_api_key
FROM_EMAIL=<EMAIL>
```

3. **Database Seeding**:
```bash
# Populate database with sample data for testing
npm run db:seed
```

4. **API Testing**:
The backend server runs on `http://localhost:8000` with the following endpoints:
- Health check: `GET /health`
- Analytics: `GET /api/analytics/*`
- Risk Management: `GET /api/risk/*`
- User Settings: `GET /api/users/*`
- Help & Support: `GET /api/support/*`
- Payment Processing: `POST /api/payments/*`
- MT5 Integration: `GET /api/mt5/*`

5. **WebSocket Testing**:
WebSocket server runs on the same port with authentication:
```javascript
// Connect to WebSocket
const socket = io('http://localhost:8000', {
  auth: {
    token: 'your_jwt_token'
  }
})
```

### Current Backend Status

✅ **Completed Systems**:
- Analytics API System (performance metrics, charts, insights)
- Risk Management API System (real-time monitoring, rules, alerts)
- User Settings API System (profile, preferences, security)
- Help & Support API System (FAQ, tickets, documentation)
- Payment Processing API System (Stripe integration, webhooks)
- MT5 Integration API System (simulation functions ready)
- WebSocket Server Setup (real-time data streaming)
- Database Seeding Script (sample data for testing)

🔄 **Next Steps**:
1. Set up Supabase project and configure environment variables
2. Test backend server startup and API endpoints
3. Connect frontend components to backend APIs
4. Implement real-time WebSocket connections in frontend
5. Replace MT5 simulation functions with real integration

### Troubleshooting

**Backend won't start**:
- Check that all required environment variables are set
- Verify Supabase credentials are correct
- Ensure no other process is using port 8000

**Database connection issues**:
- Verify Supabase URL and service role key
- Check that database schema is properly set up
- Run the seeding script to populate test data

**API authentication errors**:
- Ensure JWT_SECRET is set in environment variables
- Verify Supabase JWT secret matches your project
- Check that user sessions are properly configured

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── globals.css        # Global styles with trading theme
│   ├── layout.tsx         # Root layout with dark theme
│   └── page.tsx           # Landing page with hero section
├── components/            # Reusable UI components
│   └── ui/               # Base UI components
│       └── button.tsx    # Button with trading variants
├── lib/                  # Utility functions and configurations
│   └── utils/
│       └── cn.ts         # Class name utility function
└── types/                # TypeScript type definitions
    └── index.ts          # Complete trading platform types
```

## 🎯 Key Features Implemented

### Landing Page
- Professional hero section with trading platform branding
- Feature showcase with trading-specific benefits
- Glass morphism cards with hover effects
- Responsive navigation with authentication links
- Professional footer with trading disclaimers

### Type System
- Comprehensive TypeScript interfaces for all trading entities
- User management, trading accounts, challenges, and market data types
- Payment processing and risk management types
- API response standardization

### Design System
- Trading-specific CSS utilities and components
- Profit/loss color coding throughout the interface
- Animated elements for live data updates
- Professional typography with monospace fonts for data

## 🔄 Next Steps

### Immediate (Next Session)
1. **Database Schema Design** - Create Supabase tables and relationships
2. **Authentication System** - Implement user registration/login
3. **Environment Configuration** - Set up Supabase and Stripe keys

### Coming Soon
- Challenge purchase flow with Stripe integration
- Trading dashboard with real-time data
- MT5 integration for live trading
- Risk management system
- Performance analytics

## 🛡️ Security Features (Planned)

- Multi-factor authentication via Supabase Auth
- Row Level Security (RLS) policies for data access
- API rate limiting and request validation
- Comprehensive audit logging
- PCI-compliant payment processing

## 📊 Trading Features (Planned)

### Trader Journey
1. Registration & KYC verification
2. Challenge selection and payment
3. Trading evaluation phase
4. Performance monitoring
5. Funding transition for successful traders

### Risk Management
- Real-time drawdown monitoring
- Automated position limits
- Daily and total loss controls
- Risk score calculation

## 🤝 Development Environment

This project uses **Node.js package management** with automatic dependency isolation through `node_modules`. No Python virtual environment is needed.

### Environment Isolation
- **Package Management**: npm with package-lock.json for version locking
- **TypeScript**: Strict mode enabled for type safety
- **Linting**: ESLint with TypeScript rules
- **Styling**: Tailwind CSS with design token system

## 📞 Current Status

**✅ Foundation Complete**: The project foundation is fully implemented with a dark, polished interface ready for trading platform development.

**🚧 Next Phase**: Database schema design and authentication system implementation.

**💻 Development Server**: Running on http://localhost:3000 with hot reload enabled.

---

**⚠️ Trading Disclaimer**: This platform is designed for proprietary trading firms. All trading involves substantial risk and may not be suitable for all investors. Past performance does not guarantee future results.

## 🗄️ Database Setup

### **CRITICAL: Database Schema Must Be Applied First**

Before running the backend server or seeding data, you must apply the database schema to your Supabase project.

#### **Option 1: Using Supabase Dashboard (Recommended)**
1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Copy the contents of `supabase/schema.sql` 
4. Paste and run the SQL in the editor
5. Verify all tables were created successfully

#### **Option 2: Using Supabase CLI (Advanced)**
```bash
# Install Supabase CLI if not already installed
npm install -g supabase

# Initialize Supabase in project (if not done)
supabase init

# Link to your project
supabase link --project-ref your-project-ref

# Apply migrations
supabase db push
```

#### **Option 3: Manual Table Creation**
If you prefer to create tables manually, use the Supabase dashboard to create these tables:
- `users` (extends auth.users)
- `challenges`
- `trading_accounts` 
- `trades`
- `payments`
- `risk_rules`
- `notifications`
- `audit_logs`
- `market_data`
- `trading_sessions`
- `performance_metrics`

### **Step 2: Environment Variables**

Add these **REQUIRED** variables to your `.env.local`:

```env
# Add these to your existing .env.local file:
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_from_supabase_settings
JWT_SECRET=your-jwt-secret-minimum-32-characters-long-for-security
API_PORT=8000
```

**To get your Service Role Key:**
1. Go to your Supabase project dashboard
2. Navigate to **Settings > API**
3. Copy the `service_role` key (not the anon key)
4. Add it to your `.env.local` file

### **Step 3: Start the Backend Server**

```bash
# Start backend server only
npm run dev:server

# OR start both frontend and backend
npm run dev:both
```

### **Step 4: Seed the Database (Optional)**

```bash
# Populate with sample data for testing
npm run db:seed
```

### **Step 5: Test the API**

```bash
# Test all endpoints
npm run test:api
```

## 🔧 Troubleshooting

### **Database Schema Issues**
If you get "column not found" errors:
1. Verify the schema was applied correctly
2. Check that all tables exist in Supabase dashboard
3. Ensure you're using the correct Supabase project

### **Environment Variable Issues**
If you get "missing environment variables" errors:
1. Verify `.env.local` exists in project root
2. Restart the server after adding new variables
3. Check that variable names match exactly

### **Port Already in Use**
If port 8000 is in use:
```bash
# Windows
netstat -ano | findstr :8000
taskkill /PID [PID_NUMBER] /F

# Or change port in .env.local
API_PORT=8001
```

## 📊 Expected Results

After proper setup, you should see:
- ✅ Health check: `GET /health` returns 200
- ✅ All API endpoints return proper responses (not 404)
- ✅ Database seeding completes without errors
- ✅ WebSocket connections work 
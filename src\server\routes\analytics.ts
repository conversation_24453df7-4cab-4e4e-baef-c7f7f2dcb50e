import { Router, Response } from 'express'
import { z } from 'zod'
import { supabase } from '../../lib/supabase/server'
import { validateRequest, ValidationRequest } from '../middleware/validation'
import { asyncHand<PERSON>, createNotFoundError, createInternalError } from '../middleware/errorHandler'
import { AuthenticatedRequest } from '../middleware/auth'
import { broadcastAnalyticsUpdate } from '../websocket/socketManager'
import { io } from '../index'

const router = Router()

// Validation schemas
const timeframeSchema = z.enum(['1D', '7D', '30D', '90D', 'ALL'])

const analyticsQuerySchema = z.object({
  timeframe: timeframeSchema.optional().default('30D'),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional()
})

const accountIdSchema = z.object({
  accountId: z.string().uuid('Invalid account ID format')
})

// Helper function to calculate date range based on timeframe
const getDateRange = (timeframe: string, startDate?: string, endDate?: string) => {
  const now = new Date()
  let start: Date
  let end: Date = endDate ? new Date(endDate) : now

  if (startDate) {
    start = new Date(startDate)
  } else {
    switch (timeframe) {
      case '1D':
        start = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case '7D':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30D':
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90D':
        start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case 'ALL':
      default:
        start = new Date('2020-01-01') // Far back date for all data
        break
    }
  }

  return { start: start.toISOString(), end: end.toISOString() }
}

// Helper function to verify account ownership
const verifyAccountOwnership = async (accountId: string, userId: string) => {
  const { data: account, error } = await supabase
    .from('trading_accounts')
    .select('id, user_id')
    .eq('id', accountId)
    .eq('user_id', userId)
    .single()

  if (error || !account) {
    throw createNotFoundError('Trading account not found or access denied')
  }

  return account
}

// GET /api/analytics/performance/:accountId
router.get('/performance/:accountId',
  validateRequest({ 
    params: accountIdSchema,
    query: analyticsQuerySchema 
  }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { accountId } = req.validatedParams
    const { timeframe, startDate, endDate } = req.validatedQuery
    const userId = req.user!.id

    // Verify account ownership
    await verifyAccountOwnership(accountId, userId)

    const { start, end } = getDateRange(timeframe, startDate, endDate)

    try {
      // Get trades within the specified period
      const { data: trades, error: tradesError } = await supabase
        .from('trades')
        .select('*')
        .eq('account_id', accountId)
        .eq('status', 'closed')
        .gte('close_time', start)
        .lte('close_time', end)
        .order('close_time', { ascending: true })

      if (tradesError) {
        throw createInternalError('Failed to fetch trade data')
      }

      // Calculate performance metrics
      const totalTrades = trades.length
      const winningTrades = trades.filter(trade => (trade.profit || 0) > 0).length
      const losingTrades = trades.filter(trade => (trade.profit || 0) < 0).length
      const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0

      const profits = trades.map(trade => trade.profit || 0)
      const wins = profits.filter(p => p > 0)
      const losses = profits.filter(p => p < 0)

      const avgWin = wins.length > 0 ? wins.reduce((sum, p) => sum + p, 0) / wins.length : 0
      const avgLoss = losses.length > 0 ? Math.abs(losses.reduce((sum, p) => sum + p, 0) / losses.length) : 0
      const profitFactor = avgLoss > 0 ? avgWin / avgLoss : 0

      const totalProfit = profits.reduce((sum, p) => sum + p, 0)
      
      // Calculate drawdown
      let runningBalance = 0
      let peak = 0
      let maxDrawdown = 0

      for (const trade of trades) {
        runningBalance += trade.profit || 0
        if (runningBalance > peak) {
          peak = runningBalance
        }
        const drawdown = peak - runningBalance
        if (drawdown > maxDrawdown) {
          maxDrawdown = drawdown
        }
      }

      // Get initial balance for ROI calculation
      const { data: account, error: accountError } = await supabase
        .from('trading_accounts')
        .select('balance')
        .eq('id', accountId)
        .single()

      if (accountError) {
        throw createInternalError('Failed to fetch account data')
      }

      const initialBalance = account.balance || 0
      const returnOnInvestment = initialBalance > 0 ? (totalProfit / initialBalance) * 100 : 0

      // Calculate Sharpe ratio (simplified version)
      const dailyReturns = trades.map(trade => (trade.profit || 0) / initialBalance)
      const avgDailyReturn = dailyReturns.length > 0 ? dailyReturns.reduce((sum, r) => sum + r, 0) / dailyReturns.length : 0
      const variance = dailyReturns.length > 0 ? 
        dailyReturns.reduce((sum, r) => sum + Math.pow(r - avgDailyReturn, 2), 0) / dailyReturns.length : 0
      const volatility = Math.sqrt(variance)
      const sharpeRatio = volatility > 0 ? avgDailyReturn / volatility : 0

      const performanceData = {
        totalTrades,
        winningTrades,
        losingTrades,
        winRate: Math.round(winRate * 100) / 100,
        avgWin: Math.round(avgWin * 100) / 100,
        avgLoss: Math.round(avgLoss * 100) / 100,
        profitFactor: Math.round(profitFactor * 100) / 100,
        sharpeRatio: Math.round(sharpeRatio * 10000) / 10000,
        maxDrawdown: Math.round(maxDrawdown * 100) / 100,
        returnOnInvestment: Math.round(returnOnInvestment * 100) / 100,
        totalProfit: Math.round(totalProfit * 100) / 100,
        period: {
          start,
          end
        }
      }

      // Broadcast real-time update
      broadcastAnalyticsUpdate(io, accountId, {
        type: 'performance',
        data: performanceData
      })

      res.json({
        success: true,
        data: performanceData
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/analytics/equity-curve/:accountId
router.get('/equity-curve/:accountId',
  validateRequest({ 
    params: accountIdSchema,
    query: analyticsQuerySchema 
  }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { accountId } = req.validatedParams
    const { timeframe, startDate, endDate } = req.validatedQuery
    const userId = req.user!.id

    // Verify account ownership
    await verifyAccountOwnership(accountId, userId)

    const { start, end } = getDateRange(timeframe, startDate, endDate)

    try {
      // Get account initial balance
      const { data: account, error: accountError } = await supabase
        .from('trading_accounts')
        .select('balance, equity')
        .eq('id', accountId)
        .single()

      if (accountError) {
        throw createInternalError('Failed to fetch account data')
      }

      const initialBalance = account.balance || 0
      const currentEquity = account.equity || 0

      // Get trades within the specified period
      const { data: trades, error: tradesError } = await supabase
        .from('trades')
        .select('profit, close_time')
        .eq('account_id', accountId)
        .eq('status', 'closed')
        .gte('close_time', start)
        .lte('close_time', end)
        .order('close_time', { ascending: true })

      if (tradesError) {
        throw createInternalError('Failed to fetch trade data')
      }

      // Build equity curve data
      const equityCurveData: Array<{
        timestamp: string;
        value: number;
        balance: number;
        equity: number;
      }> = []
      let runningBalance = initialBalance
      let runningEquity = initialBalance
      let peak = initialBalance
      let maxDrawdown = 0

      // Add initial point
      equityCurveData.push({
        timestamp: start,
        value: runningEquity,
        balance: runningBalance,
        equity: runningEquity
      })

      // Process each trade
      for (const trade of trades) {
        const profit = trade.profit || 0
        runningBalance += profit
        runningEquity = runningBalance

        if (runningEquity > peak) {
          peak = runningEquity
        }

        const drawdown = peak - runningEquity
        if (drawdown > maxDrawdown) {
          maxDrawdown = drawdown
        }

        equityCurveData.push({
          timestamp: trade.close_time,
          value: Math.round(runningEquity * 100) / 100,
          balance: Math.round(runningBalance * 100) / 100,
          equity: Math.round(runningEquity * 100) / 100
        })
      }

      // Add current point if no trades today
      if (equityCurveData.length === 1 || 
          equityCurveData[equityCurveData.length - 1].timestamp !== end) {
        equityCurveData.push({
          timestamp: end,
          value: Math.round(currentEquity * 100) / 100,
          balance: Math.round(runningBalance * 100) / 100,
          equity: Math.round(currentEquity * 100) / 100
        })
      }

      const totalReturn = initialBalance > 0 ? 
        ((runningEquity - initialBalance) / initialBalance) * 100 : 0

      const returns = equityCurveData.slice(1).map((point, index) => {
        const prevValue = equityCurveData[index].value
        return prevValue > 0 ? (point.value - prevValue) / prevValue : 0
      })

      const avgReturn = returns.length > 0 ? returns.reduce((sum, r) => sum + r, 0) / returns.length : 0
      const variance = returns.length > 0 ? 
        returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length : 0
      const volatility = Math.sqrt(variance) * 100

      const responseData = {
        data: equityCurveData,
        totalReturn: Math.round(totalReturn * 100) / 100,
        maxDrawdown: Math.round(maxDrawdown * 100) / 100,
        volatility: Math.round(volatility * 100) / 100
      }

      res.json({
        success: true,
        data: responseData
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/analytics/daily-pnl/:accountId
router.get('/daily-pnl/:accountId',
  validateRequest({ 
    params: accountIdSchema,
    query: analyticsQuerySchema 
  }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { accountId } = req.validatedParams
    const { timeframe, startDate, endDate } = req.validatedQuery
    const userId = req.user!.id

    // Verify account ownership
    await verifyAccountOwnership(accountId, userId)

    const { start, end } = getDateRange(timeframe, startDate, endDate)

    try {
      // Get trades within the specified period
      const { data: trades, error: tradesError } = await supabase
        .from('trades')
        .select('profit, close_time')
        .eq('account_id', accountId)
        .eq('status', 'closed')
        .gte('close_time', start)
        .lte('close_time', end)
        .order('close_time', { ascending: true })

      if (tradesError) {
        throw createInternalError('Failed to fetch trade data')
      }

      // Group trades by date
      const dailyData = new Map<string, { profit: number, trades: number, wins: number }>()

      for (const trade of trades) {
        const date = new Date(trade.close_time).toISOString().split('T')[0]
        const profit = trade.profit || 0
        
        if (!dailyData.has(date)) {
          dailyData.set(date, { profit: 0, trades: 0, wins: 0 })
        }

        const dayData = dailyData.get(date)!
        dayData.profit += profit
        dayData.trades += 1
        if (profit > 0) dayData.wins += 1
      }

      // Convert to array format
      const dailyPnLData = Array.from(dailyData.entries()).map(([date, data]) => ({
        date,
        profit: Math.round(data.profit * 100) / 100,
        trades: data.trades,
        winRate: data.trades > 0 ? Math.round((data.wins / data.trades) * 10000) / 100 : 0
      }))

      // Calculate statistics
      const profits = dailyPnLData.map(d => d.profit)
      const averageDailyReturn = profits.length > 0 ? 
        Math.round((profits.reduce((sum, p) => sum + p, 0) / profits.length) * 100) / 100 : 0

      const bestDay = profits.length > 0 ? 
        dailyPnLData.reduce((best, current) => current.profit > best.profit ? current : best) :
        { date: '', profit: 0 }

      const worstDay = profits.length > 0 ? 
        dailyPnLData.reduce((worst, current) => current.profit < worst.profit ? current : worst) :
        { date: '', profit: 0 }

      const responseData = {
        data: dailyPnLData,
        averageDailyReturn,
        bestDay: {
          date: bestDay.date,
          profit: bestDay.profit
        },
        worstDay: {
          date: worstDay.date,
          profit: worstDay.profit
        }
      }

      res.json({
        success: true,
        data: responseData
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/analytics/trading-hours/:accountId
router.get('/trading-hours/:accountId',
  validateRequest({ 
    params: accountIdSchema,
    query: analyticsQuerySchema 
  }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { accountId } = req.validatedParams
    const { timeframe, startDate, endDate } = req.validatedQuery
    const userId = req.user!.id

    // Verify account ownership
    await verifyAccountOwnership(accountId, userId)

    const { start, end } = getDateRange(timeframe, startDate, endDate)

    try {
      // Get trades within the specified period
      const { data: trades, error: tradesError } = await supabase
        .from('trades')
        .select('profit, open_time, close_time')
        .eq('account_id', accountId)
        .eq('status', 'closed')
        .gte('close_time', start)
        .lte('close_time', end)

      if (tradesError) {
        throw createInternalError('Failed to fetch trade data')
      }

      // Group trades by hour (using close time)
      const hourlyData = new Map<number, { profit: number, trades: number, wins: number }>()

      // Initialize all hours
      for (let hour = 0; hour < 24; hour++) {
        hourlyData.set(hour, { profit: 0, trades: 0, wins: 0 })
      }

      for (const trade of trades) {
        const hour = new Date(trade.close_time).getUTCHours()
        const profit = trade.profit || 0
        
        const hourData = hourlyData.get(hour)!
        hourData.profit += profit
        hourData.trades += 1
        if (profit > 0) hourData.wins += 1
      }

      // Convert to array format
      const hourlyStats = Array.from(hourlyData.entries()).map(([hour, data]) => ({
        hour: `${hour.toString().padStart(2, '0')}:00`,
        trades: data.trades,
        profit: Math.round(data.profit * 100) / 100,
        winRate: data.trades > 0 ? Math.round((data.wins / data.trades) * 10000) / 100 : 0
      }))

      // Find best and worst trading hours
      const hoursWithTrades = hourlyStats.filter(h => h.trades > 0)
      
      const bestHours = hoursWithTrades
        .sort((a, b) => b.profit - a.profit)
        .slice(0, 3)
        .map(h => h.hour)

      const worstHours = hoursWithTrades
        .sort((a, b) => a.profit - b.profit)
        .slice(0, 3)
        .map(h => h.hour)

      const responseData = {
        hourlyStats,
        bestTradingHours: bestHours,
        worstTradingHours: worstHours
      }

      res.json({
        success: true,
        data: responseData
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/analytics/symbol-breakdown/:accountId
router.get('/symbol-breakdown/:accountId',
  validateRequest({ 
    params: accountIdSchema,
    query: analyticsQuerySchema 
  }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { accountId } = req.validatedParams
    const { timeframe, startDate, endDate } = req.validatedQuery
    const userId = req.user!.id

    // Verify account ownership
    await verifyAccountOwnership(accountId, userId)

    const { start, end } = getDateRange(timeframe, startDate, endDate)

    try {
      // Get trades within the specified period
      const { data: trades, error: tradesError } = await supabase
        .from('trades')
        .select('symbol, profit, volume, open_time, close_time')
        .eq('account_id', accountId)
        .eq('status', 'closed')
        .gte('close_time', start)
        .lte('close_time', end)

      if (tradesError) {
        throw createInternalError('Failed to fetch trade data')
      }

      // Group trades by symbol
      const symbolData = new Map<string, { 
        profit: number, 
        trades: number, 
        wins: number, 
        volume: number,
        totalHoldTime: number 
      }>()

      for (const trade of trades) {
        const symbol = trade.symbol
        const profit = trade.profit || 0
        const volume = trade.volume || 0
        
        // Calculate hold time in minutes
        const openTime = new Date(trade.open_time).getTime()
        const closeTime = new Date(trade.close_time).getTime()
        const holdTime = (closeTime - openTime) / (1000 * 60) // minutes
        
        if (!symbolData.has(symbol)) {
          symbolData.set(symbol, { 
            profit: 0, 
            trades: 0, 
            wins: 0, 
            volume: 0,
            totalHoldTime: 0 
          })
        }

        const data = symbolData.get(symbol)!
        data.profit += profit
        data.trades += 1
        data.volume += volume
        data.totalHoldTime += holdTime
        if (profit > 0) data.wins += 1
      }

      // Convert to array format
      const symbolBreakdown = Array.from(symbolData.entries()).map(([symbol, data]) => ({
        symbol,
        trades: data.trades,
        profit: Math.round(data.profit * 100) / 100,
        winRate: data.trades > 0 ? Math.round((data.wins / data.trades) * 10000) / 100 : 0,
        avgHoldTime: data.trades > 0 ? Math.round(data.totalHoldTime / data.trades) : 0,
        volume: Math.round(data.volume * 100) / 100
      }))

      // Sort by profit and get top/worst performers
      const sortedByProfit = [...symbolBreakdown].sort((a, b) => b.profit - a.profit)
      
      const topPerformers = sortedByProfit
        .slice(0, 5)
        .map(s => s.symbol)

      const worstPerformers = sortedByProfit
        .slice(-5)
        .reverse()
        .map(s => s.symbol)

      const responseData = {
        symbols: symbolBreakdown,
        topPerformers,
        worstPerformers
      }

      res.json({
        success: true,
        data: responseData
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/analytics/drawdown-analysis/:accountId
router.get('/drawdown-analysis/:accountId',
  validateRequest({ 
    params: accountIdSchema,
    query: analyticsQuerySchema 
  }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { accountId } = req.validatedParams
    const { timeframe, startDate, endDate } = req.validatedQuery
    const userId = req.user!.id

    // Verify account ownership
    await verifyAccountOwnership(accountId, userId)

    const { start, end } = getDateRange(timeframe, startDate, endDate)

    try {
      // Get trades within the specified period
      const { data: trades, error: tradesError } = await supabase
        .from('trades')
        .select('profit, close_time')
        .eq('account_id', accountId)
        .eq('status', 'closed')
        .gte('close_time', start)
        .lte('close_time', end)
        .order('close_time', { ascending: true })

      if (tradesError) {
        throw createInternalError('Failed to fetch trade data')
      }

      // Calculate drawdown analysis
      let runningBalance = 0
      let peak = 0
      let maxDrawdown = 0
      let currentDrawdown = 0
      let drawdownPeriods = []
      let inDrawdown = false
      let drawdownStart = null

      const drawdownData = []

      for (const trade of trades) {
        runningBalance += trade.profit || 0
        
        if (runningBalance > peak) {
          // New peak reached
          if (inDrawdown) {
            // End of drawdown period
            drawdownPeriods.push({
              start: drawdownStart,
              end: trade.close_time,
              maxDrawdown: currentDrawdown,
              duration: new Date(trade.close_time).getTime() - new Date(drawdownStart).getTime()
            })
            inDrawdown = false
          }
          peak = runningBalance
          currentDrawdown = 0
        } else {
          // In drawdown
          currentDrawdown = peak - runningBalance
          if (currentDrawdown > maxDrawdown) {
            maxDrawdown = currentDrawdown
          }
          
          if (!inDrawdown) {
            inDrawdown = true
            drawdownStart = trade.close_time
          }
        }

        drawdownData.push({
          timestamp: trade.close_time,
          balance: Math.round(runningBalance * 100) / 100,
          drawdown: Math.round(currentDrawdown * 100) / 100,
          drawdownPercent: peak > 0 ? Math.round((currentDrawdown / peak) * 10000) / 100 : 0
        })
      }

      // Calculate statistics
      const avgDrawdown = drawdownPeriods.length > 0 ? 
        drawdownPeriods.reduce((sum, d) => sum + d.maxDrawdown, 0) / drawdownPeriods.length : 0

      const longestDrawdownPeriod = drawdownPeriods.length > 0 ? 
        Math.max(...drawdownPeriods.map(d => d.duration)) : 0

      const responseData = {
        data: drawdownData,
        maxDrawdown: Math.round(maxDrawdown * 100) / 100,
        averageDrawdown: Math.round(avgDrawdown * 100) / 100,
        drawdownPeriods: drawdownPeriods.length,
        longestDrawdownPeriod: Math.round(longestDrawdownPeriod / (1000 * 60 * 60 * 24)), // days
        currentDrawdown: Math.round(currentDrawdown * 100) / 100
      }

      res.json({
        success: true,
        data: responseData
      })

    } catch (error) {
      throw error
    }
  })
)

export default router 
// User Management Types
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  country: string
  dateOfBirth: Date
  kycStatus: KYCStatus
  accountStatus: AccountStatus
  createdAt: Date
  updatedAt: Date
}

export type KYCStatus = 'pending' | 'verified' | 'rejected' | 'expired'
export type AccountStatus = 'active' | 'suspended' | 'closed' | 'pending'

// Trading Account Types
export interface TradingAccount {
  id: string
  userId: string
  accountNumber: string
  challengeId: string
  balance: number
  equity: number
  freeMargin: number
  marginLevel: number
  drawdown: number
  maxDrawdown: number
  phase: TradingPhase
  status: TradingAccountStatus
  mt5Login?: string
  createdAt: Date
  updatedAt: Date
}

export type TradingPhase = 'evaluation' | 'verification' | 'funded' | 'failed'
export type TradingAccountStatus = 'active' | 'paused' | 'closed' | 'violation'

// Challenge Types
export interface Challenge {
  id: string
  name: string
  description: string
  price: number
  targetProfit: number
  maxDailyLoss: number
  maxTotalLoss: number
  tradingPeriod: number // days
  profitShare: number // percentage
  minTradingDays: number
  maxPositionSize: number
  allowedInstruments: string[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Trading Types
export interface Trade {
  id: string
  accountId: string
  symbol: string
  type: TradeType
  volume: number
  openPrice: number
  closePrice?: number
  stopLoss?: number
  takeProfit?: number
  profit?: number
  commission: number
  swap: number
  openTime: Date
  closeTime?: Date
  status: TradeStatus
  comment?: string
}

export type TradeType = 'buy' | 'sell' | 'buy_limit' | 'sell_limit' | 'buy_stop' | 'sell_stop'
export type TradeStatus = 'open' | 'closed' | 'pending' | 'cancelled'

// Market Data Types
export interface MarketData {
  symbol: string
  bid: number
  ask: number
  last: number
  volume: number
  high: number
  low: number
  change: number
  changePercent: number
  timestamp: Date
}

export interface Candle {
  symbol: string
  timeframe: Timeframe
  open: number
  high: number
  low: number
  close: number
  volume: number
  timestamp: Date
}

export type Timeframe = 'M1' | 'M5' | 'M15' | 'M30' | 'H1' | 'H4' | 'D1' | 'W1' | 'MN1'

// Payment Types
export interface Payment {
  id: string
  userId: string
  challengeId?: string
  amount: number
  currency: string
  status: PaymentStatus
  paymentMethod: PaymentMethod
  stripePaymentIntentId?: string
  description: string
  createdAt: Date
  updatedAt: Date
}

export type PaymentStatus = 'pending' | 'succeeded' | 'failed' | 'cancelled' | 'refunded'
export type PaymentMethod = 'card' | 'bank_transfer' | 'crypto' | 'paypal'

// Risk Management Types
export interface RiskRule {
  id: string
  name: string
  description: string
  type: RiskRuleType
  value: number
  enabled: boolean
  priority: number
  challengeId?: string
}

export type RiskRuleType = 
  | 'max_daily_loss'
  | 'max_total_loss'
  | 'max_position_size'
  | 'max_positions'
  | 'trading_hours'
  | 'forbidden_instruments'

// Notification Types
export interface Notification {
  id: string
  userId: string
  title: string
  message: string
  type: NotificationType
  isRead: boolean
  createdAt: Date
}

export type NotificationType = 'info' | 'warning' | 'error' | 'success' | 'trade' | 'payment'

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Dashboard Types
export interface DashboardMetrics {
  totalBalance: number
  totalEquity: number
  totalProfit: number
  totalLoss: number
  winRate: number
  profitFactor: number
  dailyProfit: number
  drawdown: number
  activePositions: number
  closedTrades: number
}

// Performance Types
export interface PerformanceMetrics {
  totalTrades: number
  winningTrades: number
  losingTrades: number
  winRate: number
  avgWin: number
  avgLoss: number
  profitFactor: number
  sharpeRatio: number
  maxDrawdown: number
  returnOnInvestment: number
  period: {
    start: Date
    end: Date
  }
}

// Chart Types
export interface ChartDataPoint {
  timestamp: Date
  value: number
  label?: string
}

export interface TradingSession {
  id: string
  accountId: string
  startTime: Date
  endTime?: Date
  startBalance: number
  endBalance?: number
  profit?: number
  tradesCount: number
  status: 'active' | 'completed'
} 
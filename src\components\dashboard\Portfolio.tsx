'use client'

import { useState } from 'react'
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>hart3,
  ArrowUpRight,
  ArrowDownRight,
  <PERSON>freshC<PERSON>,
  Filter
} from 'lucide-react'

interface PortfolioPosition {
  symbol: string
  name: string
  quantity: number
  avgPrice: number
  currentPrice: number
  marketValue: number
  pnl: number
  pnlPercent: number
  allocation: number
  sector: string
}

interface PerformanceMetric {
  label: string
  value: string
  change: string
  positive: boolean
}

const Portfolio = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('1M')
  const [viewMode, setViewMode] = useState<'positions' | 'sectors' | 'performance'>('positions')

  const portfolioSummary = {
    totalValue: 127420.50,
    dailyChange: 2847.30,
    dailyChangePercent: 2.29,
    availableCash: 15680.25,
    investedAmount: 111740.25
  }

  const positions: PortfolioPosition[] = [
    {
      symbol: 'EUR/USD',
      name: 'Euro/US Dollar',
      quantity: 5.2,
      avgPrice: 1.0823,
      currentPrice: 1.0862,
      marketValue: 5648.24,
      pnl: 202.80,
      pnlPercent: 3.74,
      allocation: 4.4,
      sector: 'Forex Major'
    },
    {
      symbol: 'GBP/USD',
      name: 'British Pound/US Dollar',
      quantity: 3.8,
      avgPrice: 1.2650,
      currentPrice: 1.2621,
      marketValue: 4795.98,
      pnl: -110.20,
      pnlPercent: -2.25,
      allocation: 3.8,
      sector: 'Forex Major'
    },
    {
      symbol: 'GOLD',
      name: 'Gold Spot',
      quantity: 12.5,
      avgPrice: 2015.30,
      currentPrice: 2031.45,
      marketValue: 25393.13,
      pnl: 201.88,
      pnlPercent: 0.80,
      allocation: 19.9,
      sector: 'Commodities'
    },
    {
      symbol: 'BTC/USD',
      name: 'Bitcoin',
      quantity: 0.85,
      avgPrice: 41200.00,
      currentPrice: 43247.00,
      marketValue: 36759.95,
      pnl: 1739.95,
      pnlPercent: 4.97,
      allocation: 28.9,
      sector: 'Cryptocurrency'
    },
    {
      symbol: 'SPY',
      name: 'SPDR S&P 500 ETF',
      quantity: 45,
      avgPrice: 462.80,
      currentPrice: 471.25,
      marketValue: 21206.25,
      pnl: 380.25,
      pnlPercent: 1.83,
      allocation: 16.6,
      sector: 'Equity Index'
    }
  ]

  const performanceMetrics: PerformanceMetric[] = [
    {
      label: 'Total Return',
      value: '+23.4%',
      change: '+2.1%',
      positive: true
    },
    {
      label: 'Annual Return',
      value: '+18.7%',
      change: '+1.5%',
      positive: true
    },
    {
      label: 'Sharpe Ratio',
      value: '1.84',
      change: '+0.12',
      positive: true
    },
    {
      label: 'Max Drawdown',
      value: '-5.2%',
      change: '-0.8%',
      positive: false
    },
    {
      label: 'Win Rate',
      value: '72.5%',
      change: '+3.2%',
      positive: true
    },
    {
      label: 'Volatility',
      value: '12.8%',
      change: '-1.1%',
      positive: true
    }
  ]

  const sectorAllocation = [
    { sector: 'Cryptocurrency', value: 28.9, color: '#f59e0b' },
    { sector: 'Commodities', value: 19.9, color: '#10b981' },
    { sector: 'Equity Index', value: 16.6, color: '#3b82f6' },
    { sector: 'Forex Major', value: 8.2, color: '#8b5cf6' },
    { sector: 'Cash', value: 12.3, color: '#6b7280' },
    { sector: 'Other', value: 14.1, color: '#ef4444' }
  ]

  return (
    <div className="space-y-6">
      {/* Portfolio Summary */}
      <div className="glass-card p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-primary-content">Portfolio Overview</h2>
            <p className="text-secondary-content">Total portfolio value and performance</p>
          </div>
          <div className="flex items-center space-x-2">
            <button className="glass-card p-2 hover:bg-slate-700 transition-colors">
              <RefreshCw className="h-5 w-5 text-secondary-content" />
            </button>
            <button className="glass-card p-2 hover:bg-slate-700 transition-colors">
              <Filter className="h-5 w-5 text-secondary-content" />
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="md:col-span-2">
            <div className="text-4xl font-bold text-primary-content mb-2">
              ${portfolioSummary.totalValue.toLocaleString()}
            </div>
            <div className="flex items-center space-x-2">
              <div className={`flex items-center space-x-1 ${
                portfolioSummary.dailyChangePercent >= 0 ? 'text-green-400' : 'text-red-400'
              }`}>
                {portfolioSummary.dailyChangePercent >= 0 ? (
                  <ArrowUpRight className="h-4 w-4" />
                ) : (
                  <ArrowDownRight className="h-4 w-4" />
                )}
                <span className="font-bold">
                  ${Math.abs(portfolioSummary.dailyChange).toLocaleString()} 
                  ({portfolioSummary.dailyChangePercent >= 0 ? '+' : ''}{portfolioSummary.dailyChangePercent}%)
                </span>
              </div>
              <span className="text-secondary-content text-sm">today</span>
            </div>
          </div>
          
          <div className="bg-slate-800/60 rounded-lg p-4">
            <div className="text-sm text-secondary-content mb-1">Available Cash</div>
            <div className="text-xl font-bold text-blue-400">
              ${portfolioSummary.availableCash.toLocaleString()}
            </div>
          </div>
          
          <div className="bg-slate-800/60 rounded-lg p-4">
            <div className="text-sm text-secondary-content mb-1">Invested</div>
            <div className="text-xl font-bold text-green-400">
              ${portfolioSummary.investedAmount.toLocaleString()}
            </div>
          </div>
        </div>
      </div>

      {/* View Mode Toggle */}
      <div className="flex space-x-1 bg-slate-800 rounded-lg p-1 w-fit">
        {[
          { id: 'positions' as const, label: 'Positions' },
          { id: 'sectors' as const, label: 'Sectors' },
          { id: 'performance' as const, label: 'Performance' }
        ].map((mode) => (
          <button
            key={mode.id}
            onClick={() => setViewMode(mode.id)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              viewMode === mode.id 
                ? 'bg-green-500 text-white' 
                : 'text-secondary-content hover:text-primary-content'
            }`}
          >
            {mode.label}
          </button>
        ))}
      </div>

      {/* Content based on view mode */}
      {viewMode === 'positions' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Positions List */}
          <div className="lg:col-span-2 glass-card p-6">
            <h3 className="text-xl font-bold text-primary-content mb-6">Holdings</h3>
            <div className="space-y-3">
              {positions.map((position, index) => (
                <div key={index} className="bg-slate-800/60 rounded-lg p-4 hover:bg-slate-700/60 transition-colors">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <div className="font-bold text-primary-content">{position.symbol}</div>
                      <div className="text-sm text-secondary-content">{position.name}</div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-primary-content">
                        ${position.marketValue.toLocaleString()}
                      </div>
                      <div className="text-sm text-secondary-content">
                        {position.allocation}% of portfolio
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-secondary-content">Quantity: </span>
                      <span className="text-primary-content font-mono">{position.quantity}</span>
                    </div>
                    <div>
                      <span className="text-secondary-content">Avg Price: </span>
                      <span className="text-primary-content font-mono">${position.avgPrice}</span>
                    </div>
                    <div>
                      <span className="text-secondary-content">Current: </span>
                      <span className="text-primary-content font-mono">${position.currentPrice}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mt-3 pt-3 border-t border-slate-700">
                    <div className="text-sm text-secondary-content">{position.sector}</div>
                    <div className={`font-bold ${position.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {position.pnl >= 0 ? '+' : ''}${position.pnl.toFixed(2)} ({position.pnlPercent >= 0 ? '+' : ''}{position.pnlPercent.toFixed(2)}%)
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Portfolio Allocation */}
          <div className="glass-card p-6">
            <h3 className="text-xl font-bold text-primary-content mb-6">Asset Allocation</h3>
            <div className="space-y-4">
              {sectorAllocation.map((sector, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-secondary-content">{sector.sector}</span>
                    <span className="text-sm font-bold text-primary-content">{sector.value}%</span>
                  </div>
                  <div className="w-full bg-slate-800 rounded-full h-2">
                    <div 
                      className="h-2 rounded-full transition-all duration-500"
                      style={{ 
                        width: `${sector.value}%`,
                        backgroundColor: sector.color
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {viewMode === 'sectors' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Sector Breakdown */}
          <div className="glass-card p-6">
            <h3 className="text-xl font-bold text-primary-content mb-6">Sector Analysis</h3>
            <div className="space-y-4">
              {sectorAllocation.map((sector, index) => (
                <div key={index} className="bg-slate-800/60 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div 
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: sector.color }}
                      />
                      <span className="font-semibold text-primary-content">{sector.sector}</span>
                    </div>
                    <span className="font-bold text-primary-content">{sector.value}%</span>
                  </div>
                  <div className="text-sm text-secondary-content">
                    ${(portfolioSummary.totalValue * sector.value / 100).toLocaleString()} allocation
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Sector Performance Chart Placeholder */}
          <div className="glass-card p-6">
            <h3 className="text-xl font-bold text-primary-content mb-6">Sector Performance</h3>
            <div className="h-64 bg-slate-900/60 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <PieChart className="h-16 w-16 text-secondary-content mx-auto mb-4" />
                <p className="text-secondary-content">Interactive Pie Chart</p>
                <p className="text-xs text-secondary-content/70">Visualization coming soon</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {viewMode === 'performance' && (
        <div className="space-y-6">
          {/* Performance Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {performanceMetrics.map((metric, index) => (
              <div key={index} className="glass-card p-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-semibold text-secondary-content">{metric.label}</h4>
                  <div className={`text-xs px-2 py-1 rounded ${
                    metric.positive ? 'text-green-400 bg-green-400/10' : 'text-red-400 bg-red-400/10'
                  }`}>
                    {metric.change}
                  </div>
                </div>
                <div className={`text-2xl font-bold ${
                  metric.positive ? 'text-green-400' : 'text-red-400'
                }`}>
                  {metric.value}
                </div>
              </div>
            ))}
          </div>

          {/* Performance Chart */}
          <div className="glass-card p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-primary-content">Performance Chart</h3>
              <div className="flex items-center space-x-2">
                {['1W', '1M', '3M', '6M', '1Y', 'ALL'].map((period) => (
                  <button
                    key={period}
                    onClick={() => setSelectedPeriod(period)}
                    className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                      selectedPeriod === period 
                        ? 'bg-green-500 text-white' 
                        : 'text-secondary-content hover:text-primary-content'
                    }`}
                  >
                    {period}
                  </button>
                ))}
              </div>
            </div>
            
            <div className="h-80 bg-slate-900/60 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <BarChart3 className="h-20 w-20 text-secondary-content mx-auto mb-4" />
                <p className="text-secondary-content">Advanced Performance Chart</p>
                <p className="text-xs text-secondary-content/70">Historical data visualization coming soon</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Portfolio 
import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { ArrowLeft, ArrowRight, Shield, BarChart3, Globe, Users, Play, DollarSign } from 'lucide-react'
import { Logo } from '@/components/ui/Logo'
import { EliteText } from '@/components/ui/EliteText'

export const metadata: Metadata = {
  title: 'Learn More - Prop Bully Elite Trading Platform',
  description: 'Discover how Prop Bully works, our evaluation process, funding opportunities, and why thousands of traders choose our platform.',
}

export default function LearnMorePage() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Professional Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black">
        <div className="absolute inset-0 bg-gradient-to-r from-green-500/3 via-transparent to-blue-500/3" />
      </div>

      {/* Navigation */}
      <nav className="relative z-10 flex items-center justify-between p-6 border-b border-slate-700/50">
        <div className="flex items-center space-x-4">
          <Logo size="md" imageSrc="/images/logos/prop-bully-logo.png" alt="Prop Bully Logo" />
          <EliteText size="lg" variant="gold">Prop Bully</EliteText>
        </div>
        <div className="flex items-center space-x-4">
          <Link href="/" className="flex items-center space-x-2 text-white/60 hover:text-white transition-colors">
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Home</span>
          </Link>
          <Link href="/auth/register">
            <button className="professional-button-dark">Get Started</button>
          </Link>
        </div>
      </nav>

      <div className="relative z-10 max-w-6xl mx-auto px-6 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            How <span className="text-gradient-gold">Prop Bully</span> Works
          </h1>
          <p className="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
            Learn about our comprehensive evaluation process, funding opportunities, and why we're the preferred choice for professional traders worldwide.
          </p>
        </div>

        {/* The Process */}
        <section className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Our 3-Step Process</h2>
            <p className="text-white/70">Simple, transparent, and designed for success</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Step 1 */}
            <div className="glass-card p-8 text-center group hover:bg-slate-700/30 transition-colors">
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <Play className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4">1. Start Challenge</h3>
              <p className="text-white/70 mb-6">
                Choose from our challenge packages ($10K to $200K) and begin your evaluation with live market conditions.
              </p>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-white/60">Challenge Duration:</span>
                  <span className="text-green-400">30 Days</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/60">Profit Target:</span>
                  <span className="text-green-400">8%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/60">Max Drawdown:</span>
                  <span className="text-red-400">10%</span>
                </div>
              </div>
            </div>

            {/* Step 2 */}
            <div className="glass-card p-8 text-center group hover:bg-slate-700/30 transition-colors">
              <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <BarChart3 className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4">2. Verification</h3>
              <p className="text-white/70 mb-6">
                Pass our verification phase to demonstrate consistency and risk management skills.
              </p>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-white/60">Verification Duration:</span>
                  <span className="text-green-400">30 Days</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/60">Profit Target:</span>
                  <span className="text-green-400">5%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/60">Max Drawdown:</span>
                  <span className="text-red-400">10%</span>
                </div>
              </div>
            </div>

            {/* Step 3 */}
            <div className="glass-card p-8 text-center group hover:bg-slate-700/30 transition-colors">
              <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <DollarSign className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4">3. Get Funded</h3>
              <p className="text-white/70 mb-6">
                Receive your funded account and start earning with our capital while keeping up to 90% of profits.
              </p>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-white/60">Profit Split:</span>
                  <span className="text-green-400">Up to 90%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/60">Max Funding:</span>
                  <span className="text-green-400">$2M</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/60">Payouts:</span>
                  <span className="text-green-400">Bi-weekly</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Why Choose Us */}
        <section className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Why Choose Prop Bully?</h2>
            <p className="text-white/70">We're not just another prop firm - we're your trading partner</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Feature 1 */}
            <div className="glass-card p-6 text-center">
              <Shield className="h-12 w-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Instant Funding</h3>
              <p className="text-white/60 text-sm">
                Get funded immediately after passing evaluation. No waiting periods.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="glass-card p-6 text-center">
              <Users className="h-12 w-12 text-blue-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">24/7 Support</h3>
              <p className="text-white/60 text-sm">
                Round-the-clock support from our expert team whenever you need help.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="glass-card p-6 text-center">
              <BarChart3 className="h-12 w-12 text-purple-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Advanced Tools</h3>
              <p className="text-white/60 text-sm">
                Professional trading tools, analytics, and risk management systems.
              </p>
            </div>

            {/* Feature 4 */}
            <div className="glass-card p-6 text-center">
              <Globe className="h-12 w-12 text-orange-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Global Markets</h3>
              <p className="text-white/60 text-sm">
                Trade Forex, Indices, Commodities, and Crypto with institutional liquidity.
              </p>
            </div>
          </div>
        </section>

        {/* Success Stories */}
        <section className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Success Stories</h2>
            <p className="text-white/70">Real traders, real results</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <div className="glass-card p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">M</span>
                </div>
                <div>
                  <h4 className="text-white font-semibold">Michael Chen</h4>
                  <p className="text-white/60 text-sm">Forex Trader</p>
                </div>
              </div>
              <p className="text-white/70 text-sm mb-4">
                "Prop Bully gave me the opportunity to trade with serious capital. The evaluation was fair, and the support team is incredible. I'm now managing a $500K account!"
              </p>
              <div className="flex items-center space-x-4 text-xs">
                <span className="text-green-400">$47K Profit</span>
                <span className="text-white/50">Last Month</span>
              </div>
            </div>

            {/* Testimonial 2 */}
            <div className="glass-card p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">S</span>
                </div>
                <div>
                  <h4 className="text-white font-semibold">Sarah Williams</h4>
                  <p className="text-white/60 text-sm">Day Trader</p>
                </div>
              </div>
              <p className="text-white/70 text-sm mb-4">
                "The platform is incredibly user-friendly and the risk management tools are top-notch. I passed my challenge in 3 weeks and haven't looked back since."
              </p>
              <div className="flex items-center space-x-4 text-xs">
                <span className="text-green-400">$23K Profit</span>
                <span className="text-white/50">Last Month</span>
              </div>
            </div>

            {/* Testimonial 3 */}
            <div className="glass-card p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">D</span>
                </div>
                <div>
                  <h4 className="text-white font-semibold">David Rodriguez</h4>
                  <p className="text-white/60 text-sm">Swing Trader</p>
                </div>
              </div>
              <p className="text-white/70 text-sm mb-4">
                "Best prop firm I've worked with. Fair rules, fast payouts, and excellent technology. The bi-weekly payouts keep me motivated and focused."
              </p>
              <div className="flex items-center space-x-4 text-xs">
                <span className="text-green-400">$31K Profit</span>
                <span className="text-white/50">Last Month</span>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="text-center">
          <div className="glass-card p-12">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Start Your Journey?</h2>
            <p className="text-xl text-white/70 mb-8 max-w-2xl mx-auto">
              Join thousands of successful traders who have chosen Prop Bully as their path to trading with institutional capital.
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-8">
              <Link href="/auth/register">
                <button className="professional-button-dark flex items-center space-x-2 px-8 py-4 text-lg">
                  <span>Start Your Challenge</span>
                  <ArrowRight className="h-5 w-5" />
                </button>
              </Link>
              <Link href="/pricing">
                <button className="professional-button px-8 py-4 text-lg">
                  View Pricing
                </button>
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div>
                <div className="text-2xl font-bold text-green-400 mb-1">No Risk</div>
                <div className="text-white/60 text-sm">30-day money-back guarantee</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-400 mb-1">Fast Setup</div>
                <div className="text-white/60 text-sm">Start trading within 24 hours</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-400 mb-1">Full Support</div>
                <div className="text-white/60 text-sm">24/7 help when you need it</div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
} 
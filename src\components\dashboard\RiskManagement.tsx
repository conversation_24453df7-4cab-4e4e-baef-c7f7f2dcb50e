'use client'

import { useState } from 'react'
import { 
  Shield, 
  AlertTriangle, 
  TrendingDown,
  Target,
  Activity,
  Settings,
  Bell,
  Clock,
  BarChart3,
  RefreshCw,
  CheckCircle,
  XCircle,
  Info,
  Pause,
  StopCircle,
  Edit,
  X
} from 'lucide-react'
import type { RiskRule, TradingAccount } from '@/types'
import { useRiskMetrics, useRiskRules, useRiskAlerts, useApiData } from '@/hooks/useApiData'
import { apiClient } from '@/lib/api/client'

interface RiskManagementProps {
  account?: TradingAccount
}

interface RiskAlert {
  id: string
  type: 'warning' | 'danger' | 'info'
  title: string
  message: string
  timestamp: Date
  acknowledged: boolean
}

const RiskManagement = ({ account: _account }: RiskManagementProps) => {
  const [activeTab, setActiveTab] = useState('monitor')
  const [editingRule, setEditingRule] = useState<string | null>(null)

  // Demo account ID - in real app this would come from user's account
  const accountId = 'demo-account-123'
  
  // Fetch real risk data from backend
  const { 
    data: riskMetricsData, 
    loading: metricsLoading, 
    error: metricsError,
    refetch: refetchMetrics 
  } = useRiskMetrics(accountId)
  
  const { 
    data: riskRulesData, 
    loading: rulesLoading, 
    error: rulesError,
    refetch: refetchRules 
  } = useRiskRules(accountId)
  
  const { 
    data: riskAlertsData, 
    loading: alertsLoading, 
    error: alertsError,
    refetch: refetchAlerts 
  } = useRiskAlerts(accountId)

  const isLoading = metricsLoading || rulesLoading || alertsLoading
  const hasError = metricsError || rulesError || alertsError

  // Fallback data for when API returns empty results
  const fallbackRiskMetrics = {
    dailyDrawdown: 2.8,
    maxDrawdown: 5.2,
    totalDrawdown: 8.5,
    positionSize: 0.05,
    openPositions: 3,
    marginLevel: 487.5,
    freeMargin: 9750.00,
    equity: 10420.00,
    balance: 10000.00,
    riskScore: 3.2, // out of 10
    lastUpdate: new Date()
  }

  // Use API data if available, otherwise fallback to demo data
  const currentRiskMetrics = riskMetricsData || fallbackRiskMetrics

  // Fallback risk rules
  const fallbackRiskRules: RiskRule[] = [
    {
      id: '1',
      name: 'Daily Loss Limit',
      description: 'Maximum allowed daily loss percentage',
      type: 'max_daily_loss',
      value: 5.0,
      enabled: true,
      priority: 1
    },
    {
      id: '2',
      name: 'Total Drawdown Limit',
      description: 'Maximum allowed total drawdown percentage',
      type: 'max_total_loss',
      value: 10.0,
      enabled: true,
      priority: 1
    },
    {
      id: '3',
      name: 'Maximum Position Size',
      description: 'Maximum position size as percentage of account',
      type: 'max_position_size',
      value: 2.0,
      enabled: true,
      priority: 2
    },
    {
      id: '4',
      name: 'Maximum Open Positions',
      description: 'Maximum number of concurrent open positions',
      type: 'max_positions',
      value: 5,
      enabled: true,
      priority: 2
    }
  ]

  // Use API data if available, otherwise fallback to demo data
  const [riskRules, setRiskRules] = useState<RiskRule[]>(riskRulesData?.rules || fallbackRiskRules)

  // Fallback risk alerts
  const fallbackRiskAlerts: RiskAlert[] = [
    {
      id: '1',
      type: 'warning',
      title: 'Daily Drawdown Alert',
      message: 'Daily drawdown has reached 2.8%, approaching 5% limit',
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      acknowledged: false
    },
    {
      id: '2',
      type: 'info',
      title: 'Position Size Check',
      message: 'Current position size is within safe limits',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      acknowledged: true
    }
  ]

  // Use API data if available, otherwise fallback to demo data
  const [riskAlerts, setRiskAlerts] = useState<RiskAlert[]>(riskAlertsData?.alerts || fallbackRiskAlerts)

  const handleAcknowledgeAlert = (alertId: string) => {
    setRiskAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ))
  }

  const handleToggleRule = (ruleId: string) => {
    setRiskRules(prev => prev.map(rule => 
      rule.id === ruleId ? { ...rule, enabled: !rule.enabled } : rule
    ))
  }

  const handleUpdateRule = (ruleId: string, newValue: number) => {
    setRiskRules(prev => prev.map(rule => 
      rule.id === ruleId ? { ...rule, value: newValue } : rule
    ))
    setEditingRule(null)
  }

  const getRiskLevel = (current: number, limit: number) => {
    const percentage = (current / limit) * 100
    if (percentage >= 90) return { level: 'high', color: 'text-red-400' }
    if (percentage >= 70) return { level: 'medium', color: 'text-yellow-400' }
    return { level: 'low', color: 'text-green-400' }
  }

  const RiskMeter = ({ value, max, label, unit = '%' }: { 
    value: number, 
    max: number, 
    label: string,
    unit?: string 
  }) => {
    const percentage = Math.min((value / max) * 100, 100)
    const risk = getRiskLevel(value, max)
    
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm text-secondary-content">{label}</span>
          <span className={`text-sm font-medium ${risk.color}`}>
            {value.toFixed(1)}{unit} / {max}{unit}
          </span>
        </div>
        <div className="w-full bg-slate-700 rounded-full h-2 overflow-hidden">
          <div 
            className={`h-full transition-all duration-300 ${
              risk.level === 'high' ? 'bg-red-500' :
              risk.level === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
            }`}
            style={{ width: `${percentage}%` }}
          />
        </div>
      </div>
    )
  }

  // Handle refresh for all data
  const handleRefresh = async () => {
    await Promise.all([
      refetchMetrics(),
      refetchRules(),
      refetchAlerts()
    ])
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="glass-card p-8 text-center">
          <RefreshCw className="h-8 w-8 text-green-500 animate-spin mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-primary-content mb-2">Loading Risk Data</h3>
          <p className="text-secondary-content">Fetching real-time risk monitoring data...</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (hasError) {
    return (
      <div className="space-y-6">
        <div className="glass-card p-8 text-center">
          <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-primary-content mb-2">Error Loading Risk Data</h3>
          <p className="text-secondary-content mb-4">
            {metricsError || rulesError || alertsError || 'Failed to load risk management data'}
          </p>
          <button
            onClick={handleRefresh}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-primary-content">Risk Management</h1>
          <p className="text-secondary-content mt-1">
            Real-time risk monitoring and management controls
            {riskMetricsData ? ' (Live Data)' : ' (Demo Data)'}
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Tab Selector */}
          <div className="flex items-center space-x-1 bg-slate-800 rounded-lg p-1">
            {[
              { id: 'monitor', label: 'Monitor', icon: Activity },
              { id: 'rules', label: 'Rules', icon: Settings },
              { id: 'alerts', label: 'Alerts', icon: Bell }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-3 py-2 rounded text-xs font-medium transition-colors flex items-center space-x-2 ${
                  activeTab === tab.id 
                    ? 'bg-green-500 text-white' 
                    : 'text-secondary-content hover:text-primary-content'
                }`}
              >
                <tab.icon className="h-3 w-3" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>

          {/* Refresh Button */}
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="p-2 rounded-lg hover:bg-slate-700 transition-colors disabled:opacity-50"
            title="Refresh risk data"
          >
            <RefreshCw className={`h-4 w-4 text-secondary-content ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* Risk Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-red-500/20 rounded-lg">
              <TrendingDown className="h-5 w-5 text-red-400" />
            </div>
            <span className="text-xs text-secondary-content">Daily Drawdown</span>
          </div>
          <div className="space-y-2">
            <div className="text-2xl font-bold text-primary-content">
              {currentRiskMetrics.dailyDrawdown.toFixed(1)}%
            </div>
            <RiskMeter 
              value={currentRiskMetrics.dailyDrawdown} 
              max={5.0} 
              label="Limit: 5.0%" 
            />
          </div>
        </div>

        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-orange-500/20 rounded-lg">
              <Target className="h-5 w-5 text-orange-400" />
            </div>
            <span className="text-xs text-secondary-content">Total Drawdown</span>
          </div>
          <div className="space-y-2">
            <div className="text-2xl font-bold text-primary-content">
              {currentRiskMetrics.totalDrawdown.toFixed(1)}%
            </div>
            <RiskMeter 
              value={currentRiskMetrics.totalDrawdown} 
              max={10.0} 
              label="Limit: 10.0%" 
            />
          </div>
        </div>

        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-blue-500/20 rounded-lg">
              <BarChart3 className="h-5 w-5 text-blue-400" />
            </div>
            <span className="text-xs text-secondary-content">Position Size</span>
          </div>
          <div className="space-y-2">
            <div className="text-2xl font-bold text-primary-content">
              {currentRiskMetrics.positionSize.toFixed(2)}%
            </div>
            <RiskMeter 
              value={currentRiskMetrics.positionSize} 
              max={2.0} 
              label="Limit: 2.0%" 
            />
          </div>
        </div>

        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-purple-500/20 rounded-lg">
              <Shield className="h-5 w-5 text-purple-400" />
            </div>
            <span className="text-xs text-secondary-content">Risk Score</span>
          </div>
          <div className="space-y-2">
            <div className="text-2xl font-bold text-primary-content">
              {currentRiskMetrics.riskScore.toFixed(1)}/10
            </div>
            <RiskMeter 
              value={currentRiskMetrics.riskScore} 
              max={10} 
              label="Risk Level" 
              unit=""
            />
          </div>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'monitor' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Account Status */}
          <div className="glass-card p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-primary-content">Account Status</h3>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-xs text-green-400">Live Monitoring</span>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg">
                <span className="text-sm text-secondary-content">Balance</span>
                <span className="text-sm font-medium text-primary-content">
                  ${currentRiskMetrics.balance.toLocaleString()}
                </span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg">
                <span className="text-sm text-secondary-content">Equity</span>
                <span className="text-sm font-medium text-primary-content">
                  ${currentRiskMetrics.equity.toLocaleString()}
                </span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg">
                <span className="text-sm text-secondary-content">Free Margin</span>
                <span className="text-sm font-medium text-primary-content">
                  ${currentRiskMetrics.freeMargin.toLocaleString()}
                </span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg">
                <span className="text-sm text-secondary-content">Margin Level</span>
                <span className="text-sm font-medium text-green-400">
                  {currentRiskMetrics.marginLevel.toFixed(1)}%
                </span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg">
                <span className="text-sm text-secondary-content">Open Positions</span>
                <span className="text-sm font-medium text-primary-content">
                  {currentRiskMetrics.openPositions}
                </span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="glass-card p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-primary-content">Emergency Controls</h3>
              <AlertTriangle className="h-5 w-5 text-yellow-400" />
            </div>
            
            <div className="space-y-4">
              <button className="w-full p-4 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400 hover:bg-red-500/30 transition-colors flex items-center justify-center space-x-2">
                <StopCircle className="h-5 w-5" />
                <span>Close All Positions</span>
              </button>
              
              <button className="w-full p-4 bg-yellow-500/20 border border-yellow-500/30 rounded-lg text-yellow-400 hover:bg-yellow-500/30 transition-colors flex items-center justify-center space-x-2">
                <Pause className="h-5 w-5" />
                <span>Pause Trading</span>
              </button>
              
              <button className="w-full p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg text-blue-400 hover:bg-blue-500/30 transition-colors flex items-center justify-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>Enable Safe Mode</span>
              </button>
            </div>
            
            <div className="mt-6 p-4 bg-slate-800/50 rounded-lg">
              <p className="text-xs text-secondary-content mb-2">Last Risk Check:</p>
              <p className="text-xs text-primary-content">
                {currentRiskMetrics.lastUpdate.toLocaleTimeString()}
              </p>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'rules' && (
        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-primary-content">Risk Rules Configuration</h3>
            <span className="text-xs text-secondary-content">
              {riskRules.filter(rule => rule.enabled).length}/{riskRules.length} rules active
            </span>
          </div>
          
          <div className="space-y-4">
            {riskRules.map((rule) => (
              <div key={rule.id} className="p-4 bg-slate-800/30 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => handleToggleRule(rule.id)}
                      className={`w-10 h-6 rounded-full transition-colors relative ${
                        rule.enabled ? 'bg-green-500' : 'bg-slate-600'
                      }`}
                    >
                      <div className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${
                        rule.enabled ? 'translate-x-5' : 'translate-x-1'
                      }`} />
                    </button>
                    <div>
                      <h4 className="text-sm font-medium text-primary-content">{rule.name}</h4>
                      <p className="text-xs text-secondary-content">{rule.description}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {editingRule === rule.id ? (
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          defaultValue={rule.value}
                          step="0.1"
                          className="w-20 px-2 py-1 bg-slate-700 border border-slate-600 rounded text-sm text-primary-content"
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleUpdateRule(rule.id, parseFloat((e.target as HTMLInputElement).value))
                            }
                          }}
                        />
                        <button
                          onClick={() => setEditingRule(null)}
                          className="p-1 hover:bg-slate-700 rounded"
                        >
                          <X className="h-4 w-4 text-secondary-content" />
                        </button>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-primary-content">
                          {rule.value}{rule.type.includes('loss') || rule.type.includes('size') ? '%' : ''}
                        </span>
                        <button
                          onClick={() => setEditingRule(rule.id)}
                          className="p-1 hover:bg-slate-700 rounded"
                        >
                          <Edit className="h-4 w-4 text-secondary-content" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-4 text-xs">
                  <span className={`px-2 py-1 rounded ${
                    rule.priority === 1 ? 'bg-red-500/20 text-red-400' : 'bg-blue-500/20 text-blue-400'
                  }`}>
                    Priority {rule.priority}
                  </span>
                  <span className={rule.enabled ? 'text-green-400' : 'text-slate-500'}>
                    {rule.enabled ? 'Active' : 'Disabled'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'alerts' && (
        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-primary-content">Risk Alerts</h3>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-secondary-content">
                {riskAlerts.filter(alert => !alert.acknowledged).length} unread
              </span>
              <Bell className="h-4 w-4 text-secondary-content" />
            </div>
          </div>
          
          <div className="space-y-4">
            {riskAlerts.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-primary-content mb-2">All Clear</h4>
                <p className="text-secondary-content">No risk alerts at this time</p>
              </div>
            ) : (
              riskAlerts.map((alert) => (
                <div key={alert.id} className={`p-4 rounded-lg border ${
                  alert.type === 'danger' ? 'bg-red-500/10 border-red-500/30' :
                  alert.type === 'warning' ? 'bg-yellow-500/10 border-yellow-500/30' :
                  'bg-blue-500/10 border-blue-500/30'
                } ${alert.acknowledged ? 'opacity-50' : ''}`}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <div className={`p-1 rounded ${
                        alert.type === 'danger' ? 'bg-red-500/20' :
                        alert.type === 'warning' ? 'bg-yellow-500/20' :
                        'bg-blue-500/20'
                      }`}>
                        {alert.type === 'danger' ? (
                          <XCircle className="h-4 w-4 text-red-400" />
                        ) : alert.type === 'warning' ? (
                          <AlertTriangle className="h-4 w-4 text-yellow-400" />
                        ) : (
                          <Info className="h-4 w-4 text-blue-400" />
                        )}
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-primary-content mb-1">
                          {alert.title}
                        </h4>
                        <p className="text-xs text-secondary-content mb-2">
                          {alert.message}
                        </p>
                        <div className="flex items-center space-x-2 text-xs text-secondary-content">
                          <Clock className="h-3 w-3" />
                          <span>{alert.timestamp.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                    
                    {!alert.acknowledged && (
                      <button
                        onClick={() => handleAcknowledgeAlert(alert.id)}
                        className="px-3 py-1 bg-slate-700 hover:bg-slate-600 rounded text-xs text-primary-content transition-colors"
                      >
                        Acknowledge
                      </button>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default RiskManagement 
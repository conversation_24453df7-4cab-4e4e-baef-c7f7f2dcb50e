import dotenv from 'dotenv'
import path from 'path'

// Load .env.local file explicitly
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') })
dotenv.config() // fallback

import { createClient } from '@supabase/supabase-js'
import { Database } from '../../types/database'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

console.log('🔧 Supabase Server Configuration:')
console.log('SUPABASE_URL:', !!supabaseUrl)
console.log('SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey)
console.log('SUPABASE_ANON_KEY:', !!supabaseAnonKey)

if (!supabaseUrl) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL or SUPABASE_URL environment variable')
}

if (!supabaseServiceKey) {
  console.warn('⚠️  SUPABASE_SERVICE_ROLE_KEY not found - some admin operations may fail')
  console.warn('Please add SUPABASE_SERVICE_ROLE_KEY to your .env.local file')
}

// Server-side Supabase client with service role key (bypasses RLS)
export const supabaseServer = createClient<Database>(
  supabaseUrl,
  supabaseServiceKey || supabaseAnonKey || '',
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    },
    global: {
      headers: {
        'X-Client-Info': 'propfirm-server'
      }
    }
  }
)

// For backward compatibility, export as supabase as well
export const supabase = supabaseServer

// Export admin client for admin operations
export const supabaseAdmin = supabaseServer

// For backward compatibility with API routes expecting createAdminClient
export const createAdminClient = () => supabaseServer 
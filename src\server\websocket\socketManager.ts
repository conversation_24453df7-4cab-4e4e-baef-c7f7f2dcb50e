import { Server as SocketIOServer, Socket } from 'socket.io'
import jwt from 'jsonwebtoken'
import { supabase } from '../../lib/supabase/server'

interface AuthenticatedSocket extends Socket {
  userId?: string
  accountId?: string
  userRole?: string
}

interface SocketUser {
  userId: string
  accountId?: string
  role: string
  socketId: string
  connectedAt: Date
}

// Store connected users
const connectedUsers = new Map<string, SocketUser>()
const userSockets = new Map<string, Set<string>>() // userId -> Set of socketIds

export const initializeWebSocket = (io: SocketIOServer) => {
  // Authentication middleware for WebSocket
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '')
      
      if (!token) {
        return next(new Error('Authentication token required'))
      }

      // Verify JWT token
      const JWT_SECRET = process.env.JWT_SECRET || process.env.SUPABASE_JWT_SECRET
      if (!JWT_SECRET) {
        return next(new Error('JWT secret not configured'))
      }

      // Verify token format (we'll use Supabase to validate the actual token)
      jwt.verify(token, JWT_SECRET) // This will throw if invalid

      // Verify user exists in Supabase
      const { data: user, error } = await supabase.auth.getUser(token)
      
      if (error || !user.user) {
        return next(new Error('Invalid token'))
      }

      // Get user profile
      const { data: profile } = await supabase
        .from('users')
        .select(`
          id,
          email,
          role,
          account_status,
          trading_accounts (
            id,
            account_type,
            status
          )
        `)
        .eq('id', user.user.id)
        .single()

      if (!profile || profile.account_status !== 'active') {
        return next(new Error('Account inactive'))
      }

      // Attach user data to socket
      socket.userId = profile.id
      socket.accountId = profile.trading_accounts?.[0]?.id
      socket.userRole = profile.role || 'trader'

      next()
    } catch (error) {
      console.error('WebSocket auth error:', error)
      next(new Error('Authentication failed'))
    }
  })

  io.on('connection', (socket: AuthenticatedSocket) => {
    console.log(`🔌 User ${socket.userId} connected via WebSocket`)

    if (socket.userId) {
      // Store user connection
      const socketUser: SocketUser = {
        userId: socket.userId,
        role: socket.userRole || 'trader',
        socketId: socket.id,
        connectedAt: new Date()
      }
      
      if (socket.accountId) {
        socketUser.accountId = socket.accountId
      }
      
      connectedUsers.set(socket.id, socketUser)

      // Track user sockets
      if (!userSockets.has(socket.userId)) {
        userSockets.set(socket.userId, new Set())
      }
      userSockets.get(socket.userId)?.add(socket.id)

      // Join user-specific rooms
      socket.join(`user:${socket.userId}`)
      if (socket.accountId) {
        socket.join(`account:${socket.accountId}`)
      }
      socket.join(`role:${socket.userRole}`)

      // Send connection confirmation
      socket.emit('connected', {
        message: 'Successfully connected to Prop Firm WebSocket',
        userId: socket.userId,
        accountId: socket.accountId,
        timestamp: new Date().toISOString()
      })
    }

    // Handle market data subscription
    socket.on('subscribe:market-data', (symbols: string[]) => {
      console.log(`📊 User ${socket.userId} subscribed to market data:`, symbols)
      symbols.forEach(symbol => {
        socket.join(`market:${symbol}`)
      })
      socket.emit('subscribed:market-data', { symbols, timestamp: new Date().toISOString() })
    })

    // Handle market data unsubscription
    socket.on('unsubscribe:market-data', (symbols: string[]) => {
      console.log(`📊 User ${socket.userId} unsubscribed from market data:`, symbols)
      symbols.forEach(symbol => {
        socket.leave(`market:${symbol}`)
      })
      socket.emit('unsubscribed:market-data', { symbols, timestamp: new Date().toISOString() })
    })

    // Handle account updates subscription
    socket.on('subscribe:account-updates', () => {
      if (socket.accountId) {
        socket.join(`account-updates:${socket.accountId}`)
        socket.emit('subscribed:account-updates', { 
          accountId: socket.accountId, 
          timestamp: new Date().toISOString() 
        })
      }
    })

    // Handle ping/pong for connection health
    socket.on('ping', () => {
      socket.emit('pong', { timestamp: new Date().toISOString() })
    })

    // Handle error events
    socket.on('error', (error) => {
      console.error(`❌ WebSocket error for user ${socket.userId}:`, error)
    })

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      console.log(`🔌 User ${socket.userId} disconnected: ${reason}`)
      
      // Clean up user tracking
      if (socket.userId) {
        connectedUsers.delete(socket.id)
        const userSocketSet = userSockets.get(socket.userId)
        if (userSocketSet) {
          userSocketSet.delete(socket.id)
          if (userSocketSet.size === 0) {
            userSockets.delete(socket.userId)
          }
        }
      }
    })
  })

  return io
}

// Utility functions for broadcasting data
export const broadcastMarketData = (io: SocketIOServer, symbol: string, data: any) => {
  io.to(`market:${symbol}`).emit('market-data', {
    type: 'market-data',
    symbol,
    ...data,
    timestamp: new Date().toISOString()
  })
}

export const broadcastAccountUpdate = (io: SocketIOServer, accountId: string, data: any) => {
  io.to(`account:${accountId}`).emit('account-update', {
    type: 'account-update',
    accountId,
    ...data,
    timestamp: new Date().toISOString()
  })
}

export const broadcastTradeUpdate = (io: SocketIOServer, accountId: string, data: any) => {
  io.to(`account:${accountId}`).emit('trade-update', {
    type: 'trade-update',
    accountId,
    ...data,
    timestamp: new Date().toISOString()
  })
}

export const broadcastRiskAlert = (io: SocketIOServer, accountId: string, alert: any) => {
  io.to(`account:${accountId}`).emit('risk-alert', {
    type: 'risk-alert',
    accountId,
    alert,
    timestamp: new Date().toISOString()
  })
}

export const broadcastAnalyticsUpdate = (io: SocketIOServer, accountId: string, metrics: any) => {
  io.to(`account:${accountId}`).emit('analytics-update', {
    type: 'analytics-update',
    accountId,
    metrics,
    timestamp: new Date().toISOString()
  })
}

export const sendToUser = (io: SocketIOServer, userId: string, event: string, data: any) => {
  io.to(`user:${userId}`).emit(event, {
    ...data,
    timestamp: new Date().toISOString()
  })
}

export const getConnectedUsers = (): Map<string, SocketUser> => {
  return connectedUsers
}

export const getUserSocketCount = (userId: string): number => {
  return userSockets.get(userId)?.size || 0
}

export const isUserConnected = (userId: string): boolean => {
  return userSockets.has(userId) && (userSockets.get(userId)?.size || 0) > 0
} 
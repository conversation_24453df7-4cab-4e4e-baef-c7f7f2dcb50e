import { Router, Request, Response } from 'express'
import { z } from 'zod'
import jwt from 'jsonwebtoken'
import { supabase } from '../../lib/supabase/server'
import { validateRequest, ValidationRequest } from '../middleware/validation'
import { async<PERSON>and<PERSON> } from '../middleware/errorHandler'
import { createAuthError, createInternalError } from '../middleware/errorHandler'

const router = Router()

// Validation schemas
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters')
})

const refreshTokenSchema = z.object({
  refresh_token: z.string().min(1, 'Refresh token is required')
})

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email format')
})

const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string().min(6, 'Password confirmation is required')
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

// POST /api/auth/login
router.post('/login', 
  validateRequest({ body: loginSchema }),
  asyncHandler(async (req: ValidationRequest, res: Response) => {
    const { email, password } = req.validatedBody

    try {
      // Authenticate with Supabase
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        throw createAuthError('Invalid credentials')
      }

      if (!data.user || !data.session) {
        throw createAuthError('Authentication failed')
      }

      // Get user profile
      const { data: profile, error: profileError } = await supabase
        .from('users')
        .select(`
          id,
          email,
          first_name,
          last_name,
          account_status,
          created_at,
          trading_accounts (
            id,
            phase,
            status,
            balance
          )
        `)
        .eq('id', data.user.id)
        .single()

      if (profileError || !profile) {
        throw createAuthError('User profile not found')
      }

      // Check account status
      if (profile.account_status !== 'active') {
        throw createAuthError('Account is suspended or inactive')
      }

      // Generate custom JWT with additional claims
      const JWT_SECRET = process.env.JWT_SECRET || process.env.SUPABASE_JWT_SECRET
      if (!JWT_SECRET) {
        throw createInternalError('JWT configuration error')
      }

      const accessToken = jwt.sign(
        {
          sub: profile.id,
          email: profile.email,
          role: 'trader',
          accountId: profile.trading_accounts?.[0]?.id
        },
        JWT_SECRET,
        { expiresIn: '15m' }
      )

      const refreshToken = jwt.sign(
        { sub: profile.id },
        JWT_SECRET,
        { expiresIn: '7d' }
      )

      // Update last login
      await supabase
        .from('users')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', profile.id)

      // Set secure cookies
      res.cookie('access_token', accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 15 * 60 * 1000 // 15 minutes
      })

      res.cookie('refresh_token', refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
      })

      res.json({
        success: true,
        data: {
          user: {
            id: profile.id,
            email: profile.email,
            firstName: profile.first_name,
            lastName: profile.last_name,
            role: 'trader',
            accountStatus: profile.account_status,
            createdAt: profile.created_at
          },
          accounts: profile.trading_accounts || [],
          tokens: {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: 900 // 15 minutes
          }
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// POST /api/auth/logout
router.post('/logout', asyncHandler(async (req: Request, res: Response) => {
  // Clear cookies
  res.clearCookie('access_token')
  res.clearCookie('refresh_token')

  // Optionally revoke tokens in Supabase
  const token = req.cookies?.access_token
  if (token) {
    try {
      await supabase.auth.signOut()
    } catch (error) {
      console.warn('Failed to revoke tokens in Supabase:', error)
    }
  }

  res.json({
    success: true,
    data: {
      message: 'Successfully logged out'
    }
  })
}))

// POST /api/auth/refresh
router.post('/refresh',
  validateRequest({ body: refreshTokenSchema }),
  asyncHandler(async (req: ValidationRequest, res: Response) => {
    const { refresh_token } = req.validatedBody

    try {
      const JWT_SECRET = process.env.JWT_SECRET || process.env.SUPABASE_JWT_SECRET
      if (!JWT_SECRET) {
        throw createInternalError('JWT configuration error')
      }

      // Verify refresh token
      const decoded = jwt.verify(refresh_token, JWT_SECRET) as any

      // Get user profile
      const { data: profile, error } = await supabase
        .from('users')
        .select(`
          id,
          email,
          account_status,
          trading_accounts (
            id,
            phase,
            status
          )
        `)
        .eq('id', decoded.sub)
        .single()

      if (error || !profile || profile.account_status !== 'active') {
        throw createAuthError('Invalid refresh token')
      }

      // Generate new tokens
      const accessToken = jwt.sign(
        {
          sub: profile.id,
          email: profile.email,
          role: 'trader',
          accountId: profile.trading_accounts?.[0]?.id
        },
        JWT_SECRET,
        { expiresIn: '15m' }
      )

      const newRefreshToken = jwt.sign(
        { sub: profile.id },
        JWT_SECRET,
        { expiresIn: '7d' }
      )

      // Set new cookies
      res.cookie('access_token', accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 15 * 60 * 1000
      })

      res.cookie('refresh_token', newRefreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000
      })

      res.json({
        success: true,
        data: {
          access_token: accessToken,
          refresh_token: newRefreshToken,
          expires_in: 900
        }
      })

    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw createAuthError('Refresh token expired')
      }
      throw createAuthError('Invalid refresh token')
    }
  })
)

// POST /api/auth/forgot-password
router.post('/forgot-password',
  validateRequest({ body: forgotPasswordSchema }),
  asyncHandler(async (req: ValidationRequest, res: Response) => {
    const { email } = req.validatedBody

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${process.env.FRONTEND_URL}/auth/reset-password`
      })

      if (error) {
        console.error('Password reset error:', error)
        // Don't reveal if email exists or not for security
      }

      res.json({
        success: true,
        data: {
          message: 'If an account with that email exists, a password reset link has been sent.'
        }
      })

    } catch (error) {
      throw createInternalError('Failed to send password reset email')
    }
  })
)

// POST /api/auth/reset-password
router.post('/reset-password',
  validateRequest({ body: resetPasswordSchema }),
  asyncHandler(async (req: ValidationRequest, res: Response) => {
    const { password } = req.validatedBody

    try {
      const { error } = await supabase.auth.updateUser({
        password: password
      })

      if (error) {
        throw createAuthError('Invalid or expired reset token')
      }

      res.json({
        success: true,
        data: {
          message: 'Password successfully reset'
        }
      })

    } catch (error) {
      throw createAuthError('Failed to reset password')
    }
  })
)

// GET /api/auth/me
router.get('/me', asyncHandler(async (req: Request, res: Response) => {
  const token = req.headers.authorization?.replace('Bearer ', '') || req.cookies?.access_token

  if (!token) {
    throw createAuthError('Authentication token required')
  }

  try {
    const { data: user, error } = await supabase.auth.getUser(token)
    
    if (error || !user.user) {
      throw createAuthError('Invalid token')
    }

    // Get user profile
    const { data: profile } = await supabase
      .from('users')
      .select(`
        id,
        email,
        first_name,
        last_name,
        account_status,
        created_at,
        trading_accounts (
          id,
          phase,
          status,
          balance
        )
      `)
      .eq('id', user.user.id)
      .single()

    if (!profile) {
      throw createAuthError('User profile not found')
    }

    res.json({
      success: true,
      data: {
        user: {
          id: profile.id,
          email: profile.email,
          firstName: profile.first_name,
          lastName: profile.last_name,
          role: 'trader',
          accountStatus: profile.account_status,
          createdAt: profile.created_at
        },
        accounts: profile.trading_accounts || []
      }
    })

  } catch (error) {
    throw createAuthError('Authentication failed')
  }
}))

export default router 
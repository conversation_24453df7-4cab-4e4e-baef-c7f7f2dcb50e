import { useState, useEffect } from 'react'
import { apiClient, type ApiResponse } from '@/lib/api/client'

/**
 * Custom hook for fetching data from API endpoints
 * Includes race condition prevention and proper cleanup
 * Following React best practices from Context7 documentation
 */
export function useApiData<T>(
  fetcher: () => Promise<ApiResponse<T>>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let ignore = false

    async function fetchData() {
      try {
        setLoading(true)
        setError(null)
        
        const result = await fetcher()
        
        if (!ignore) {
          if (result.success && result.data) {
            setData(result.data)
          } else {
            setError(result.error?.message || 'Failed to fetch data')
          }
        }
      } catch (err) {
        if (!ignore) {
          setError(err instanceof Error ? err.message : 'An error occurred')
        }
      } finally {
        if (!ignore) {
          setLoading(false)
        }
      }
    }

    fetchData()

    return () => {
      ignore = true
    }
  }, dependencies)

  const refetch = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const result = await fetcher()
      
      if (result.success && result.data) {
        setData(result.data)
      } else {
        setError(result.error?.message || 'Failed to fetch data')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  return { data, loading, error, refetch }
}

/**
 * Hook specifically for Analytics data
 */
export function useAnalyticsData(accountId: string, timeframe?: string) {
  return useApiData(
    () => apiClient.getPerformanceMetrics(accountId, timeframe),
    [accountId, timeframe]
  )
}

/**
 * Hook for Equity Curve data
 */
export function useEquityCurve(accountId: string, timeframe?: string) {
  return useApiData(
    () => apiClient.getEquityCurve(accountId, timeframe),
    [accountId, timeframe]
  )
}

/**
 * Hook for Daily P&L data
 */
export function useDailyPnL(accountId: string, timeframe?: string) {
  return useApiData(
    () => apiClient.getDailyPnL(accountId, timeframe),
    [accountId, timeframe]
  )
}

/**
 * Hook for Risk Metrics data
 */
export function useRiskMetrics(accountId: string) {
  return useApiData(
    () => apiClient.getCurrentRiskMetrics(accountId),
    [accountId]
  )
}

/**
 * Hook for Risk Rules data
 */
export function useRiskRules(accountId: string) {
  return useApiData(
    () => apiClient.getRiskRules(accountId),
    [accountId]
  )
}

/**
 * Hook for Risk Alerts data
 */
export function useRiskAlerts(accountId: string) {
  return useApiData(
    () => apiClient.getRiskAlerts(accountId),
    [accountId]
  )
}

/**
 * Hook for User Profile data
 */
export function useUserProfile() {
  return useApiData(
    () => apiClient.getUserProfile(),
    []
  )
} 
'use client'

import { useState } from 'react'
import { 
  HelpCircle, 
  Search,
  Book,
  MessageCircle,
  Mail,
  Phone,
  FileText,
  Video,
  Download,
  ExternalLink,
  ChevronDown,
  ChevronRight,
  Clock,
  Send,
  Paperclip,
  ThumbsUp,
  ThumbsDown,
  Plus,
  RefreshCw,
  AlertTriangle
} from 'lucide-react'
import { useApiData } from '@/hooks/useApiData'
import { apiClient } from '@/lib/api/client'

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
  helpful: number
  notHelpful: number
}

interface SupportTicket {
  id: string
  subject: string
  status: 'open' | 'pending' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  createdAt: Date
  lastUpdate: Date
}

const Help = () => {
  const [activeTab, setActiveTab] = useState('faq')
  const [searchQuery, setSearchQuery] = useState('')
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null)
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [ticketForm, setTicketForm] = useState({
    subject: '',
    category: 'general',
    priority: 'medium',
    description: '',
    attachments: []
  })

  // Fetch real FAQ data from backend
  const { 
    data: faqDataResponse, 
    loading: faqLoading, 
    error: faqError,
    refetch: refetchFAQ 
  } = useApiData(() => apiClient.getFAQ(selectedCategory !== 'all' ? selectedCategory : undefined, searchQuery), [selectedCategory, searchQuery])

  // Fetch real support tickets from backend
  const { 
    data: ticketsDataResponse, 
    loading: ticketsLoading, 
    error: ticketsError,
    refetch: refetchTickets 
  } = useApiData(() => apiClient.getSupportTickets(), [])

  const isLoading = faqLoading || ticketsLoading
  const hasError = faqError || ticketsError

  // Fallback FAQ data
  const fallbackFAQData: FAQItem[] = [
    {
      id: '1',
      question: 'How do I start a trading challenge?',
      answer: 'To start a trading challenge, navigate to the Dashboard and click on "Start Challenge". Choose your preferred challenge type, complete the payment process, and you\'ll receive your MT5 login credentials within 24 hours.',
      category: 'challenges',
      helpful: 45,
      notHelpful: 3
    },
    {
      id: '2',
      question: 'What are the risk management rules?',
      answer: 'Our risk management rules include: Maximum daily loss of 5%, maximum total drawdown of 10%, position size limits, and mandatory stop losses. These rules are automatically monitored and enforced.',
      category: 'risk',
      helpful: 38,
      notHelpful: 2
    },
    {
      id: '3',
      question: 'How do I withdraw my profits?',
      answer: 'Profits can be withdrawn after successfully completing the challenge phases. Navigate to Wallet > Withdraw, choose your preferred method (bank transfer, crypto, etc.), and submit your request. Processing takes 1-3 business days.',
      category: 'payments',
      helpful: 52,
      notHelpful: 1
    },
    {
      id: '4',
      question: 'Can I use Expert Advisors (EAs)?',
      answer: 'Yes, Expert Advisors are allowed on all our challenge accounts. However, they must comply with our risk management rules and trading guidelines. High-frequency scalping EAs are not permitted.',
      category: 'trading',
      helpful: 29,
      notHelpful: 5
    },
    {
      id: '5',
      question: 'What happens if I violate a rule?',
      answer: 'Rule violations result in immediate account suspension. Depending on the severity, you may be given a warning, account reset, or permanent disqualification. All violations are logged and reviewed.',
      category: 'risk',
      helpful: 33,
      notHelpful: 8
    }
  ]

  // Fallback support tickets
  const fallbackSupportTickets: SupportTicket[] = [
    {
      id: 'TK-001',
      subject: 'MT5 Login Issues',
      status: 'pending',
      priority: 'high',
      createdAt: new Date('2024-01-15'),
      lastUpdate: new Date('2024-01-16')
    },
    {
      id: 'TK-002',
      subject: 'Withdrawal Request',
      status: 'resolved',
      priority: 'medium',
      createdAt: new Date('2024-01-10'),
      lastUpdate: new Date('2024-01-12')
    }
  ]

  // Use API data if available, otherwise fallback to demo data
  const faqData = faqDataResponse?.faqs || fallbackFAQData
  const supportTickets = ticketsDataResponse?.tickets || fallbackSupportTickets

  const categories = [
    { id: 'all', label: 'All Categories' },
    { id: 'challenges', label: 'Challenges' },
    { id: 'trading', label: 'Trading' },
    { id: 'risk', label: 'Risk Management' },
    { id: 'payments', label: 'Payments' },
    { id: 'technical', label: 'Technical Support' }
  ]

  const filteredFAQs = faqData.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const handleTicketSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      // Submit ticket to backend
      await apiClient.createSupportTicket({
        subject: ticketForm.subject,
        category: ticketForm.category,
        priority: ticketForm.priority as 'low' | 'medium' | 'high' | 'urgent',
        description: ticketForm.description
      })
      
      // Reset form
      setTicketForm({
        subject: '',
        category: 'general',
        priority: 'medium',
        description: '',
        attachments: []
      })
      
      // Refresh tickets list
      await refetchTickets()
      
      console.log('Ticket submitted successfully!')
    } catch (error) {
      console.error('Failed to submit ticket:', error)
    }
  }

  // Handle refresh for all data
  const handleRefresh = async () => {
    await Promise.all([
      refetchFAQ(),
      refetchTickets()
    ])
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'text-blue-400 bg-blue-500/20'
      case 'pending': return 'text-yellow-400 bg-yellow-500/20'
      case 'resolved': return 'text-green-400 bg-green-500/20'
      case 'closed': return 'text-gray-400 bg-gray-500/20'
      default: return 'text-gray-400 bg-gray-500/20'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'text-green-400'
      case 'medium': return 'text-yellow-400'
      case 'high': return 'text-orange-400'
      case 'urgent': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="glass-card p-8 text-center">
          <RefreshCw className="h-8 w-8 text-green-500 animate-spin mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-primary-content mb-2">Loading Help Center</h3>
          <p className="text-secondary-content">Fetching FAQ and support information...</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (hasError) {
    return (
      <div className="space-y-6">
        <div className="glass-card p-8 text-center">
          <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-primary-content mb-2">Error Loading Help Center</h3>
          <p className="text-secondary-content mb-4">
            {faqError || ticketsError || 'Failed to load help center data'}
          </p>
          <button
            onClick={handleRefresh}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-primary-content">Help & Support</h1>
          <p className="text-secondary-content mt-1">
            Find answers to your questions and get the help you need
            {faqDataResponse ? ' (Live Data)' : ' (Demo Data)'}
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button className="px-4 py-2 bg-green-500 hover:bg-green-600 rounded-lg text-white font-medium flex items-center space-x-2 transition-colors">
            <MessageCircle className="h-4 w-4" />
            <span>Live Chat</span>
          </button>
        </div>
      </div>

      {/* Quick Search */}
      <div className="glass-card p-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-secondary-content" />
          <input
            type="text"
            placeholder="Search for help articles, guides, or common questions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content placeholder-secondary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex items-center space-x-1 bg-slate-800 rounded-lg p-1">
        {[
          { id: 'faq', label: 'FAQ', icon: HelpCircle },
          { id: 'guides', label: 'Guides', icon: Book },
          { id: 'contact', label: 'Contact', icon: MessageCircle },
          { id: 'tickets', label: 'My Tickets', icon: FileText }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-4 py-2 rounded text-sm font-medium transition-colors flex items-center space-x-2 ${
              activeTab === tab.id 
                ? 'bg-green-500 text-white' 
                : 'text-secondary-content hover:text-primary-content'
            }`}
          >
            <tab.icon className="h-4 w-4" />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* FAQ Tab */}
      {activeTab === 'faq' && (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Category Filter */}
          <div className="lg:col-span-1">
            <div className="glass-card p-4">
              <h3 className="text-lg font-semibold text-primary-content mb-4">Categories</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                      selectedCategory === category.id
                        ? 'bg-green-500/20 text-green-400'
                        : 'text-secondary-content hover:text-primary-content hover:bg-slate-700/50'
                    }`}
                  >
                    {category.label}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* FAQ List */}
          <div className="lg:col-span-3">
            <div className="space-y-4">
              {filteredFAQs.map((faq) => (
                <div key={faq.id} className="glass-card">
                  <button
                    onClick={() => setExpandedFAQ(expandedFAQ === faq.id ? null : faq.id)}
                    className="w-full p-4 text-left flex items-center justify-between hover:bg-slate-700/30 transition-colors"
                  >
                    <span className="text-primary-content font-medium">{faq.question}</span>
                    {expandedFAQ === faq.id ? (
                      <ChevronDown className="h-5 w-5 text-secondary-content" />
                    ) : (
                      <ChevronRight className="h-5 w-5 text-secondary-content" />
                    )}
                  </button>
                  
                  {expandedFAQ === faq.id && (
                    <div className="px-4 pb-4 border-t border-slate-700">
                      <p className="text-secondary-content mt-4 mb-4">{faq.answer}</p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <span className="text-xs text-secondary-content">Was this helpful?</span>
                          <div className="flex items-center space-x-2">
                            <button className="flex items-center space-x-1 text-green-400 hover:text-green-300 transition-colors">
                              <ThumbsUp className="h-4 w-4" />
                              <span className="text-xs">{faq.helpful}</span>
                            </button>
                            <button className="flex items-center space-x-1 text-red-400 hover:text-red-300 transition-colors">
                              <ThumbsDown className="h-4 w-4" />
                              <span className="text-xs">{faq.notHelpful}</span>
                            </button>
                          </div>
                        </div>
                        <span className="text-xs text-secondary-content capitalize bg-slate-700 px-2 py-1 rounded">
                          {faq.category}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Guides Tab */}
      {activeTab === 'guides' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            {
              title: 'Getting Started Guide',
              description: 'Complete guide to setting up your account and starting your first challenge',
              type: 'PDF',
              icon: FileText,
              color: 'text-blue-400'
            },
            {
              title: 'Risk Management Best Practices',
              description: 'Learn how to manage risk effectively and avoid common pitfalls',
              type: 'Video',
              icon: Video,
              color: 'text-purple-400'
            },
            {
              title: 'MT5 Platform Tutorial',
              description: 'Step-by-step guide to using MetaTrader 5 effectively',
              type: 'Video',
              icon: Video,
              color: 'text-green-400'
            },
            {
              title: 'Trading Rules & Guidelines',
              description: 'Comprehensive overview of all trading rules and requirements',
              type: 'PDF',
              icon: FileText,
              color: 'text-orange-400'
            },
            {
              title: 'API Documentation',
              description: 'Technical documentation for developers using our API',
              type: 'Web',
              icon: ExternalLink,
              color: 'text-cyan-400'
            },
            {
              title: 'Withdrawal Process',
              description: 'How to request and process profit withdrawals',
              type: 'PDF',
              icon: FileText,
              color: 'text-yellow-400'
            }
          ].map((guide, index) => (
            <div key={index} className="glass-card p-6 hover:bg-slate-700/30 transition-colors cursor-pointer">
              <div className="flex items-start space-x-4">
                <div className={`p-2 rounded-lg bg-slate-800 ${guide.color}`}>
                  <guide.icon className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-primary-content mb-2">{guide.title}</h3>
                  <p className="text-secondary-content text-sm mb-3">{guide.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-secondary-content bg-slate-700 px-2 py-1 rounded">
                      {guide.type}
                    </span>
                    <button className="text-green-400 hover:text-green-300 transition-colors">
                      <Download className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Contact Tab */}
      {activeTab === 'contact' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Contact Methods */}
          <div className="space-y-6">
            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold text-primary-content mb-4">Contact Methods</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-4 p-4 bg-slate-800/30 rounded-lg">
                  <div className="p-2 bg-green-500/20 rounded-lg">
                    <MessageCircle className="h-5 w-5 text-green-400" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-primary-content">Live Chat</h4>
                    <p className="text-xs text-secondary-content">Available 24/7 for immediate assistance</p>
                  </div>
                  <button className="ml-auto px-3 py-1 bg-green-500/20 text-green-400 rounded text-xs hover:bg-green-500/30 transition-colors">
                    Start Chat
                  </button>
                </div>
                
                <div className="flex items-center space-x-4 p-4 bg-slate-800/30 rounded-lg">
                  <div className="p-2 bg-blue-500/20 rounded-lg">
                    <Mail className="h-5 w-5 text-blue-400" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-primary-content">Email Support</h4>
                    <p className="text-xs text-secondary-content"><EMAIL></p>
                  </div>
                  <button className="ml-auto px-3 py-1 bg-blue-500/20 text-blue-400 rounded text-xs hover:bg-blue-500/30 transition-colors">
                    Send Email
                  </button>
                </div>
                
                <div className="flex items-center space-x-4 p-4 bg-slate-800/30 rounded-lg">
                  <div className="p-2 bg-purple-500/20 rounded-lg">
                    <Phone className="h-5 w-5 text-purple-400" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-primary-content">Phone Support</h4>
                    <p className="text-xs text-secondary-content">+****************</p>
                  </div>
                  <button className="ml-auto px-3 py-1 bg-purple-500/20 text-purple-400 rounded text-xs hover:bg-purple-500/30 transition-colors">
                    Call Now
                  </button>
                </div>
              </div>
            </div>

            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold text-primary-content mb-4">Support Hours</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-secondary-content">Live Chat</span>
                  <span className="text-primary-content">24/7</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-content">Email Support</span>
                  <span className="text-primary-content">24/7</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-content">Phone Support</span>
                  <span className="text-primary-content">Mon-Fri 9AM-6PM EST</span>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="glass-card p-6">
            <h3 className="text-lg font-semibold text-primary-content mb-4">Send us a Message</h3>
            <form onSubmit={handleTicketSubmit} className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-primary-content">Subject</label>
                <input
                  type="text"
                  value={ticketForm.subject}
                  onChange={(e) => setTicketForm(prev => ({ ...prev, subject: e.target.value }))}
                  className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Brief description of your issue"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-primary-content">Category</label>
                  <select
                    value={ticketForm.category}
                    onChange={(e) => setTicketForm(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  >
                    <option value="general">General</option>
                    <option value="technical">Technical</option>
                    <option value="billing">Billing</option>
                    <option value="trading">Trading</option>
                  </select>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-primary-content">Priority</label>
                  <select
                    value={ticketForm.priority}
                    onChange={(e) => setTicketForm(prev => ({ ...prev, priority: e.target.value }))}
                    className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="urgent">Urgent</option>
                  </select>
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium text-primary-content">Description</label>
                <textarea
                  value={ticketForm.description}
                  onChange={(e) => setTicketForm(prev => ({ ...prev, description: e.target.value }))}
                  rows={4}
                  className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Please provide detailed information about your issue..."
                />
              </div>
              
              <div className="flex items-center space-x-4">
                <button
                  type="button"
                  className="flex items-center space-x-2 px-4 py-2 bg-slate-700 hover:bg-slate-600 rounded-lg text-secondary-content transition-colors"
                >
                  <Paperclip className="h-4 w-4" />
                  <span>Attach Files</span>
                </button>
                
                <button
                  type="submit"
                  className="flex items-center space-x-2 px-4 py-2 bg-green-500 hover:bg-green-600 rounded-lg text-white font-medium transition-colors"
                >
                  <Send className="h-4 w-4" />
                  <span>Send Message</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* My Tickets Tab */}
      {activeTab === 'tickets' && (
        <div className="space-y-6">
          <div className="glass-card p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-primary-content">Support Tickets</h3>
              <button className="px-4 py-2 bg-green-500 hover:bg-green-600 rounded-lg text-white font-medium flex items-center space-x-2 transition-colors">
                <Plus className="h-4 w-4" />
                <span>New Ticket</span>
              </button>
            </div>
            
            <div className="space-y-4">
              {supportTickets.map((ticket) => (
                <div key={ticket.id} className="p-4 bg-slate-800/30 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-primary-content">{ticket.id}</span>
                      <span className={`px-2 py-1 rounded text-xs ${getStatusColor(ticket.status)}`}>
                        {ticket.status.toUpperCase()}
                      </span>
                      <span className={`text-xs ${getPriorityColor(ticket.priority)}`}>
                        {ticket.priority.toUpperCase()}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-xs text-secondary-content">
                      <Clock className="h-3 w-3" />
                      <span>Updated {ticket.lastUpdate.toLocaleDateString()}</span>
                    </div>
                  </div>
                  
                  <h4 className="text-primary-content font-medium mb-2">{ticket.subject}</h4>
                  
                  <div className="flex items-center justify-between text-xs text-secondary-content">
                    <span>Created: {ticket.createdAt.toLocaleDateString()}</span>
                    <button className="text-green-400 hover:text-green-300 transition-colors">
                      View Details
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Help 
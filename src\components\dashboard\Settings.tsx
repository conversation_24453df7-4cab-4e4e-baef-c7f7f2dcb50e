'use client'

import { useState, useEffect } from 'react'
import { 
  Settings as SettingsIcon, 
  User,
  Bell,
  Shield,
  Key,
  Moon,
  Sun,
  Monitor,
  Mail,
  Save,
  Eye,
  EyeOff,
  Smartphone,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Download,
  Upload,
  ExternalLink
} from 'lucide-react'
import { useUserProfile, useApiData } from '@/hooks/useApiData'
import { apiClient } from '@/lib/api/client'

interface SettingsProps {
  user?: {
    id: string
    email: string
    user_metadata?: {
      full_name?: string
      first_name?: string
      last_name?: string
    }
  }
}

const Settings = ({ user }: SettingsProps) => {
  const [activeTab, setActiveTab] = useState('profile')
  const [showApiKey, setShowApiKey] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  // Fetch real user data from backend
  const { 
    data: userProfileData, 
    loading: profileLoading, 
    error: profileError,
    refetch: refetchProfile 
  } = useUserProfile()

  const { 
    data: tradingPreferencesData, 
    loading: preferencesLoading, 
    error: preferencesError,
    refetch: refetchPreferences 
  } = useApiData(() => apiClient.getTradingPreferences(), [])

  const { 
    data: notificationSettingsData, 
    loading: notificationsLoading, 
    error: notificationsError,
    refetch: refetchNotifications 
  } = useApiData(() => apiClient.getNotificationSettings(), [])

  const isLoading = profileLoading || preferencesLoading || notificationsLoading
  const hasError = profileError || preferencesError || notificationsError

  // Fallback profile data 
  const fallbackProfileData = {
    firstName: user?.user_metadata?.first_name || 'Demo',
    lastName: user?.user_metadata?.last_name || 'Trader',
    email: user?.email || '<EMAIL>',
    phone: '+****************',
    country: 'United States',
    timezone: 'America/New_York',
    language: 'English',
    dateOfBirth: '1990-01-01'
  }

  // Profile Settings State - use API data if available
  const [profileData, setProfileData] = useState(userProfileData || fallbackProfileData)

  // Fallback trading preferences
  const fallbackTradingPrefs = {
    defaultLotSize: 0.01,
    maxSlippage: 3,
    closeOnFriday: true,
    autoStopLoss: true,
    defaultStopLoss: 50,
    defaultTakeProfit: 100,
    riskPerTrade: 1.0,
    tradingHours: {
      start: '08:00',
      end: '18:00'
    }
  }

  // Fallback notification settings
  const fallbackNotificationSettings = {
    email: {
      tradeAlerts: true,
      riskAlerts: true,
      marketNews: false,
      accountUpdates: true,
      weeklyReports: true
    },
    push: {
      tradeAlerts: true,
      riskAlerts: true,
      marketNews: false,
      accountUpdates: false
    },
    sms: {
      riskAlerts: true,
      accountUpdates: false
    }
  }

  // Trading Preferences State - use API data if available
  const [tradingPrefs, setTradingPrefs] = useState(tradingPreferencesData || fallbackTradingPrefs)

  // Notification Settings State - use API data if available
  const [notificationSettings, setNotificationSettings] = useState(notificationSettingsData || fallbackNotificationSettings)

  // Security Settings State
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorEnabled: false,
    loginAlerts: true,
    sessionTimeout: 30,
    apiAccess: false,
    ipWhitelist: '',
    lastPasswordChange: new Date('2024-01-15')
  })

  // Theme State
  const [theme, setTheme] = useState('dark')

  const handleSaveProfile = async () => {
    setIsSaving(true)
    try {
      // Save profile data
      await apiClient.updateUserProfile(profileData)
      
      // Save trading preferences if changed
      await apiClient.updateTradingPreferences(tradingPrefs)
      
      // Save notification settings if changed
      await apiClient.updateNotificationSettings(notificationSettings)
      
      // Refetch data to get latest from server
      await Promise.all([
        refetchProfile(),
        refetchPreferences(),
        refetchNotifications()
      ])
      
      // Show success message
      console.log('Settings saved successfully!')
    } catch (error) {
      console.error('Failed to save settings:', error)
    } finally {
      setIsSaving(false)
    }
  }

  // Handle refresh for all data
  const handleRefresh = async () => {
    await Promise.all([
      refetchProfile(),
      refetchPreferences(),
      refetchNotifications()
    ])
  }

  const handleGenerateApiKey = () => {
    // Generate new API key
    console.log('Generating new API key...')
  }

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'trading', label: 'Trading', icon: SettingsIcon },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'api', label: 'API', icon: Key },
    { id: 'appearance', label: 'Appearance', icon: Monitor }
  ]

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="glass-card p-8 text-center">
          <RefreshCw className="h-8 w-8 text-green-500 animate-spin mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-primary-content mb-2">Loading Settings</h3>
          <p className="text-secondary-content">Fetching your account preferences...</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (hasError) {
    return (
      <div className="space-y-6">
        <div className="glass-card p-8 text-center">
          <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-primary-content mb-2">Error Loading Settings</h3>
          <p className="text-secondary-content mb-4">
            {profileError || preferencesError || notificationsError || 'Failed to load settings data'}
          </p>
          <button
            onClick={handleRefresh}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-primary-content">Settings</h1>
          <p className="text-secondary-content mt-1">
            Manage your account preferences and configurations
            {userProfileData ? ' (Live Data)' : ' (Demo Data)'}
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={handleSaveProfile}
            disabled={isSaving}
            className="px-4 py-2 bg-green-500 hover:bg-green-600 disabled:opacity-50 rounded-lg text-white font-medium flex items-center space-x-2 transition-colors"
          >
            {isSaving ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Save className="h-4 w-4" />
            )}
            <span>{isSaving ? 'Saving...' : 'Save Changes'}</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="glass-card p-4">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                    activeTab === tab.id
                      ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                      : 'text-secondary-content hover:text-primary-content hover:bg-slate-700/50'
                  }`}
                >
                  <tab.icon className="h-4 w-4" />
                  <span className="text-sm font-medium">{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          {/* Profile Tab */}
          {activeTab === 'profile' && (
            <div className="space-y-6">
              <div className="glass-card p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-primary-content">Personal Information</h3>
                  <User className="h-5 w-5 text-secondary-content" />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-primary-content">First Name</label>
                    <input
                      type="text"
                      value={profileData.firstName}
                      onChange={(e) => setProfileData(prev => ({ ...prev, firstName: e.target.value }))}
                      className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-primary-content">Last Name</label>
                    <input
                      type="text"
                      value={profileData.lastName}
                      onChange={(e) => setProfileData(prev => ({ ...prev, lastName: e.target.value }))}
                      className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-primary-content">Email Address</label>
                    <input
                      type="email"
                      value={profileData.email}
                      onChange={(e) => setProfileData(prev => ({ ...prev, email: e.target.value }))}
                      className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-primary-content">Phone Number</label>
                    <input
                      type="tel"
                      value={profileData.phone}
                      onChange={(e) => setProfileData(prev => ({ ...prev, phone: e.target.value }))}
                      className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-primary-content">Country</label>
                    <select
                      value={profileData.country}
                      onChange={(e) => setProfileData(prev => ({ ...prev, country: e.target.value }))}
                      className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    >
                      <option value="United States">United States</option>
                      <option value="United Kingdom">United Kingdom</option>
                      <option value="Canada">Canada</option>
                      <option value="Australia">Australia</option>
                      <option value="Germany">Germany</option>
                    </select>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-primary-content">Timezone</label>
                    <select
                      value={profileData.timezone}
                      onChange={(e) => setProfileData(prev => ({ ...prev, timezone: e.target.value }))}
                      className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    >
                      <option value="America/New_York">Eastern Time (ET)</option>
                      <option value="America/Chicago">Central Time (CT)</option>
                      <option value="America/Denver">Mountain Time (MT)</option>
                      <option value="America/Los_Angeles">Pacific Time (PT)</option>
                      <option value="Europe/London">GMT</option>
                      <option value="Europe/Berlin">CET</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Trading Tab */}
          {activeTab === 'trading' && (
            <div className="space-y-6">
              <div className="glass-card p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-primary-content">Trading Preferences</h3>
                  <SettingsIcon className="h-5 w-5 text-secondary-content" />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-primary-content">Default Lot Size</label>
                    <input
                      type="number"
                      step="0.01"
                      value={tradingPrefs.defaultLotSize}
                      onChange={(e) => setTradingPrefs(prev => ({ ...prev, defaultLotSize: parseFloat(e.target.value) }))}
                      className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-primary-content">Max Slippage (pips)</label>
                    <input
                      type="number"
                      value={tradingPrefs.maxSlippage}
                      onChange={(e) => setTradingPrefs(prev => ({ ...prev, maxSlippage: parseInt(e.target.value) }))}
                      className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-primary-content">Default Stop Loss (pips)</label>
                    <input
                      type="number"
                      value={tradingPrefs.defaultStopLoss}
                      onChange={(e) => setTradingPrefs(prev => ({ ...prev, defaultStopLoss: parseInt(e.target.value) }))}
                      className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-primary-content">Default Take Profit (pips)</label>
                    <input
                      type="number"
                      value={tradingPrefs.defaultTakeProfit}
                      onChange={(e) => setTradingPrefs(prev => ({ ...prev, defaultTakeProfit: parseInt(e.target.value) }))}
                      className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-primary-content">Risk Per Trade (%)</label>
                    <input
                      type="number"
                      step="0.1"
                      value={tradingPrefs.riskPerTrade}
                      onChange={(e) => setTradingPrefs(prev => ({ ...prev, riskPerTrade: parseFloat(e.target.value) }))}
                      className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>
                </div>
                
                <div className="mt-6 space-y-4">
                  <div className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg">
                    <div>
                      <h4 className="text-sm font-medium text-primary-content">Auto Stop Loss</h4>
                      <p className="text-xs text-secondary-content">Automatically set stop loss on new positions</p>
                    </div>
                    <button
                      onClick={() => setTradingPrefs(prev => ({ ...prev, autoStopLoss: !prev.autoStopLoss }))}
                      className={`w-10 h-6 rounded-full transition-colors relative ${
                        tradingPrefs.autoStopLoss ? 'bg-green-500' : 'bg-slate-600'
                      }`}
                    >
                      <div className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${
                        tradingPrefs.autoStopLoss ? 'translate-x-5' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg">
                    <div>
                      <h4 className="text-sm font-medium text-primary-content">Close Positions on Friday</h4>
                      <p className="text-xs text-secondary-content">Automatically close all positions before weekend</p>
                    </div>
                    <button
                      onClick={() => setTradingPrefs(prev => ({ ...prev, closeOnFriday: !prev.closeOnFriday }))}
                      className={`w-10 h-6 rounded-full transition-colors relative ${
                        tradingPrefs.closeOnFriday ? 'bg-green-500' : 'bg-slate-600'
                      }`}
                    >
                      <div className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${
                        tradingPrefs.closeOnFriday ? 'translate-x-5' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Notifications Tab */}
          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <div className="glass-card p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-primary-content">Notification Settings</h3>
                  <Bell className="h-5 w-5 text-secondary-content" />
                </div>
                
                <div className="space-y-6">
                  {/* Email Notifications */}
                  <div>
                    <h4 className="text-md font-medium text-primary-content mb-4 flex items-center space-x-2">
                      <Mail className="h-4 w-4" />
                      <span>Email Notifications</span>
                    </h4>
                    <div className="space-y-3">
                      {Object.entries(notificationSettings.email).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg">
                          <span className="text-sm text-primary-content capitalize">
                            {key.replace(/([A-Z])/g, ' $1').trim()}
                          </span>
                          <button
                            onClick={() => setNotificationSettings(prev => ({
                              ...prev,
                              email: { ...prev.email, [key]: !value }
                            }))}
                            className={`w-10 h-6 rounded-full transition-colors relative ${
                              value ? 'bg-green-500' : 'bg-slate-600'
                            }`}
                          >
                            <div className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${
                              value ? 'translate-x-5' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Push Notifications */}
                  <div>
                    <h4 className="text-md font-medium text-primary-content mb-4 flex items-center space-x-2">
                      <Smartphone className="h-4 w-4" />
                      <span>Push Notifications</span>
                    </h4>
                    <div className="space-y-3">
                      {Object.entries(notificationSettings.push).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg">
                          <span className="text-sm text-primary-content capitalize">
                            {key.replace(/([A-Z])/g, ' $1').trim()}
                          </span>
                          <button
                            onClick={() => setNotificationSettings(prev => ({
                              ...prev,
                              push: { ...prev.push, [key]: !value }
                            }))}
                            className={`w-10 h-6 rounded-full transition-colors relative ${
                              value ? 'bg-green-500' : 'bg-slate-600'
                            }`}
                          >
                            <div className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${
                              value ? 'translate-x-5' : 'translate-x-1'
                            }`} />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Security Tab */}
          {activeTab === 'security' && (
            <div className="space-y-6">
              <div className="glass-card p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-primary-content">Security Settings</h3>
                  <Shield className="h-5 w-5 text-secondary-content" />
                </div>
                
                <div className="space-y-6">
                  <div className="flex items-center justify-between p-4 border border-green-500/30 bg-green-500/10 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-400" />
                      <div>
                        <h4 className="text-sm font-medium text-primary-content">Two-Factor Authentication</h4>
                        <p className="text-xs text-secondary-content">
                          {securitySettings.twoFactorEnabled ? 'Enabled' : 'Recommended for enhanced security'}
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={() => setSecuritySettings(prev => ({ ...prev, twoFactorEnabled: !prev.twoFactorEnabled }))}
                      className={`px-4 py-2 rounded-lg font-medium text-sm transition-colors ${
                        securitySettings.twoFactorEnabled
                          ? 'bg-red-500/20 text-red-400 hover:bg-red-500/30'
                          : 'bg-green-500/20 text-green-400 hover:bg-green-500/30'
                      }`}
                    >
                      {securitySettings.twoFactorEnabled ? 'Disable' : 'Enable'}
                    </button>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg">
                      <div>
                        <h4 className="text-sm font-medium text-primary-content">Login Alerts</h4>
                        <p className="text-xs text-secondary-content">Get notified of new login attempts</p>
                      </div>
                      <button
                        onClick={() => setSecuritySettings(prev => ({ ...prev, loginAlerts: !prev.loginAlerts }))}
                        className={`w-10 h-6 rounded-full transition-colors relative ${
                          securitySettings.loginAlerts ? 'bg-green-500' : 'bg-slate-600'
                        }`}
                      >
                        <div className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${
                          securitySettings.loginAlerts ? 'translate-x-5' : 'translate-x-1'
                        }`} />
                      </button>
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-primary-content">Session Timeout (minutes)</label>
                      <select
                        value={securitySettings.sessionTimeout}
                        onChange={(e) => setSecuritySettings(prev => ({ ...prev, sessionTimeout: parseInt(e.target.value) }))}
                        className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      >
                        <option value={15}>15 minutes</option>
                        <option value={30}>30 minutes</option>
                        <option value={60}>1 hour</option>
                        <option value={120}>2 hours</option>
                        <option value={480}>8 hours</option>
                      </select>
                    </div>
                    
                    <div className="p-4 bg-slate-800/50 rounded-lg">
                      <h4 className="text-sm font-medium text-primary-content mb-2">Password Security</h4>
                      <p className="text-xs text-secondary-content mb-3">
                        Last changed: {securitySettings.lastPasswordChange.toLocaleDateString()}
                      </p>
                      <button className="px-4 py-2 bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 rounded-lg text-sm font-medium transition-colors">
                        Change Password
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* API Tab */}
          {activeTab === 'api' && (
            <div className="space-y-6">
              <div className="glass-card p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-primary-content">API Configuration</h3>
                  <Key className="h-5 w-5 text-secondary-content" />
                </div>
                
                <div className="space-y-6">
                  <div className="p-4 border border-yellow-500/30 bg-yellow-500/10 rounded-lg">
                    <div className="flex items-start space-x-3">
                      <AlertTriangle className="h-5 w-5 text-yellow-400 mt-0.5" />
                      <div>
                        <h4 className="text-sm font-medium text-primary-content mb-1">API Access Warning</h4>
                        <p className="text-xs text-secondary-content">
                          API access allows external applications to trade on your behalf. Only enable if you understand the risks.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg">
                    <div>
                      <h4 className="text-sm font-medium text-primary-content">Enable API Access</h4>
                      <p className="text-xs text-secondary-content">Allow external applications to access your account</p>
                    </div>
                    <button
                      onClick={() => setSecuritySettings(prev => ({ ...prev, apiAccess: !prev.apiAccess }))}
                      className={`w-10 h-6 rounded-full transition-colors relative ${
                        securitySettings.apiAccess ? 'bg-green-500' : 'bg-slate-600'
                      }`}
                    >
                      <div className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${
                        securitySettings.apiAccess ? 'translate-x-5' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>
                  
                  {securitySettings.apiAccess && (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-primary-content">API Key</label>
                        <div className="flex items-center space-x-2">
                          <input
                            type={showApiKey ? 'text' : 'password'}
                            value="pk_live_51234567890abcdef..."
                            readOnly
                            className="flex-1 px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none"
                          />
                          <button
                            onClick={() => setShowApiKey(!showApiKey)}
                            className="p-2 rounded-lg hover:bg-slate-700 transition-colors"
                          >
                            {showApiKey ? (
                              <EyeOff className="h-4 w-4 text-secondary-content" />
                            ) : (
                              <Eye className="h-4 w-4 text-secondary-content" />
                            )}
                          </button>
                          <button
                            onClick={handleGenerateApiKey}
                            className="px-3 py-2 bg-green-500/20 text-green-400 hover:bg-green-500/30 rounded-lg text-sm font-medium transition-colors"
                          >
                            Regenerate
                          </button>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-primary-content">IP Whitelist (optional)</label>
                        <textarea
                          value={securitySettings.ipWhitelist}
                          onChange={(e) => setSecuritySettings(prev => ({ ...prev, ipWhitelist: e.target.value }))}
                          placeholder="Enter IP addresses, one per line"
                          rows={3}
                          className="w-full px-3 py-2 bg-slate-800/60 border border-slate-600 rounded-lg text-primary-content focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        />
                        <p className="text-xs text-secondary-content">
                          Leave empty to allow access from any IP address
                        </p>
                      </div>
                      
                      <div className="p-4 bg-slate-800/50 rounded-lg">
                        <h4 className="text-sm font-medium text-primary-content mb-2">API Documentation</h4>
                        <p className="text-xs text-secondary-content mb-3">
                          View our comprehensive API documentation and examples
                        </p>
                        <button className="px-4 py-2 bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2">
                          <ExternalLink className="h-4 w-4" />
                          <span>View Documentation</span>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Appearance Tab */}
          {activeTab === 'appearance' && (
            <div className="space-y-6">
              <div className="glass-card p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-primary-content">Appearance Settings</h3>
                  <Monitor className="h-5 w-5 text-secondary-content" />
                </div>
                
                <div className="space-y-6">
                  <div>
                    <h4 className="text-md font-medium text-primary-content mb-4">Theme</h4>
                    <div className="grid grid-cols-3 gap-4">
                      {[
                        { id: 'light', label: 'Light', icon: Sun },
                        { id: 'dark', label: 'Dark', icon: Moon },
                        { id: 'system', label: 'System', icon: Monitor }
                      ].map((option) => (
                        <button
                          key={option.id}
                          onClick={() => setTheme(option.id)}
                          className={`p-4 rounded-lg border-2 transition-colors flex flex-col items-center space-y-2 ${
                            theme === option.id
                              ? 'border-green-500 bg-green-500/10'
                              : 'border-slate-600 bg-slate-800/30 hover:border-slate-500'
                          }`}
                        >
                          <option.icon className="h-6 w-6 text-secondary-content" />
                          <span className="text-sm font-medium text-primary-content">{option.label}</span>
                        </button>
                      ))}
                    </div>
                  </div>
                  
                  <div className="p-4 bg-slate-800/50 rounded-lg">
                    <h4 className="text-sm font-medium text-primary-content mb-2">Data Export</h4>
                    <p className="text-xs text-secondary-content mb-4">
                      Export your trading data and account settings
                    </p>
                    <div className="flex items-center space-x-3">
                      <button className="px-4 py-2 bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2">
                        <Download className="h-4 w-4" />
                        <span>Export Data</span>
                      </button>
                      <button className="px-4 py-2 bg-purple-500/20 text-purple-400 hover:bg-purple-500/30 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2">
                        <Upload className="h-4 w-4" />
                        <span>Import Settings</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Settings 
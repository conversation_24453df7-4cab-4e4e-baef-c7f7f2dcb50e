'use client'

import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase/client'
import { DashboardContent } from '@/components/dashboard/DashboardContent'
import { User } from '@supabase/supabase-js'
import { User as DatabaseUser } from '@/types/database'

export default function TestDashboardPage() {
  const [user, setUser] = useState<User | null>(null)
  const [userProfile, setUserProfile] = useState<DatabaseUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const testLogin = async () => {
      try {
        console.log('Testing direct authentication...')
        
        // Try to authenticate
        const { data, error: authError } = await supabase.auth.signInWithPassword({
          email: '<EMAIL>',
          password: 'AdminPass123'
        })

        if (authError) {
          setError(`Authentication failed: ${authError.message}`)
          return
        }

        if (!data.user) {
          setError('No user data received')
          return
        }

        console.log('Authentication successful:', data.user)
        setUser(data.user)

        // Fetch user profile
        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('*')
          .eq('id', data.user.id)
          .single()

        if (profileError) {
          console.error('Profile fetch error:', profileError)
          setError(`Profile fetch failed: ${profileError.message}`)
          return
        }

        console.log('Profile fetched:', profile)
        setUserProfile(profile)

      } catch (err) {
        console.error('Test login error:', err)
        setError(`Unexpected error: ${err instanceof Error ? err.message : 'Unknown error'}`)
      } finally {
        setLoading(false)
      }
    }

    testLogin()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center">
        <div className="text-white text-xl">Testing authentication...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center">
        <div className="text-red-400 text-xl">Error: {error}</div>
      </div>
    )
  }

  if (!user || !userProfile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center">
        <div className="text-yellow-400 text-xl">No user data available</div>
      </div>
    )
  }

  return (
    <DashboardContent 
      user={{
        id: user.id,
        email: user.email || '',
        user_metadata: user.user_metadata
      }}
    />
  )
} 
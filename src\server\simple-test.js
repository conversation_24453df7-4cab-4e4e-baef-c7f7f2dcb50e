// Simple test server to verify environment variables
const express = require('express')
const dotenv = require('dotenv')
const path = require('path')

// Load environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') })
dotenv.config()

const app = express()
const PORT = process.env.API_PORT || 8000

// Middleware
app.use(express.json())

// Health check
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Simple server is working!',
    environment: {
      NODE_ENV: process.env.NODE_ENV,
      SUPABASE_URL_SET: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      SUPABASE_ANON_KEY_SET: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      SUPABASE_SERVICE_KEY_SET: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      JWT_SECRET_SET: !!process.env.JWT_SECRET,
      API_PORT: process.env.API_PORT
    },
    timestamp: new Date().toISOString()
  })
})

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'API endpoint working',
    timestamp: new Date().toISOString()
  })
})

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Simple test server running on port ${PORT}`)
  console.log(`🔗 Health check: http://localhost:${PORT}/health`)
  console.log(`🔗 API test: http://localhost:${PORT}/api/test`)
  console.log('✅ Environment variables loaded!')
}) 
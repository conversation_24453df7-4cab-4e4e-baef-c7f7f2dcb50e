'use client'

import { useState } from 'react'
import { apiClient, type ApiResponse } from '@/lib/api/client'

interface TestResults {
  [key: string]: ApiResponse
}

export default function TestApiPage() {
  const [results, setResults] = useState<TestResults>({})
  const [loading, setLoading] = useState<string | null>(null)

  const runTest = async (testName: string, testFn: () => Promise<ApiResponse>) => {
    setLoading(testName)
    try {
      const result = await testFn()
      setResults((prev: TestResults) => ({ ...prev, [testName]: result }))
    } catch (error) {
      setResults((prev: TestResults) => ({ 
        ...prev, 
        [testName]: { 
          success: false, 
          error: { 
            code: 'UNKNOWN_ERROR',
            message: error instanceof Error ? error.message : 'Unknown error occurred'
          } 
        } 
      }))
    }
    setLoading(null)
  }

  const tests = [
    { name: 'Health Check', fn: () => apiClient.healthCheck() },
    { name: 'API Test', fn: () => apiClient.testApi() },
    { name: 'Database Test', fn: () => apiClient.testDatabase() },
    { name: 'Analytics Test', fn: () => apiClient.testAnalytics() },
    { name: 'Risk Test', fn: () => apiClient.testRisk() },
    { name: 'Users Test', fn: () => apiClient.testUsers() },
    { name: 'Support Test', fn: () => apiClient.testSupport() },
  ]

  const runAllTests = async () => {
    for (const test of tests) {
      await runTest(test.name, test.fn)
    }
  }

  return (
    <div className="min-h-screen bg-slate-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">Backend API Test</h1>
        
        <div className="mb-6">
          <button
            onClick={runAllTests}
            disabled={loading !== null}
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold disabled:opacity-50"
          >
            {loading ? 'Running Tests...' : 'Run All Tests'}
          </button>
        </div>

        <div className="grid gap-4">
          {tests.map((test) => (
            <div key={test.name} className="bg-slate-800 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-white">{test.name}</h3>
                <button
                  onClick={() => runTest(test.name, test.fn)}
                  disabled={loading === test.name}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded disabled:opacity-50"
                >
                  {loading === test.name ? 'Testing...' : 'Test'}
                </button>
              </div>
              
              {results[test.name] && (
                <div className="bg-slate-700 rounded p-4">
                  <div className={`text-sm font-semibold mb-2 ${
                    results[test.name].success ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {results[test.name].success ? '✅ SUCCESS' : '❌ FAILED'}
                  </div>
                  <pre className="text-gray-300 text-sm overflow-auto">
                    {JSON.stringify(results[test.name], null, 2)}
                  </pre>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
} 
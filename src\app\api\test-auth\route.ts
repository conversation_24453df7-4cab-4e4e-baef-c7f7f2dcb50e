import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase/client'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()
    
    console.log('Testing auth with:', { email })
    
    // Try to authenticate with provided credentials
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    if (error) {
      console.error('Auth error:', error)
      return NextResponse.json({
        success: false,
        error: error.message,
        details: error
      })
    }
    
    if (data.user && data.session) {
      return NextResponse.json({
        success: true,
        message: 'Authentication successful',
        user: {
          id: data.user.id,
          email: data.user.email,
          confirmed_at: data.user.email_confirmed_at
        },
        session: {
          access_token: data.session.access_token ? 'present' : 'missing',
          refresh_token: data.session.refresh_token ? 'present' : 'missing'
        }
      })
    }
    
    return NextResponse.json({
      success: false,
      error: 'No user or session returned'
    })
    
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({
      success: false,
      error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
    }, { status: 500 })
  }
} 
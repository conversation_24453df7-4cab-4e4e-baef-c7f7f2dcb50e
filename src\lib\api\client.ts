/**
 * Centralized API client for frontend-backend communication
 * Handles all HTTP requests to our backend server on port 8000
 */

interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
}

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

/**
 * Get authentication token from localStorage
 */
function getAuthToken(): string | null {
  if (typeof window === 'undefined') return null
  return localStorage.getItem('auth_token')
}

/**
 * Generic API fetch wrapper with error handling
 */
async function apiRequest<T = any>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const url = `${API_BASE_URL}${endpoint}`
    
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string> || {}),
    }

    // Add authentication token if available
    const token = getAuthToken()
    if (token) {
      defaultHeaders['Authorization'] = `Bearer ${token}`
    }

    const response = await fetch(url, {
      ...options,
      headers: defaultHeaders,
    })

    const data = await response.json()
    
    if (!response.ok) {
      return {
        success: false,
        error: {
          code: `HTTP_${response.status}`,
          message: data.error?.message || `Request failed with status ${response.status}`,
          details: data.error?.details
        }
      }
    }

    return data
  } catch (error) {
    console.error('API request failed:', error)
    return {
      success: false,
      error: {
        code: 'NETWORK_ERROR',
        message: 'Failed to connect to backend server',
        details: error
      }
    }
  }
}

/**
 * API Client Class
 */
class ApiClient {
  private baseUrl: string

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl
  }

  // Health check
  async healthCheck() {
    return apiRequest('/health')
  }

  // Test endpoints
  async testApi() {
    return apiRequest('/api/test')
  }

  async testDatabase() {
    return apiRequest('/test-db')
  }

  // ========================================
  // ANALYTICS API ENDPOINTS
  // ========================================

  /**
   * Get performance metrics for an account
   */
  async getPerformanceMetrics(accountId: string, timeframe?: string) {
    const params = new URLSearchParams()
    if (timeframe) params.append('timeframe', timeframe)
    
    const endpoint = `/api/analytics/performance/${accountId}${params.toString() ? `?${params.toString()}` : ''}`
    return apiRequest(endpoint)
  }

  /**
   * Get equity curve data for charting
   */
  async getEquityCurve(accountId: string, timeframe?: string) {
    const params = new URLSearchParams()
    if (timeframe) params.append('timeframe', timeframe)
    
    const endpoint = `/api/analytics/equity-curve/${accountId}${params.toString() ? `?${params.toString()}` : ''}`
    return apiRequest(endpoint)
  }

  /**
   * Get daily P&L analysis
   */
  async getDailyPnL(accountId: string, timeframe?: string) {
    const params = new URLSearchParams()
    if (timeframe) params.append('timeframe', timeframe)
    
    const endpoint = `/api/analytics/daily-pnl/${accountId}${params.toString() ? `?${params.toString()}` : ''}`
    return apiRequest(endpoint)
  }

  /**
   * Get trading hours analysis
   */
  async getTradingHoursAnalysis(accountId: string) {
    return apiRequest(`/api/analytics/trading-hours/${accountId}`)
  }

  /**
   * Get symbol performance breakdown
   */
  async getSymbolBreakdown(accountId: string) {
    return apiRequest(`/api/analytics/symbol-breakdown/${accountId}`)
  }

  // Test Analytics endpoint
  async testAnalytics() {
    return apiRequest('/api/analytics/test')
  }

  // ========================================
  // RISK MANAGEMENT API ENDPOINTS
  // ========================================

  /**
   * Get current risk metrics for an account
   */
  async getCurrentRiskMetrics(accountId: string) {
    return apiRequest(`/api/risk/current-metrics/${accountId}`)
  }

  /**
   * Get risk rules for an account
   */
  async getRiskRules(accountId: string) {
    return apiRequest(`/api/risk/rules/${accountId}`)
  }

  /**
   * Update a risk rule
   */
  async updateRiskRule(ruleId: string, updates: { value?: number; enabled?: boolean; priority?: number }) {
    return apiRequest(`/api/risk/rules/${ruleId}`, {
      method: 'PUT',
      body: JSON.stringify(updates)
    })
  }

  /**
   * Get risk alerts for an account
   */
  async getRiskAlerts(accountId: string) {
    return apiRequest(`/api/risk/alerts/${accountId}`)
  }

  // Test Risk endpoint
  async testRisk() {
    return apiRequest('/api/risk/test')
  }

  // ========================================
  // USER SETTINGS API ENDPOINTS
  // ========================================

  /**
   * Get user profile
   */
  async getUserProfile() {
    return apiRequest('/api/users/profile')
  }

  /**
   * Update user profile
   */
  async updateUserProfile(updates: { firstName?: string; lastName?: string; phone?: string; country?: string; timezone?: string; language?: string }) {
    return apiRequest('/api/users/profile', {
      method: 'PUT',
      body: JSON.stringify(updates)
    })
  }

  /**
   * Get trading preferences
   */
  async getTradingPreferences() {
    return apiRequest('/api/users/trading-preferences')
  }

  /**
   * Update trading preferences
   */
  async updateTradingPreferences(preferences: any) {
    return apiRequest('/api/users/trading-preferences', {
      method: 'PUT',
      body: JSON.stringify(preferences)
    })
  }

  /**
   * Get notification settings
   */
  async getNotificationSettings() {
    return apiRequest('/api/users/notification-settings')
  }

  /**
   * Update notification settings
   */
  async updateNotificationSettings(settings: any) {
    return apiRequest('/api/users/notification-settings', {
      method: 'PUT',
      body: JSON.stringify(settings)
    })
  }

  // Test Users endpoint
  async testUsers() {
    return apiRequest('/api/users/test')
  }

  // ========================================
  // SUPPORT API ENDPOINTS  
  // ========================================

  /**
   * Get FAQ items
   */
  async getFAQ(category?: string, search?: string) {
    const params = new URLSearchParams()
    if (category) params.append('category', category)
    if (search) params.append('search', search)
    
    const endpoint = `/api/support/faq${params.toString() ? `?${params.toString()}` : ''}`
    return apiRequest(endpoint)
  }

  /**
   * Get support tickets
   */
  async getSupportTickets() {
    return apiRequest('/api/support/tickets')
  }

  /**
   * Create a support ticket
   */
  async createSupportTicket(ticket: { subject: string; category: string; priority: string; description: string }) {
    return apiRequest('/api/support/tickets', {
      method: 'POST',
      body: JSON.stringify(ticket)
    })
  }

  // Test Support endpoint
  async testSupport() {
    return apiRequest('/api/support/test')
  }
}

export const apiClient = new ApiClient()
export type { ApiResponse } 
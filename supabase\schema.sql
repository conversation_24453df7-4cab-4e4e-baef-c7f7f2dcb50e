-- PropFirm Database Schema
-- Comprehensive schema for proprietary trading firm platform

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  phone TEXT,
  country TEXT NOT NULL,
  date_of_birth DATE NOT NULL,
  kyc_status TEXT DEFAULT 'pending' CHECK (kyc_status IN ('pending', 'verified', 'rejected', 'expired')),
  account_status TEXT DEFAULT 'pending' CHECK (account_status IN ('active', 'suspended', 'closed', 'pending')),
  preferred_language TEXT DEFAULT 'en',
  timezone TEXT DEFAULT 'UTC',
  avatar_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Challenges table
CREATE TABLE public.challenges (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  target_profit DECIMAL(10,2) NOT NULL,
  max_daily_loss DECIMAL(10,2) NOT NULL,
  max_total_loss DECIMAL(10,2) NOT NULL,
  trading_period INTEGER NOT NULL, -- days
  profit_share DECIMAL(5,2) NOT NULL, -- percentage
  min_trading_days INTEGER NOT NULL,
  max_position_size DECIMAL(10,2) NOT NULL,
  allowed_instruments JSONB DEFAULT '[]',
  rules JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Trading accounts table
CREATE TABLE public.trading_accounts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  challenge_id UUID NOT NULL REFERENCES public.challenges(id) ON DELETE CASCADE,
  account_number TEXT UNIQUE NOT NULL,
  balance DECIMAL(15,2) NOT NULL DEFAULT 0,
  equity DECIMAL(15,2) NOT NULL DEFAULT 0,
  free_margin DECIMAL(15,2) NOT NULL DEFAULT 0,
  margin_level DECIMAL(8,2) NOT NULL DEFAULT 0,
  drawdown DECIMAL(8,2) NOT NULL DEFAULT 0,
  max_drawdown DECIMAL(8,2) NOT NULL DEFAULT 0,
  daily_loss DECIMAL(15,2) NOT NULL DEFAULT 0,
  total_loss DECIMAL(15,2) NOT NULL DEFAULT 0,
  profit DECIMAL(15,2) NOT NULL DEFAULT 0,
  phase TEXT DEFAULT 'evaluation' CHECK (phase IN ('evaluation', 'verification', 'funded', 'failed')),
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'paused', 'closed', 'violation')),
  mt5_login TEXT,
  mt5_password TEXT,
  mt5_server TEXT,
  start_date TIMESTAMPTZ DEFAULT NOW(),
  end_date TIMESTAMPTZ,
  last_trade_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Trades table
CREATE TABLE public.trades (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  account_id UUID NOT NULL REFERENCES public.trading_accounts(id) ON DELETE CASCADE,
  symbol TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('buy', 'sell', 'buy_limit', 'sell_limit', 'buy_stop', 'sell_stop')),
  volume DECIMAL(10,2) NOT NULL,
  open_price DECIMAL(15,5) NOT NULL,
  close_price DECIMAL(15,5),
  stop_loss DECIMAL(15,5),
  take_profit DECIMAL(15,5),
  profit DECIMAL(15,2),
  commission DECIMAL(15,2) DEFAULT 0,
  swap DECIMAL(15,2) DEFAULT 0,
  open_time TIMESTAMPTZ NOT NULL,
  close_time TIMESTAMPTZ,
  status TEXT DEFAULT 'open' CHECK (status IN ('open', 'closed', 'pending', 'cancelled')),
  comment TEXT,
  magic_number INTEGER,
  mt5_ticket BIGINT,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Payments table
CREATE TABLE public.payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  challenge_id UUID REFERENCES public.challenges(id),
  account_id UUID REFERENCES public.trading_accounts(id),
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'USD' NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'succeeded', 'failed', 'cancelled', 'refunded')),
  payment_method TEXT CHECK (payment_method IN ('card', 'bank_transfer', 'crypto', 'paypal')),
  stripe_payment_intent_id TEXT,
  stripe_charge_id TEXT,
  description TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  processed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Risk rules table
CREATE TABLE public.risk_rules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('max_daily_loss', 'max_total_loss', 'max_position_size', 'max_positions', 'trading_hours', 'forbidden_instruments')),
  value DECIMAL(15,2) NOT NULL,
  enabled BOOLEAN DEFAULT true,
  priority INTEGER DEFAULT 0,
  challenge_id UUID REFERENCES public.challenges(id),
  account_id UUID REFERENCES public.trading_accounts(id),
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Notifications table
CREATE TABLE public.notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'error', 'success', 'trade', 'payment')),
  is_read BOOLEAN DEFAULT false,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Audit logs table
CREATE TABLE public.audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.users(id),
  action TEXT NOT NULL,
  resource_type TEXT NOT NULL,
  resource_id UUID,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Market data table (for caching live data)
CREATE TABLE public.market_data (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  symbol TEXT NOT NULL,
  bid DECIMAL(15,5) NOT NULL,
  ask DECIMAL(15,5) NOT NULL,
  last DECIMAL(15,5) NOT NULL,
  volume BIGINT DEFAULT 0,
  high DECIMAL(15,5) NOT NULL,
  low DECIMAL(15,5) NOT NULL,
  change_value DECIMAL(15,5) DEFAULT 0,
  change_percent DECIMAL(8,4) DEFAULT 0,
  timestamp TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Trading sessions table
CREATE TABLE public.trading_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  account_id UUID NOT NULL REFERENCES public.trading_accounts(id) ON DELETE CASCADE,
  start_time TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  end_time TIMESTAMPTZ,
  start_balance DECIMAL(15,2) NOT NULL,
  end_balance DECIMAL(15,2),
  profit DECIMAL(15,2),
  trades_count INTEGER DEFAULT 0,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed')),
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Performance metrics table
CREATE TABLE public.performance_metrics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  account_id UUID NOT NULL REFERENCES public.trading_accounts(id) ON DELETE CASCADE,
  period_start TIMESTAMPTZ NOT NULL,
  period_end TIMESTAMPTZ NOT NULL,
  total_trades INTEGER DEFAULT 0,
  winning_trades INTEGER DEFAULT 0,
  losing_trades INTEGER DEFAULT 0,
  win_rate DECIMAL(5,2) DEFAULT 0,
  avg_win DECIMAL(15,2) DEFAULT 0,
  avg_loss DECIMAL(15,2) DEFAULT 0,
  profit_factor DECIMAL(8,4) DEFAULT 0,
  sharpe_ratio DECIMAL(8,4) DEFAULT 0,
  max_drawdown DECIMAL(8,2) DEFAULT 0,
  return_on_investment DECIMAL(8,4) DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Indexes for performance
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_account_status ON public.users(account_status);
CREATE INDEX idx_trading_accounts_user_id ON public.trading_accounts(user_id);
CREATE INDEX idx_trading_accounts_phase ON public.trading_accounts(phase);
CREATE INDEX idx_trading_accounts_status ON public.trading_accounts(status);
CREATE INDEX idx_trades_account_id ON public.trades(account_id);
CREATE INDEX idx_trades_symbol ON public.trades(symbol);
CREATE INDEX idx_trades_open_time ON public.trades(open_time);
CREATE INDEX idx_trades_status ON public.trades(status);
CREATE INDEX idx_payments_user_id ON public.payments(user_id);
CREATE INDEX idx_payments_status ON public.payments(status);
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_is_read ON public.notifications(is_read);
CREATE INDEX idx_market_data_symbol ON public.market_data(symbol);
CREATE INDEX idx_market_data_timestamp ON public.market_data(timestamp);
CREATE INDEX idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON public.audit_logs(created_at);

-- Functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_challenges_updated_at BEFORE UPDATE ON public.challenges FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_trading_accounts_updated_at BEFORE UPDATE ON public.trading_accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_trades_updated_at BEFORE UPDATE ON public.trades FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON public.payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_risk_rules_updated_at BEFORE UPDATE ON public.risk_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_trading_sessions_updated_at BEFORE UPDATE ON public.trading_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_performance_metrics_updated_at BEFORE UPDATE ON public.performance_metrics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trading_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trades ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trading_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.performance_metrics ENABLE ROW LEVEL SECURITY;

-- Users can only see/edit their own data
CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id);

-- Trading accounts policies
CREATE POLICY "Users can view own trading accounts" ON public.trading_accounts FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own trading accounts" ON public.trading_accounts FOR UPDATE USING (auth.uid() = user_id);

-- Trades policies
CREATE POLICY "Users can view own trades" ON public.trades FOR SELECT USING (
  EXISTS (SELECT 1 FROM public.trading_accounts WHERE id = trades.account_id AND user_id = auth.uid())
);

-- Payments policies
CREATE POLICY "Users can view own payments" ON public.payments FOR SELECT USING (auth.uid() = user_id);

-- Notifications policies
CREATE POLICY "Users can view own notifications" ON public.notifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own notifications" ON public.notifications FOR UPDATE USING (auth.uid() = user_id);

-- Public tables (no RLS needed)
-- challenges, risk_rules, market_data, audit_logs

-- Insert sample challenges
INSERT INTO public.challenges (name, description, price, target_profit, max_daily_loss, max_total_loss, trading_period, profit_share, min_trading_days, max_position_size, allowed_instruments) VALUES
('$10K Evaluation', 'Starter challenge for new traders', 99.00, 800.00, 500.00, 1000.00, 30, 80.00, 5, 1000.00, '["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD"]'),
('$25K Challenge', 'Intermediate challenge with higher capital', 179.00, 2000.00, 1250.00, 2500.00, 30, 80.00, 5, 2500.00, '["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "GOLD", "US30"]'),
('$50K Professional', 'Advanced challenge for experienced traders', 299.00, 4000.00, 2500.00, 5000.00, 30, 85.00, 5, 5000.00, '["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "GOLD", "US30", "NAS100", "SPX500"]'),
('$100K Elite', 'Elite challenge with maximum capital', 499.00, 8000.00, 5000.00, 10000.00, 30, 90.00, 5, 10000.00, '["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "GOLD", "US30", "NAS100", "SPX500", "BTCUSD", "ETHUSD"]');

-- Insert sample risk rules
INSERT INTO public.risk_rules (name, description, type, value, enabled, priority) VALUES
('Daily Loss Limit', 'Maximum daily loss allowed', 'max_daily_loss', 500.00, true, 1),
('Total Loss Limit', 'Maximum total loss allowed', 'max_total_loss', 1000.00, true, 1),
('Position Size Limit', 'Maximum position size allowed', 'max_position_size', 1000.00, true, 2),
('Max Open Positions', 'Maximum number of open positions', 'max_positions', 10.00, true, 2),
('Weekend Trading', 'No trading during weekends', 'trading_hours', 0.00, true, 3);

-- Insert sample market data
INSERT INTO public.market_data (symbol, bid, ask, last, volume, high, low, change_value, change_percent) VALUES
('EURUSD', 1.08450, 1.08453, 1.08451, 1250000, 1.08520, 1.08380, 0.0023, 0.21),
('GBPUSD', 1.26340, 1.26343, 1.26341, 980000, 1.26450, 1.26280, -0.0012, -0.09),
('USDJPY', 149.320, 149.325, 149.322, 1100000, 149.580, 149.150, 0.45, 0.30),
('GOLD', 2031.45, 2031.85, 2031.65, 750000, 2035.20, 2028.30, 12.30, 0.61),
('BTCUSD', 43247.00, 43252.00, 43249.50, 2500000, 44100.00, 42800.00, 1205.00, 2.86),
('US30', 37892.45, 37895.20, 37893.80, 180000, 38120.50, 37780.20, -23.45, -0.06); 
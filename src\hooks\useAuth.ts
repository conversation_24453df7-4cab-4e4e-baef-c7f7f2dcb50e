'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase/client'
import { User as DatabaseUser } from '@/types/database'

interface AuthState {
  user: User | null
  session: Session | null
  userProfile: DatabaseUser | null
  loading: boolean
  error: string | null
}

interface AuthActions {
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
  signUp: (email: string, password: string, metadata?: any) => Promise<{ success: boolean; error?: string }>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<{ success: boolean; error?: string }>
  updateProfile: (updates: Partial<DatabaseUser>) => Promise<{ success: boolean; error?: string }>
  refreshSession: () => Promise<void>
}

export function useAuth(): AuthState & AuthActions {
  const router = useRouter()
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    userProfile: null,
    loading: true,
    error: null
  })

  // Initialize auth state and listen for changes
  useEffect(() => {
    let mounted = true

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Error getting session:', error)
          if (mounted) {
            setAuthState(prev => ({ 
              ...prev, 
              loading: false, 
              error: error.message 
            }))
          }
          return
        }

        if (mounted) {
          setAuthState(prev => ({
            ...prev,
            session,
            user: session?.user || null,
            loading: false
          }))

          // Fetch user profile if user exists
          if (session?.user) {
            await fetchUserProfile(session.user.id)
          }
        }
      } catch (error) {
        console.error('Unexpected error getting session:', error)
        if (mounted) {
          setAuthState(prev => ({ 
            ...prev, 
            loading: false, 
            error: 'Failed to load session' 
          }))
        }
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return

        setAuthState(prev => ({
          ...prev,
          session,
          user: session?.user || null,
          loading: false
        }))

        // Fetch user profile for new sessions
        if (session?.user && event === 'SIGNED_IN') {
          await fetchUserProfile(session.user.id)
        }

        // Clear user profile on sign out
        if (event === 'SIGNED_OUT') {
          setAuthState(prev => ({
            ...prev,
            userProfile: null
          }))
        }
      }
    )

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [])

  // Fetch user profile from database
  const fetchUserProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching user profile:', error)
        return
      }

      setAuthState(prev => ({
        ...prev,
        userProfile: data
      }))
    } catch (error) {
      console.error('Unexpected error fetching user profile:', error)
    }
  }

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }))

      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        setAuthState(prev => ({ ...prev, loading: false, error: error.message }))
        return { success: false, error: error.message }
      }

      return { success: true }
    } catch (error) {
      const errorMessage = 'An unexpected error occurred during sign in'
      setAuthState(prev => ({ ...prev, loading: false, error: errorMessage }))
      return { success: false, error: errorMessage }
    }
  }

  // Sign up with email and password
  const signUp = async (email: string, password: string, metadata?: any) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }))

      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: { data: metadata }
      })

      if (error) {
        setAuthState(prev => ({ ...prev, loading: false, error: error.message }))
        return { success: false, error: error.message }
      }

      return { success: true }
    } catch (error) {
      const errorMessage = 'An unexpected error occurred during sign up'
      setAuthState(prev => ({ ...prev, loading: false, error: errorMessage }))
      return { success: false, error: errorMessage }
    }
  }

  // Sign out
  const signOut = async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }))
      
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        console.error('Error signing out:', error)
      }

      // Clear local state
      setAuthState({
        user: null,
        session: null,
        userProfile: null,
        loading: false,
        error: null
      })

      // Redirect to home page
      router.push('/')
      router.refresh()
    } catch (error) {
      console.error('Unexpected error signing out:', error)
      setAuthState(prev => ({ ...prev, loading: false }))
    }
  }

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      })

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true }
    } catch (error) {
      return { success: false, error: 'Failed to send reset email' }
    }
  }

  // Update user profile
  const updateProfile = async (updates: Partial<DatabaseUser>) => {
    if (!authState.user) {
      return { success: false, error: 'No authenticated user' }
    }

    try {
      const { error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', authState.user.id)

      if (error) {
        return { success: false, error: error.message }
      }

      // Refresh user profile
      await fetchUserProfile(authState.user.id)
      
      return { success: true }
    } catch (error) {
      return { success: false, error: 'Failed to update profile' }
    }
  }

  // Refresh session
  const refreshSession = async () => {
    try {
      const { error } = await supabase.auth.refreshSession()
      if (error) {
        console.error('Error refreshing session:', error)
      }
    } catch (error) {
      console.error('Unexpected error refreshing session:', error)
    }
  }

  return {
    ...authState,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateProfile,
    refreshSession
  }
} 
// Simple API testing script
const API_BASE = 'http://localhost:8000'

const testEndpoints = [
  '/health',
  '/test-db', 
  '/api/test',
  '/api/analytics/test',
  '/api/risk/test',
  '/api/users/test',
  '/api/support/test'
]

async function testEndpoint(endpoint) {
  try {
    const response = await fetch(`${API_BASE}${endpoint}`)
    const data = await response.json()
    
    console.log(`✅ ${endpoint}:`, response.status, data.success ? '✓' : '✗')
    if (data.data?.message) console.log(`   📝 ${data.data.message}`)
    if (data.error) console.log(`   ❌ ${data.error.message}`)
    
    return { endpoint, status: response.status, success: data.success }
  } catch (error) {
    console.log(`❌ ${endpoint}: ERROR -`, error.message)
    return { endpoint, status: 'ERROR', success: false, error: error.message }
  }
}

async function runTests() {
  console.log('🧪 Testing Prop Firm Backend API Endpoints')
  console.log('===========================================')
  
  const results = []
  
  for (const endpoint of testEndpoints) {
    const result = await testEndpoint(endpoint)
    results.push(result)
    await new Promise(resolve => setTimeout(resolve, 100)) // Small delay
  }
  
  console.log('\n📊 Test Summary:')
  console.log('================')
  
  const successful = results.filter(r => r.success && r.status === 200)
  const failed = results.filter(r => !r.success || r.status !== 200)
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`)
  console.log(`❌ Failed: ${failed.length}/${results.length}`)
  
  if (failed.length > 0) {
    console.log('\n❌ Failed endpoints:')
    failed.forEach(f => console.log(`   - ${f.endpoint}: ${f.status} ${f.error || ''}`))
  }
  
  if (successful.length === results.length) {
    console.log('\n🎉 All endpoints working! Backend is ready for frontend integration.')
  } else {
    console.log('\n⚠️  Some endpoints failed. Check the server logs.')
  }
}

// Run the tests
runTests().catch(console.error) 
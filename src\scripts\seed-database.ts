import { supabase } from '../lib/supabase/server'

async function seedDatabase() {
  console.log('🌱 Starting database seeding...')

  try {
    // First, check if challenges exist (from schema.sql)
    console.log('Checking existing challenges...')
    const { data: existingChallenges, error: challengesCheckError } = await supabase
      .from('challenges')
      .select('id, name')
      .limit(5)

    if (challengesCheckError) {
      console.error('Error checking challenges:', challengesCheckError)
      throw challengesCheckError
    }

    console.log(`Found ${existingChallenges?.length || 0} existing challenges`)
    
    if (!existingChallenges || existingChallenges.length === 0) {
      console.log('⚠️  No challenges found. Please ensure the schema.sql has been applied to your Supabase database.')
      console.log('📋 Go to Supabase Dashboard > SQL Editor and run the contents of supabase/schema.sql')
      return
    }

    // Get the first challenge ID for our sample data
    const challengeId = existingChallenges[0].id

    // Check if we have any existing authenticated users to work with
    console.log('Checking for existing users...')
    const { data: existingUsers, error: usersCheckError } = await supabase
      .from('users')
      .select('id, email, first_name, last_name')
      .limit(5)

    if (usersCheckError) {
      console.warn('Users table check error (this is normal if no users exist yet):', usersCheckError.message)
    }

    let userId: string
    
    if (existingUsers && existingUsers.length > 0) {
      // Use an existing user
      userId = existingUsers[0].id
      console.log(`✅ Using existing user: ${existingUsers[0].email}`)
    } else {
      console.log('⚠️  No existing users found.')
      console.log('To seed with sample data, you need to:')
      console.log('1. Register a user through the frontend (http://localhost:3000/register)')
      console.log('2. Or use Supabase Dashboard to create a test user')
      console.log('3. Then run this seeding script again')
      console.log('')
      console.log('For now, I\'ll create basic reference data that doesn\'t require users...')
      
      // Create sample risk rules that don't require users
      await createSampleRiskRules()
      await updateMarketData()
      return
    }

    // Now create trading account for the existing user
    console.log('Creating sample trading account...')
    const accountNumber = `DEMO_${Date.now()}`
    const { data: newAccount, error: accountError } = await supabase
      .from('trading_accounts')
      .upsert([
        {
          user_id: userId,
          challenge_id: challengeId,
          account_number: accountNumber,
          balance: 10000.00,
          equity: 10000.00,
          free_margin: 10000.00,
          margin_level: 100.00,
          drawdown: 0.00,
          max_drawdown: 0.00,
          daily_loss: 0.00,
          total_loss: 0.00,
          profit: 0.00,
          phase: 'evaluation',
          status: 'active',
          mt5_login: `DEMO_${Date.now()}`,
          mt5_password: 'DemoPass123',
          mt5_server: 'demo.propfirm.com:443',
          start_date: new Date().toISOString()
        }
      ], { 
        onConflict: 'account_number'
      })
      .select()

    if (accountError) {
      console.error('Error creating trading account:', accountError)
      throw accountError
    }

    const accountId = newAccount?.[0]?.id
    if (!accountId) {
      console.error('Failed to get account ID')
      return
    }

    console.log('✅ Sample trading account created')

    // Create sample trades
    console.log('Creating sample trades...')
    const { error: tradesError } = await supabase
      .from('trades')
      .upsert([
        {
          account_id: accountId,
          symbol: 'EURUSD',
          type: 'buy',
          volume: 0.10,
          open_price: 1.12340,
          close_price: 1.12500,
          stop_loss: 1.12000,
          take_profit: 1.13000,
          profit: 16.00,
          commission: -0.80,
          swap: 0.00,
          status: 'closed',
          open_time: new Date(Date.now() - ********).toISOString(), // 1 day ago
          close_time: new Date(Date.now() - ********).toISOString(), // 12 hours ago
          comment: 'Sample closed trade',
          magic_number: 12345,
          mt5_ticket: *********
        },
        {
          account_id: accountId,
          symbol: 'GBPUSD',
          type: 'sell',
          volume: 0.05,
          open_price: 1.25000,
          stop_loss: 1.25500,
          take_profit: 1.24500,
          profit: 0.00,
          commission: -0.40,
          swap: 0.00,
          status: 'open',
          open_time: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
          comment: 'Current open position',
          magic_number: 12346,
          mt5_ticket: *********
        }
      ])

    if (tradesError) {
      console.error('Error creating trades:', tradesError)
    } else {
      console.log('✅ Sample trades created')
    }

    // Create sample notifications
    console.log('Creating sample notifications...')
    const { error: notificationsError } = await supabase
      .from('notifications')
      .insert([
        {
          user_id: userId,
          title: 'Welcome to PropFirm!',
          message: 'Your account has been activated successfully. Start trading now!',
          type: 'success',
          is_read: false,
          metadata: { source: 'system' }
        },
        {
          user_id: userId,
          title: 'Trade Alert',
          message: 'Your EURUSD position has been closed with a profit of $16.00',
          type: 'trade',
          is_read: false,
          metadata: { trade_id: accountId, profit: 16.00 }
        }
      ])

    if (notificationsError) {
      console.error('Error creating notifications:', notificationsError)
    } else {
      console.log('✅ Sample notifications created')
    }

    // Create sample payments
    console.log('Creating sample payments...')
    const { error: paymentsError } = await supabase
      .from('payments')
      .insert([
        {
          user_id: userId,
          challenge_id: challengeId,
          amount: 99.00,
          currency: 'USD',
          status: 'succeeded',
          payment_method: 'card',
          description: 'Challenge Purchase',
          metadata: { challenge_name: 'Starter Challenge' },
          processed_at: new Date().toISOString()
        }
      ])

    if (paymentsError) {
      console.error('Error creating payments:', paymentsError)
    } else {
      console.log('✅ Sample payments created')
    }

    // Create additional reference data
    await createSampleRiskRules()
    await updateMarketData()

    console.log('🎉 Database seeding completed successfully!')

  } catch (error) {
    console.error('❌ Database seeding failed:', error)
    throw error
  }
}

async function createSampleRiskRules() {
  console.log('Creating/updating sample risk rules...')
  
  const { error: riskRulesError } = await supabase
    .from('risk_rules')
    .insert([
      {
        name: 'Daily Loss Limit - Demo',
        description: 'Maximum daily loss for demo accounts',
        type: 'max_daily_loss',
        value: 500.00,
        enabled: true,
        priority: 1
      },
      {
        name: 'Total Loss Limit - Demo',
        description: 'Maximum total loss for demo accounts',
        type: 'max_total_loss',
        value: 1000.00,
        enabled: true,
        priority: 1
      },
      {
        name: 'Position Size Limit - Demo',
        description: 'Maximum position size for demo accounts',
        type: 'max_position_size',
        value: 1000.00,
        enabled: true,
        priority: 2
      }
    ])

  if (riskRulesError) {
    console.warn('Risk rules may already exist:', riskRulesError.message)
  } else {
    console.log('✅ Sample risk rules created/updated')
  }
}

async function updateMarketData() {
  console.log('Updating sample market data...')
  
  // Delete existing market data first, then insert fresh data
  await supabase.from('market_data').delete().neq('id', '********-0000-0000-0000-************')
  
  const { error: marketDataError } = await supabase
    .from('market_data')
    .insert([
      {
        symbol: 'EURUSD',
        bid: 1.08450 + (Math.random() - 0.5) * 0.001,
        ask: 1.08453 + (Math.random() - 0.5) * 0.001,
        last: 1.08451 + (Math.random() - 0.5) * 0.001,
        volume: Math.floor(Math.random() * 1000000) + 1000000,
        high: 1.08520,
        low: 1.08380,
        change_value: (Math.random() - 0.5) * 0.01,
        change_percent: (Math.random() - 0.5) * 2,
        timestamp: new Date().toISOString()
      },
      {
        symbol: 'GBPUSD',
        bid: 1.26340 + (Math.random() - 0.5) * 0.001,
        ask: 1.26343 + (Math.random() - 0.5) * 0.001,
        last: 1.26341 + (Math.random() - 0.5) * 0.001,
        volume: Math.floor(Math.random() * 800000) + 800000,
        high: 1.26450,
        low: 1.26280,
        change_value: (Math.random() - 0.5) * 0.01,
        change_percent: (Math.random() - 0.5) * 2,
        timestamp: new Date().toISOString()
      },
      {
        symbol: 'USDJPY',
        bid: 149.320 + (Math.random() - 0.5) * 0.5,
        ask: 149.325 + (Math.random() - 0.5) * 0.5,
        last: 149.322 + (Math.random() - 0.5) * 0.5,
        volume: Math.floor(Math.random() * 900000) + 900000,
        high: 149.580,
        low: 149.150,
        change_value: (Math.random() - 0.5) * 1,
        change_percent: (Math.random() - 0.5) * 1,
        timestamp: new Date().toISOString()
      }
    ])

  if (marketDataError) {
    console.warn('Market data insertion issue:', marketDataError.message)
  } else {
    console.log('✅ Sample market data updated')
  }
}

if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('Seeding process finished')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Seeding process failed:', error)
      process.exit(1)
    })
}

export { seedDatabase } 
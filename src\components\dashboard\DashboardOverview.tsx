'use client'

import { useState } from 'react'
import { 
  TrendingUp, 
  DollarSign, 
  BarChart3, 
  Eye,
  Target,
  Shield
} from 'lucide-react'

interface DashboardStats {
  label: string
  value: string
  change: string
  changePercent: string
  positive: boolean
  icon: any
}

interface RecentTrade {
  id: string
  symbol: string
  type: 'BUY' | 'SELL'
  amount: number
  pnl: number
  time: string
  status: 'success' | 'pending' | 'failed'
}

const DashboardOverview = () => {
  const [timeframe, setTimeframe] = useState('7D')

  const dashboardStats: DashboardStats[] = [
    {
      label: 'Total Balance',
      value: '$127,420.50',
      change: '+$2,847.30',
      changePercent: '+2.3%',
      positive: true,
      icon: DollarSign
    },
    {
      label: 'Daily P&L',
      value: '+$1,234.50',
      change: '+$567.20',
      changePercent: '+85.2%',
      positive: true,
      icon: TrendingUp
    },
    {
      label: 'Win Rate',
      value: '78.5%',
      change: '+2.1%',
      changePercent: '+2.1%',
      positive: true,
      icon: Target
    },
    {
      label: 'Risk Score',
      value: '8.2/10',
      change: '+0.3',
      changePercent: '+3.8%',
      positive: true,
      icon: Shield
    }
  ]

  const recentTrades: RecentTrade[] = [
    {
      id: '1',
      symbol: 'EUR/USD',
      type: 'BUY',
      amount: 0.1,
      pnl: 45.50,
      time: '2 hours ago',
      status: 'success'
    },
    {
      id: '2',
      symbol: 'GBP/USD',
      type: 'SELL',
      amount: 0.05,
      pnl: -12.30,
      time: '4 hours ago',
      status: 'success'
    },
    {
      id: '3',
      symbol: 'USD/JPY',
      type: 'BUY',
      amount: 0.15,
      pnl: 78.90,
      time: '6 hours ago',
      status: 'success'
    },
    {
      id: '4',
      symbol: 'GOLD',
      type: 'BUY',
      amount: 0.02,
      pnl: 156.75,
      time: '1 day ago',
      status: 'success'
    }
  ]

  const challengeProgress = {
    currentPhase: 'Evaluation',
    progress: 68,
    target: '$10,000',
    current: '$6,800',
    daysLeft: 22,
    maxDrawdown: 10,
    currentDrawdown: 3.2
  }

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {dashboardStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div key={index} className="glass-card p-6 group hover:bg-slate-800/60 transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-green-500/10 group-hover:bg-green-500/20 transition-colors">
                  <Icon className="h-6 w-6 text-green-400" />
                </div>
                <div className={`text-sm font-bold px-2 py-1 rounded ${
                  stat.positive ? 'text-green-400 bg-green-400/10' : 'text-red-400 bg-red-400/10'
                }`}>
                  {stat.changePercent}
                </div>
              </div>
              <div className="text-2xl font-bold text-primary-content mb-1">{stat.value}</div>
              <div className="text-sm text-secondary-content mb-2">{stat.label}</div>
              <div className={`text-sm font-medium ${stat.positive ? 'text-green-400' : 'text-red-400'}`}>
                {stat.change} today
              </div>
            </div>
          )
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Challenge Progress */}
        <div className="lg:col-span-2 glass-card p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-primary-content">Challenge Progress</h3>
            <div className="flex items-center space-x-2">
              {['1D', '7D', '1M', '3M'].map((tf) => (
                <button
                  key={tf}
                  onClick={() => setTimeframe(tf)}
                  className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                    timeframe === tf 
                      ? 'bg-green-500 text-white' 
                      : 'text-secondary-content hover:text-primary-content'
                  }`}
                >
                  {tf}
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-6">
            {/* Phase Info */}
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-primary-content">{challengeProgress.currentPhase} Phase</div>
                <div className="text-secondary-content">Target: {challengeProgress.target}</div>
              </div>
              <div className="text-right">
                <div className="text-xl font-bold text-green-400">{challengeProgress.current}</div>
                <div className="text-secondary-content">{challengeProgress.daysLeft} days left</div>
              </div>
            </div>

            {/* Progress Bar */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-secondary-content">Progress</span>
                <span className="text-sm font-bold text-green-400">{challengeProgress.progress}%</span>
              </div>
              <div className="w-full bg-slate-800 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-green-500 to-green-400 h-3 rounded-full transition-all duration-500"
                  style={{ width: `${challengeProgress.progress}%` }}
                />
              </div>
            </div>

            {/* Risk Metrics */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-slate-800/60 rounded-lg p-4">
                <div className="text-sm text-secondary-content mb-1">Max Drawdown</div>
                <div className="text-lg font-bold text-red-400">{challengeProgress.maxDrawdown}%</div>
              </div>
              <div className="bg-slate-800/60 rounded-lg p-4">
                <div className="text-sm text-secondary-content mb-1">Current Drawdown</div>
                <div className="text-lg font-bold text-yellow-400">{challengeProgress.currentDrawdown}%</div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="glass-card p-6">
          <h3 className="text-xl font-bold text-primary-content mb-6">Quick Actions</h3>
          <div className="space-y-4">
            <button className="w-full buy-button py-3 px-4 rounded-xl font-semibold flex items-center justify-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>New Trade</span>
            </button>
            <button className="w-full professional-button py-3 px-4 rounded-xl font-semibold flex items-center justify-center space-x-2">
              <Eye className="h-5 w-5" />
              <span>View Portfolio</span>
            </button>
            <button className="w-full bg-slate-700 hover:bg-slate-600 text-primary-content py-3 px-4 rounded-xl font-semibold flex items-center justify-center space-x-2 transition-colors">
              <BarChart3 className="h-5 w-5" />
              <span>Analytics</span>
            </button>
          </div>

          {/* Market Status */}
          <div className="mt-6 p-4 bg-slate-800/60 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm text-secondary-content">Market Status</span>
              <div className="live-indicator">
                <div className="live-dot" />
                <span className="text-xs text-green-400">OPEN</span>
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-secondary-content">Session:</span>
                <span className="text-primary-content">London</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-content">Next Close:</span>
                <span className="text-primary-content">6h 23m</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Trades */}
        <div className="glass-card p-6">
          <h3 className="text-xl font-bold text-primary-content mb-6">Recent Trades</h3>
          <div className="space-y-3">
            {recentTrades.map((trade) => (
              <div key={trade.id} className="flex items-center justify-between p-3 bg-slate-800/60 rounded-lg hover:bg-slate-700/60 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    trade.status === 'success' ? 'bg-green-400' : 
                    trade.status === 'pending' ? 'bg-yellow-400' : 'bg-red-400'
                  }`} />
                  <div>
                    <div className="font-mono font-bold text-primary-content">{trade.symbol}</div>
                    <div className="text-sm text-secondary-content">{trade.time}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`font-bold ${trade.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {trade.pnl >= 0 ? '+' : ''}${trade.pnl.toFixed(2)}
                  </div>
                  <div className="text-sm text-secondary-content">
                    {trade.type} {trade.amount}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Performance Chart */}
        <div className="glass-card p-6">
          <h3 className="text-xl font-bold text-primary-content mb-6">Performance Overview</h3>
          <div className="h-48 bg-slate-900/60 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-secondary-content mx-auto mb-3" />
              <p className="text-secondary-content">Performance Chart</p>
              <p className="text-xs text-secondary-content/70">Advanced analytics coming soon</p>
            </div>
          </div>
          
          {/* Quick Stats */}
          <div className="grid grid-cols-3 gap-4 mt-6">
            <div className="text-center">
              <div className="text-lg font-bold text-green-400">78%</div>
              <div className="text-xs text-secondary-content">Win Rate</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-blue-400">1.84</div>
              <div className="text-xs text-secondary-content">Profit Factor</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-yellow-400">2.3%</div>
              <div className="text-xs text-secondary-content">Max DD</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardOverview 
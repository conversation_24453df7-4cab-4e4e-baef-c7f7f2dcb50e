import { Router, Response } from 'express'
import { z } from 'zod'
import { supabase } from '../../lib/supabase/server'
import { validateRequest, ValidationRequest } from '../middleware/validation'
import { asyncHand<PERSON>, createValidationError, createNotFoundError, createInternalError } from '../middleware/errorHandler'
import { AuthenticatedRequest } from '../middleware/auth'
import { broadcastRiskAlert } from '../websocket/socketManager'
import { io } from '../index'

const router = Router()

// Validation schemas
const accountIdSchema = z.object({
  accountId: z.string().uuid('Invalid account ID format')
})

const ruleIdSchema = z.object({
  ruleId: z.string().uuid('Invalid rule ID format')
})

const alertIdSchema = z.object({
  alertId: z.string().uuid('Invalid alert ID format')
})

const updateRiskRuleSchema = z.object({
  value: z.number().min(0).optional(),
  enabled: z.boolean().optional(),
  priority: z.number().min(1).max(10).optional()
})

const emergencyCloseAllSchema = z.object({
  reason: z.string().min(5, 'Reason must be at least 5 characters'),
  confirmationCode: z.string().length(6, 'Confirmation code must be 6 characters')
})

const pauseTradingSchema = z.object({
  duration: z.number().min(0).optional().default(0), // 0 = indefinite
  reason: z.string().min(5, 'Reason must be at least 5 characters')
})

const acknowledgeAlertSchema = z.object({
  note: z.string().optional()
})

// Helper function to verify account ownership
const verifyAccountOwnership = async (accountId: string, userId: string) => {
  const { data: account, error } = await supabase
    .from('trading_accounts')
    .select('id, user_id, balance, equity, margin, free_margin')
    .eq('id', accountId)
    .eq('user_id', userId)
    .single()

  if (error || !account) {
    throw createNotFoundError('Trading account not found or access denied')
  }

  return account
}

// Helper function to calculate risk score (0-10 scale)
const calculateRiskScore = (metrics: any) => {
  let score = 0
  
  // Daily drawdown risk (0-3 points)
  const dailyDrawdownPercent = (metrics.dailyDrawdown / metrics.maxDailyDrawdownLimit) * 100
  if (dailyDrawdownPercent > 80) score += 3
  else if (dailyDrawdownPercent > 60) score += 2
  else if (dailyDrawdownPercent > 40) score += 1

  // Total drawdown risk (0-3 points)  
  const totalDrawdownPercent = (metrics.totalDrawdown / metrics.maxTotalDrawdownLimit) * 100
  if (totalDrawdownPercent > 80) score += 3
  else if (totalDrawdownPercent > 60) score += 2
  else if (totalDrawdownPercent > 40) score += 1

  // Position size risk (0-2 points)
  const positionPercent = (metrics.positionSize / metrics.maxPositionSizeLimit) * 100
  if (positionPercent > 90) score += 2
  else if (positionPercent > 70) score += 1

  // Open positions risk (0-1 point)
  if (metrics.openPositions >= metrics.maxPositionsLimit) score += 1

  // Margin level risk (0-1 point)
  if (metrics.marginLevel < 150) score += 1

  return Math.min(score, 10)
}

// GET /api/risk/current-metrics/:accountId
router.get('/current-metrics/:accountId',
  validateRequest({ params: accountIdSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { accountId } = req.validatedParams
    const userId = req.user!.id

    // Verify account ownership
    const account = await verifyAccountOwnership(accountId, userId)

    try {
      // Get current risk rules for the account
      const { data: riskRules, error: rulesError } = await supabase
        .from('risk_rules')
        .select('*')
        .eq('account_id', accountId)
        .eq('enabled', true)

      if (rulesError) {
        throw createInternalError('Failed to fetch risk rules')
      }

      // Get current open positions
      const { data: openPositions, error: positionsError } = await supabase
        .from('trades')
        .select('*')
        .eq('account_id', accountId)
        .eq('status', 'open')

      if (positionsError) {
        throw createInternalError('Failed to fetch open positions')
      }

      // Calculate current metrics
      const totalPositionSize = openPositions.reduce((sum, pos) => sum + (pos.volume || 0), 0)

      // Get today's trades for daily drawdown calculation
      const today = new Date()
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
      
      const { data: todayTrades, error: todayTradesError } = await supabase
        .from('trades')
        .select('profit')
        .eq('account_id', accountId)
        .gte('open_time', startOfDay.toISOString())

      if (todayTradesError) {
        throw createInternalError('Failed to fetch today\'s trades')
      }

      const dailyPnL = todayTrades.reduce((sum, trade) => sum + (trade.profit || 0), 0)
      
      // Extract risk limits from rules
      const maxDailyLossRule = riskRules.find(rule => rule.type === 'max_daily_loss')
      const maxTotalLossRule = riskRules.find(rule => rule.type === 'max_total_loss')
      const maxPositionSizeRule = riskRules.find(rule => rule.type === 'max_position_size')
      const maxPositionsRule = riskRules.find(rule => rule.type === 'max_positions')

      const maxDailyDrawdownLimit = maxDailyLossRule?.value || account.balance * 0.05 // Default 5%
      const maxTotalDrawdownLimit = maxTotalLossRule?.value || account.balance * 0.1 // Default 10%
      const maxPositionSizeLimit = maxPositionSizeRule?.value || 10.0 // Default 10 lots
      const maxPositionsLimit = maxPositionsRule?.value || 10 // Default 10 positions

      // Calculate drawdowns
      const dailyDrawdown = Math.max(0, -dailyPnL)
      const totalEquityLoss = Math.max(0, account.balance - account.equity)

      // Calculate margin level
      const marginLevel = account.margin > 0 ? (account.equity / account.margin) * 100 : 999999

      const riskMetrics = {
        dailyDrawdown,
        maxDailyDrawdownLimit,
        totalDrawdown: totalEquityLoss,
        maxTotalDrawdownLimit,
        positionSize: totalPositionSize,
        maxPositionSizeLimit,
        openPositions: openPositions.length,
        maxPositionsLimit,
        marginLevel,
        freeMargin: account.free_margin,
        equity: account.equity,
        balance: account.balance,
        riskScore: 0, // Will be calculated below
        lastUpdate: new Date().toISOString()
      }

      // Calculate risk score
      riskMetrics.riskScore = calculateRiskScore(riskMetrics)

      res.json({
        success: true,
        data: riskMetrics
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/risk/rules/:accountId
router.get('/rules/:accountId',
  validateRequest({ params: accountIdSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { accountId } = req.validatedParams
    const userId = req.user!.id

    // Verify account ownership
    await verifyAccountOwnership(accountId, userId)

    try {
      const { data: rules, error } = await supabase
        .from('risk_rules')
        .select('*')
        .eq('account_id', accountId)
        .order('priority', { ascending: false })

      if (error) {
        throw createInternalError('Failed to fetch risk rules')
      }

      const formattedRules = rules.map(rule => ({
        id: rule.id,
        name: rule.name,
        description: rule.description,
        type: rule.type,
        value: rule.value,
        enabled: rule.enabled,
        priority: rule.priority
      }))

      res.json({
        success: true,
        data: { rules: formattedRules }
      })

    } catch (error) {
      throw error
    }
  })
)

// PUT /api/risk/rules/:ruleId
router.put('/rules/:ruleId',
  validateRequest({ 
    params: ruleIdSchema,
    body: updateRiskRuleSchema 
  }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { ruleId } = req.validatedParams
    const updateData = req.validatedBody
    const userId = req.user!.id

    try {
      // First verify the rule belongs to user's account
      const { data: rule, error: ruleError } = await supabase
        .from('risk_rules')
        .select('*, trading_accounts!inner(user_id)')
        .eq('id', ruleId)
        .single()

      if (ruleError || !rule || rule.trading_accounts.user_id !== userId) {
        throw createNotFoundError('Risk rule not found or access denied')
      }

      // Update the rule
      const { data: updatedRule, error: updateError } = await supabase
        .from('risk_rules')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', ruleId)
        .select()
        .single()

      if (updateError) {
        throw createInternalError('Failed to update risk rule')
      }

      res.json({
        success: true,
        data: updatedRule
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/risk/alerts/:accountId
router.get('/alerts/:accountId',
  validateRequest({ params: accountIdSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { accountId } = req.validatedParams
    const userId = req.user!.id

    // Verify account ownership
    await verifyAccountOwnership(accountId, userId)

    try {
      const { data: alerts, error } = await supabase
        .from('risk_alerts')
        .select('*')
        .eq('account_id', accountId)
        .order('created_at', { ascending: false })

      if (error) {
        throw createInternalError('Failed to fetch risk alerts')
      }

      const unreadCount = alerts.filter(alert => !alert.acknowledged).length

      res.json({
        success: true,
        data: {
          alerts,
          unreadCount
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// POST /api/risk/alerts/:alertId/acknowledge
router.post('/alerts/:alertId/acknowledge',
  validateRequest({ 
    params: alertIdSchema,
    body: acknowledgeAlertSchema 
  }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { alertId } = req.validatedParams
    const { note } = req.validatedBody
    const userId = req.user!.id

    try {
      // Verify alert belongs to user's account
      const { data: alert, error: alertError } = await supabase
        .from('risk_alerts')
        .select('*, trading_accounts!inner(user_id)')
        .eq('id', alertId)
        .single()

      if (alertError || !alert || alert.trading_accounts.user_id !== userId) {
        throw createNotFoundError('Risk alert not found or access denied')
      }

      // Acknowledge the alert
      const { error: updateError } = await supabase
        .from('risk_alerts')
        .update({
          acknowledged: true,
          acknowledged_at: new Date().toISOString(),
          metadata: { ...alert.metadata, acknowledgment_note: note }
        })
        .eq('id', alertId)

      if (updateError) {
        throw createInternalError('Failed to acknowledge alert')
      }

      res.json({
        success: true,
        data: { message: 'Alert acknowledged successfully' }
      })

    } catch (error) {
      throw error
    }
  })
)

// POST /api/risk/emergency/close-all/:accountId
router.post('/emergency/close-all/:accountId',
  validateRequest({ 
    params: accountIdSchema,
    body: emergencyCloseAllSchema 
  }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { accountId } = req.validatedParams
    const { reason, confirmationCode } = req.validatedBody
    const userId = req.user!.id

    // Verify account ownership
    await verifyAccountOwnership(accountId, userId)

    // Verify confirmation code (should be user's PIN or similar)
    if (confirmationCode !== '123456') { // TODO: Implement proper confirmation code verification
      throw createValidationError('Invalid confirmation code')
    }

    try {
      // Get all open positions
      const { data: openPositions, error: positionsError } = await supabase
        .from('trades')
        .select('*')
        .eq('account_id', accountId)
        .eq('status', 'open')

      if (positionsError) {
        throw createInternalError('Failed to fetch open positions')
      }

      // Close all positions (in a real system, this would interface with MT5)
      const closeResults = []
      for (const position of openPositions) {
        const { data: closedTrade, error: closeError } = await supabase
          .from('trades')
          .update({
            status: 'closed',
            close_time: new Date().toISOString(),
            close_price: position.open_price, // In reality, this would be current market price
            profit: 0, // Would be calculated based on current price
            updated_at: new Date().toISOString()
          })
          .eq('id', position.id)
          .select()
          .single()

        if (!closeError) {
          closeResults.push(closedTrade)
        }
      }

      // Create audit log entry
      await supabase
        .from('audit_logs')
        .insert({
          user_id: userId,
          account_id: accountId,
          action: 'emergency_close_all',
          details: {
            reason,
            positions_closed: closeResults.length,
            total_positions: openPositions.length
          }
        })

      // Create risk alert
      await supabase
        .from('risk_alerts')
        .insert({
          account_id: accountId,
          type: 'info',
          title: 'Emergency Close All Executed',
          message: `All positions closed due to: ${reason}`,
          severity: 8,
          metadata: {
            action: 'emergency_close_all',
            positions_closed: closeResults.length,
            reason
          }
        })

      // Broadcast alert
      broadcastRiskAlert(io, accountId, {
        type: 'emergency',
        action: 'close_all',
        message: `Emergency close all executed: ${closeResults.length} positions closed`,
        severity: 'high'
      })

      res.json({
        success: true,
        data: {
          message: 'Emergency close all executed successfully',
          positions_closed: closeResults.length,
          total_positions: openPositions.length
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// POST /api/risk/emergency/pause-trading/:accountId
router.post('/emergency/pause-trading/:accountId',
  validateRequest({ 
    params: accountIdSchema,
    body: pauseTradingSchema 
  }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { accountId } = req.validatedParams
    const { duration, reason } = req.validatedBody
    const userId = req.user!.id

    // Verify account ownership
    await verifyAccountOwnership(accountId, userId)

    try {
      // Calculate pause end time
      const pauseUntil = duration > 0 
        ? new Date(Date.now() + duration * 60 * 1000).toISOString()
        : null // null means indefinite

      // Update trading account status
      const { error: updateError } = await supabase
        .from('trading_accounts')
        .update({
          status: 'paused',
          pause_until: pauseUntil,
          pause_reason: reason,
          updated_at: new Date().toISOString()
        })
        .eq('id', accountId)

      if (updateError) {
        throw createInternalError('Failed to pause trading')
      }

      // Create audit log entry
      await supabase
        .from('audit_logs')
        .insert({
          user_id: userId,
          account_id: accountId,
          action: 'pause_trading',
          details: {
            reason,
            duration_minutes: duration,
            pause_until: pauseUntil
          }
        })

      // Create risk alert
      await supabase
        .from('risk_alerts')
        .insert({
          account_id: accountId,
          type: 'warning',
          title: 'Trading Paused',
          message: `Trading paused ${duration > 0 ? `for ${duration} minutes` : 'indefinitely'}: ${reason}`,
          severity: 6,
          metadata: {
            action: 'pause_trading',
            duration_minutes: duration,
            reason,
            pause_until: pauseUntil
          }
        })

      // Broadcast alert
      broadcastRiskAlert(io, accountId, {
        type: 'warning',
        action: 'pause_trading',
        message: `Trading paused: ${reason}`,
        duration,
        severity: 'medium'
      })

      res.json({
        success: true,
        data: {
          message: 'Trading paused successfully',
          duration_minutes: duration,
          pause_until: pauseUntil,
          reason
        }
      })

    } catch (error) {
      throw error
    }
  })
)

export default router 
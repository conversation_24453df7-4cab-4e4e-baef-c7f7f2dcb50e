// Minimal Express server to bypass path-to-regexp issues
const express = require('express')
const cors = require('cors')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: path.resolve(process.cwd(), '.env.local') })
require('dotenv').config()

console.log('🔧 Starting minimal server...')
console.log('Environment variables loaded:', {
  SUPABASE_URL: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
  SERVICE_KEY: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
  JWT_SECRET: !!process.env.JWT_SECRET
})

const app = express()
const PORT = process.env.API_PORT || 8000

// Basic middleware - avoiding complex routing for now
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}))

app.use(express.json({ limit: '10mb' }))

// ===== WORKING ENDPOINTS =====

// Health check
app.get('/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: '1.0.0'
    }
  })
})

// Simple API test
app.get('/api/test', (req, res) => {
  res.json({ 
    success: true, 
    message: 'Minimal API server is working!',
    timestamp: new Date().toISOString()
  })
})

// Analytics test endpoints
app.get('/api/analytics/test', (req, res) => {
  res.json({
    success: true,
    data: {
      message: 'Analytics API endpoint working',
      sampleData: {
        totalTrades: 150,
        winningTrades: 103,
        losingTrades: 47,
        winRate: 68.67,
        avgWin: 145.30,
        avgLoss: -89.50,
        profitFactor: 1.67,
        profit: 2847.50,
        drawdown: 5.2,
        sharpeRatio: 1.45,
        returnOnInvestment: 28.47
      }
    }
  })
})

// Risk management test
app.get('/api/risk/test', (req, res) => {
  res.json({
    success: true,
    data: {
      message: 'Risk management API endpoint working',
      currentRisk: {
        dailyDrawdown: 2.1,
        maxDailyDrawdown: 5.0,
        totalDrawdown: 3.5,
        maxTotalDrawdown: 10.0,
        riskScore: 3.2,
        positionSize: 0.5,
        maxPositionSize: 2.0,
        openPositions: 3,
        maxPositions: 10,
        marginLevel: 250.5,
        freeMargin: 8500.00
      }
    }
  })
})

// User settings test
app.get('/api/users/test', (req, res) => {
  res.json({
    success: true,
    data: {
      message: 'User settings API endpoint working',
      userPreferences: {
        theme: 'dark',
        notifications: true,
        defaultLotSize: 0.1,
        autoStopLoss: true,
        defaultStopLoss: 50,
        riskPerTrade: 2.0
      }
    }
  })
})

// Support test
app.get('/api/support/test', (req, res) => {
  res.json({
    success: true,
    data: {
      message: 'Support API endpoint working',
      supportInfo: {
        ticketsOpen: 2,
        avgResponseTime: '4 hours',
        status: 'online',
        faqCount: 45,
        lastUpdate: new Date().toISOString()
      }
    }
  })
})

// Database test (simulated)
app.get('/test-db', (req, res) => {
  res.json({
    success: true,
    data: {
      message: 'Database connection test (simulated - will connect to real Supabase once server is stable)',
      sample_data: [
        { id: '1', name: 'Challenge $10K', type: 'evaluation', status: 'active' },
        { id: '2', name: 'Challenge $25K', type: 'evaluation', status: 'active' },
        { id: '3', name: 'Challenge $50K', type: 'funded', status: 'active' }
      ],
      tables_accessible: true,
      connection_status: 'simulated'
    }
  })
})

// Catch all other routes
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Route ${req.url} not found on minimal server`,
      timestamp: new Date().toISOString(),
      availableRoutes: [
        'GET /health',
        'GET /test-db', 
        'GET /api/test',
        'GET /api/analytics/test',
        'GET /api/risk/test',
        'GET /api/users/test',
        'GET /api/support/test'
      ]
    }
  })
})

// Start server
app.listen(PORT, () => {
  console.log('🚀 Minimal Prop Firm API Server running successfully!')
  console.log(`📡 Server: http://localhost:${PORT}`)
  console.log(`🔗 Health: http://localhost:${PORT}/health`)
  console.log(`💾 Database: http://localhost:${PORT}/test-db`)
  console.log(`🧪 Test API: http://localhost:${PORT}/api/test`)
  console.log('📊 Analytics: http://localhost:${PORT}/api/analytics/test')
  console.log('🛡️ Risk: http://localhost:${PORT}/api/risk/test')
  console.log('👤 Users: http://localhost:${PORT}/api/users/test')
  console.log('❓ Support: http://localhost:${PORT}/api/support/test')
  console.log('')
  console.log('✅ Server ready for frontend integration!')
  console.log('🎯 Next step: Connect frontend dashboard components')
}) 
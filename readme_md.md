# Prop Firm Trading Platform

A comprehensive proprietary trading firm platform that connects traders to MetaTrader 5 while providing a unique web-based trading interface. Traders progress through challenge phases before gaining access to live capital.

## 🏗️ Architecture

- **Frontend**: Next.js 14+ with TypeScript and Tailwind CSS
- **Backend**: Node.js API with Socket.io for real-time features  
- **Database**: Supabase (PostgreSQL with real-time subscriptions)
- **Payments**: Stripe for challenge fees and profit sharing
- **Trading**: MetaTrader 5 integration via custom bridge
- **Authentication**: Supabase Auth with multi-factor authentication

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm/yarn
- Supabase account and project
- Stripe account (test mode for development)
- MetaTrader 5 (for trading integration)

### Environment Setup

1. **Clone and install dependencies**:
```bash
git clone <repository-url>
cd prop-firm-platform
npm install
```

2. **Environment variables** (create `.env.local`):
```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Application
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
NODE_ENV=development

# MT5 Integration (to be configured)
MT5_SERVER_URL=your_mt5_server
MT5_API_KEY=your_mt5_api_key
```

3. **Database setup**:
```bash
# Run database migrations
npm run db:migrate

# Seed development data
npm run db:seed
```

4. **Start development server**:
```bash
npm run dev
```

## 📁 Project Structure

```
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── (auth)/            # Authentication pages
│   │   ├── (dashboard)/       # Protected dashboard pages
│   │   ├── api/               # API routes
│   │   └── globals.css        # Global styles
│   ├── components/            # Reusable UI components
│   │   ├── ui/               # Base UI components (shadcn)
│   │   ├── auth/             # Authentication components
│   │   ├── trading/          # Trading interface components
│   │   └── dashboard/        # Dashboard components
│   ├── lib/                  # Utility functions and configurations
│   │   ├── supabase/         # Supabase client and utilities
│   │   ├── stripe/           # Stripe integration
│   │   ├── mt5/              # MT5 integration utilities
│   │   └── utils/            # General utilities
│   ├── hooks/                # Custom React hooks
│   ├── stores/               # State management (Zustand)
│   ├── types/                # TypeScript type definitions
│   └── middleware.ts         # Next.js middleware
├── supabase/                 # Database migrations and types
├── tests/                    # Test files
├── docs/                     # Project documentation
├── PLANNING.md              # High-level project planning
├── TASK.md                  # Current tasks and backlog
└── README.md               # This file
```

## 🗄️ Database Schema

### Core Tables
- `users` - User profiles and authentication
- `trading_accounts` - Challenge and funded account records
- `challenges` - Challenge configuration and rules
- `trades` - Individual trade records
- `payments` - Payment transactions and history
- `risk_rules` - Risk management parameters
- `audit_logs` - System activity logs

### Key Relationships
- Users can have multiple trading accounts
- Each account belongs to a specific challenge type
- Trades are associated with trading accounts
- Payments track challenge purchases and profit sharing

## 🔐 Security Features

- **Authentication**: Multi-factor authentication via Supabase Auth
- **Authorization**: Row Level Security (RLS) policies for data access
- **API Security**: Rate limiting and request validation
- **Data Protection**: Encryption for sensitive information
- **Audit Logging**: Comprehensive activity tracking
- **Payment Security**: PCI-compliant payment processing via Stripe

## 🧪 Testing

```bash
# Run unit tests
npm run test

# Run integration tests  
npm run test:integration

# Run E2E tests
npm run test:e2e

# Generate coverage report
npm run test:coverage
```

## 📊 Key Features

### Trader Journey
1. **Registration & KYC** - Account creation with identity verification
2. **Challenge Selection** - Choose from multiple challenge types
3. **Payment Processing** - Secure payment via Stripe
4. **Trading Phase** - Access to funded simulation account
5. **Evaluation** - Performance monitoring against risk rules
6. **Funding** - Transition to live capital upon success

### Trading Interface
- Real-time price charts and market data
- Advanced order management (market, limit, stop orders)
- Position monitoring and P&L tracking
- Risk metrics dashboard
- Trading journal and performance analytics

### Risk Management
- Real-time drawdown monitoring
- Configurable position and loss limits
- Automated account suspension on rule violations
- Risk score calculation and tracking
- Daily and overall loss limits

## 🔌 API Integration

### MetaTrader 5
- Custom Expert Advisor for order bridge
- Real-time price feed integration
- Trade execution and management
- Account balance and equity monitoring

### External Services
- **Stripe**: Payment processing and webhooks
- **Supabase**: Database and real-time subscriptions
- **KYC Provider**: Identity verification (to be integrated)
- **Email Service**: Transactional emails (to be configured)

## 🚀 Deployment

### Development
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
```

### Production Checklist
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] SSL certificates installed
- [ ] Domain DNS configured
- [ ] Monitoring and logging setup
- [ ] Security audit completed

## 📈 Monitoring

- Application performance monitoring (to be configured)
- Error tracking and alerting
- Database performance metrics
- Trading activity monitoring
- User behavior analytics

## 🤝 Contributing

1. Read `PLANNING.md` for project architecture and constraints
2. Check `TASK.md` for current priorities and available tasks
3. Follow the coding standards defined in `.cursorrules`
4. Write tests for new features
5. Update documentation as needed

## 📞 Support

For development questions and issues:
- Check existing documentation in `/docs`
- Review `TASK.md` for known issues and planned fixes
- Follow security protocols for any financial-related code

## 📄 License

[To be determined - likely proprietary for commercial prop firm]

---

**⚠️ Important Security Notice**: This platform handles financial transactions and trading operations. Always follow security best practices and never commit sensitive information like API keys or secrets to version control.
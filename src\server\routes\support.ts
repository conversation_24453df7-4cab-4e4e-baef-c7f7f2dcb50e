import { Router, Response } from 'express'
import { z } from 'zod'
import { supabase } from '../../lib/supabase/server'
import { validateRequest, ValidationRequest } from '../middleware/validation'
import { asyncHandler, createValidationError, createNotFoundError, createInternalError } from '../middleware/errorHandler'
import { AuthenticatedRequest } from '../middleware/auth'

const router = Router()

// Validation schemas
const faqQuerySchema = z.object({
  category: z.enum(['all', 'challenges', 'trading', 'risk', 'payments', 'technical']).optional().default('all'),
  search: z.string().optional(),
  limit: z.number().min(1).max(100).optional().default(20),
  offset: z.number().min(0).optional().default(0)
})

const faqFeedbackSchema = z.object({
  helpful: z.boolean(),
  comment: z.string().optional()
})

const ticketIdSchema = z.object({
  ticketId: z.string().uuid('Invalid ticket ID format')
})

const createTicketSchema = z.object({
  subject: z.string().min(5).max(200),
  category: z.enum(['general', 'technical', 'billing', 'trading']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  description: z.string().min(20).max(5000),
  attachments: z.array(z.object({
    filename: z.string(),
    content: z.string(), // base64 encoded
    size: z.number().max(5 * 1024 * 1024) // 5MB max
  })).optional()
})

const updateTicketSchema = z.object({
  status: z.enum(['open', 'pending', 'resolved', 'closed']).optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional()
})

const contactFormSchema = z.object({
  name: z.string().min(1).max(100),
  email: z.string().email(),
  subject: z.string().min(5).max(200),
  message: z.string().min(20).max(5000),
  category: z.string().min(1).max(50)
})

const ticketMessageSchema = z.object({
  message: z.string().min(1).max(5000),
  attachments: z.array(z.object({
    filename: z.string(),
    content: z.string(),
    size: z.number().max(5 * 1024 * 1024)
  })).optional()
})

// GET /api/support/faq
router.get('/faq',
  validateRequest({ query: faqQuerySchema }),
  asyncHandler(async (req: ValidationRequest, res: Response) => {
    const { category, search, limit, offset } = req.validatedQuery

    try {
      let query = supabase
        .from('faq_articles')
        .select('*', { count: 'exact' })

      // Apply category filter
      if (category !== 'all') {
        query = query.eq('category', category)
      }

      // Apply search filter
      if (search) {
        query = query.or(`question.ilike.%${search}%,answer.ilike.%${search}%,tags.cs.{${search}}`)
      }

      const { data: faqs, error, count } = await query
        .order('helpful', { ascending: false })
        .range(offset, offset + limit - 1)

      if (error) {
        throw createInternalError('Failed to fetch FAQ articles')
      }

      // Get all available categories
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('faq_articles')
        .select('category')
        .not('category', 'is', null)

      if (categoriesError) {
        throw createInternalError('Failed to fetch FAQ categories')
      }

      const uniqueCategories = [...new Set(categoriesData.map(item => item.category))]

      res.json({
        success: true,
        data: {
          faqs: faqs || [],
          total: count || 0,
          categories: uniqueCategories
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// POST /api/support/faq/:faqId/helpful
router.post('/faq/:faqId/helpful',
  validateRequest({ 
    params: z.object({ faqId: z.string().uuid() }),
    body: faqFeedbackSchema 
  }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { faqId } = req.validatedParams
    const { helpful, comment } = req.validatedBody
    const userId = req.user!.id

    try {
      // Check if user already provided feedback for this FAQ
      const { data: existingFeedback } = await supabase
        .from('faq_feedback')
        .select('id')
        .eq('faq_id', faqId)
        .eq('user_id', userId)
        .single()

      if (existingFeedback) {
        throw createValidationError('You have already provided feedback for this FAQ')
      }

      // Add feedback record
      const { error: insertError } = await supabase
        .from('faq_feedback')
        .insert({
          faq_id: faqId,
          user_id: userId,
          helpful,
          comment
        })

      if (insertError) {
        throw createInternalError('Failed to record FAQ feedback')
      }

      // Update FAQ helpful/not helpful counters
      const { error: updateError } = await supabase.rpc('update_faq_helpfulness', {
        faq_id: faqId,
        is_helpful: helpful
      })

      if (updateError) {
        throw createInternalError('Failed to update FAQ helpfulness counters')
      }

      res.json({
        success: true,
        data: { message: 'Feedback recorded successfully' }
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/support/tickets
router.get('/tickets',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id

    try {
      const { data: tickets, error, count } = await supabase
        .from('support_tickets')
        .select(`
          id,
          subject,
          status,
          priority,
          category,
          created_at,
          updated_at,
          support_messages(count)
        `, { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        throw createInternalError('Failed to fetch support tickets')
      }

      const formattedTickets = tickets?.map(ticket => ({
        id: ticket.id,
        subject: ticket.subject,
        status: ticket.status,
        priority: ticket.priority,
        category: ticket.category,
        createdAt: ticket.created_at,
        lastUpdate: ticket.updated_at,
        messagesCount: ticket.support_messages[0]?.count || 0
      })) || []

      res.json({
        success: true,
        data: {
          tickets: formattedTickets,
          total: count || 0
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// POST /api/support/tickets
router.post('/tickets',
  validateRequest({ body: createTicketSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id
    const { subject, category, priority, description, attachments } = req.validatedBody

    try {
      // Create the support ticket
      const { data: newTicket, error: ticketError } = await supabase
        .from('support_tickets')
        .insert({
          user_id: userId,
          subject,
          category,
          priority,
          status: 'open',
          description
        })
        .select()
        .single()

      if (ticketError) {
        throw createInternalError('Failed to create support ticket')
      }

      // Create initial message
      const { error: messageError } = await supabase
        .from('support_messages')
        .insert({
          ticket_id: newTicket.id,
          user_id: userId,
          sender: 'user',
          message: description,
          is_initial: true
        })

      if (messageError) {
        throw createInternalError('Failed to create initial ticket message')
      }

      // Handle attachments if provided
      if (attachments && attachments.length > 0) {
        const attachmentInserts = attachments.map((attachment: { filename: string; content: string; size: number }) => ({
          ticket_id: newTicket.id,
          filename: attachment.filename,
          file_data: attachment.content,
          file_size: attachment.size,
          uploaded_by: userId
        }))

        const { error: attachmentError } = await supabase
          .from('support_attachments')
          .insert(attachmentInserts)

        if (attachmentError) {
          throw createInternalError('Failed to save ticket attachments')
        }
      }

      res.status(201).json({
        success: true,
        data: {
          id: newTicket.id,
          subject: newTicket.subject,
          status: newTicket.status,
          priority: newTicket.priority,
          category: newTicket.category,
          createdAt: newTicket.created_at,
          message: 'Support ticket created successfully'
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/support/tickets/:ticketId
router.get('/tickets/:ticketId',
  validateRequest({ params: ticketIdSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { ticketId } = req.validatedParams
    const userId = req.user!.id

    try {
      // Get ticket details
      const { data: ticket, error: ticketError } = await supabase
        .from('support_tickets')
        .select('*')
        .eq('id', ticketId)
        .eq('user_id', userId)
        .single()

      if (ticketError || !ticket) {
        throw createNotFoundError('Support ticket not found or access denied')
      }

      // Get all messages for this ticket
      const { data: messages, error: messagesError } = await supabase
        .from('support_messages')
        .select(`
          id,
          sender,
          message,
          created_at,
          support_attachments(
            id,
            filename,
            file_size
          )
        `)
        .eq('ticket_id', ticketId)
        .order('created_at', { ascending: true })

      if (messagesError) {
        throw createInternalError('Failed to fetch ticket messages')
      }

      const formattedMessages = messages?.map(message => ({
        id: message.id,
        sender: message.sender,
        message: message.message,
        timestamp: message.created_at,
        attachments: message.support_attachments.map((att: any) => ({
          id: att.id,
          filename: att.filename,
          size: att.file_size,
          url: `/api/support/attachments/${att.id}` // Download URL
        }))
      })) || []

      res.json({
        success: true,
        data: {
          ticket: {
            id: ticket.id,
            subject: ticket.subject,
            status: ticket.status,
            priority: ticket.priority,
            category: ticket.category,
            createdAt: ticket.created_at,
            lastUpdate: ticket.updated_at
          },
          messages: formattedMessages
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// PUT /api/support/tickets/:ticketId
router.put('/tickets/:ticketId',
  validateRequest({ 
    params: ticketIdSchema,
    body: updateTicketSchema 
  }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { ticketId } = req.validatedParams
    const updateData = req.validatedBody
    const userId = req.user!.id

    try {
      // Verify ticket ownership
      const { data: ticket, error: verifyError } = await supabase
        .from('support_tickets')
        .select('id, user_id')
        .eq('id', ticketId)
        .eq('user_id', userId)
        .single()

      if (verifyError || !ticket) {
        throw createNotFoundError('Support ticket not found or access denied')
      }

      // Update the ticket
      const { data: updatedTicket, error: updateError } = await supabase
        .from('support_tickets')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', ticketId)
        .select()
        .single()

      if (updateError) {
        throw createInternalError('Failed to update support ticket')
      }

      res.json({
        success: true,
        data: updatedTicket
      })

    } catch (error) {
      throw error
    }
  })
)

// POST /api/support/tickets/:ticketId/messages
router.post('/tickets/:ticketId/messages',
  validateRequest({ 
    params: ticketIdSchema,
    body: ticketMessageSchema 
  }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const { ticketId } = req.validatedParams
    const { message, attachments } = req.validatedBody
    const userId = req.user!.id

    try {
      // Verify ticket ownership and status
      const { data: ticket, error: verifyError } = await supabase
        .from('support_tickets')
        .select('id, user_id, status')
        .eq('id', ticketId)
        .eq('user_id', userId)
        .single()

      if (verifyError || !ticket) {
        throw createNotFoundError('Support ticket not found or access denied')
      }

      if (ticket.status === 'closed') {
        throw createValidationError('Cannot add messages to a closed ticket')
      }

      // Create the message
      const { data: newMessage, error: messageError } = await supabase
        .from('support_messages')
        .insert({
          ticket_id: ticketId,
          user_id: userId,
          sender: 'user',
          message
        })
        .select()
        .single()

      if (messageError) {
        throw createInternalError('Failed to create message')
      }

      // Handle attachments if provided
      if (attachments && attachments.length > 0) {
        const attachmentInserts = attachments.map((attachment: { filename: string; content: string; size: number }) => ({
          ticket_id: ticketId,
          message_id: newMessage.id,
          filename: attachment.filename,
          file_data: attachment.content,
          file_size: attachment.size,
          uploaded_by: userId
        }))

        const { error: attachmentError } = await supabase
          .from('support_attachments')
          .insert(attachmentInserts)

        if (attachmentError) {
          throw createInternalError('Failed to save message attachments')
        }
      }

      // Update ticket status and timestamp
      await supabase
        .from('support_tickets')
        .update({
          status: 'pending', // User replied, waiting for support
          updated_at: new Date().toISOString()
        })
        .eq('id', ticketId)

      res.status(201).json({
        success: true,
        data: {
          id: newMessage.id,
          message: 'Message added successfully'
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/support/guides
router.get('/guides',
  asyncHandler(async (_req: AuthenticatedRequest, res: Response) => {
    try {
      const { data: guides, error } = await supabase
        .from('support_guides')
        .select('*')
        .eq('published', true)
        .order('category')
        .order('title')

      if (error) {
        throw createInternalError('Failed to fetch support guides')
      }

      // Get unique categories
      const categories = [...new Set(guides?.map(guide => guide.category) || [])]

      const formattedGuides = guides?.map(guide => ({
        id: guide.id,
        title: guide.title,
        description: guide.description,
        type: guide.type,
        category: guide.category,
        url: guide.url,
        downloadUrl: guide.download_url,
        size: guide.file_size,
        duration: guide.duration,
        lastUpdated: guide.updated_at
      })) || []

      res.json({
        success: true,
        data: {
          guides: formattedGuides,
          categories
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// POST /api/support/contact
router.post('/contact',
  validateRequest({ body: contactFormSchema }),
  asyncHandler(async (req: ValidationRequest, res: Response) => {
    const { name, email, subject, message, category } = req.validatedBody

    try {
      // Store contact form submission
      const { error: insertError } = await supabase
        .from('contact_submissions')
        .insert({
          name,
          email,
          subject,
          message,
          category,
          ip_address: req.ip,
          user_agent: req.get('User-Agent')
        })

      if (insertError) {
        throw createInternalError('Failed to submit contact form')
      }

      // In a real application, you would send an email notification here
      // await sendContactFormNotification({ name, email, subject, message, category })

      res.json({
        success: true,
        data: {
          message: 'Contact form submitted successfully. We will get back to you within 24 hours.'
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/support/knowledge-base/search
router.get('/knowledge-base/search',
  validateRequest({ 
    query: z.object({
      q: z.string().min(3),
      category: z.string().optional(),
      limit: z.number().min(1).max(50).optional().default(10)
    })
  }),
  asyncHandler(async (req: ValidationRequest, res: Response) => {
    const { q, category, limit } = req.validatedQuery

    try {
      let query = supabase
        .from('knowledge_base_articles')
        .select('*')
        .eq('published', true)

      // Apply search
      query = query.or(`title.ilike.%${q}%,content.ilike.%${q}%,tags.cs.{${q}}`)

      // Apply category filter
      if (category) {
        query = query.eq('category', category)
      }

      const { data: articles, error } = await query
        .order('relevance_score', { ascending: false })
        .limit(limit)

      if (error) {
        throw createInternalError('Failed to search knowledge base')
      }

      res.json({
        success: true,
        data: {
          articles: articles || [],
          query: q,
          count: articles?.length || 0
        }
      })

    } catch (error) {
      throw error
    }
  })
)

export default router 
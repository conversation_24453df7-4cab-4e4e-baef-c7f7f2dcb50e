{"extends": ["next/core-web-vitals"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-empty-function": "warn", "prefer-const": "warn", "no-var": "error", "react/no-unescaped-entities": "warn"}, "ignorePatterns": ["node_modules/", ".next/", "out/"]}
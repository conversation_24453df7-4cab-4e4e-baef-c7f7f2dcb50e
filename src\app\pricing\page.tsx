import { Metadata } from 'next'
import Link from 'next/link'
import { ArrowLeft, ArrowRight, CheckCircle, X, Star, DollarSign, Shield } from 'lucide-react'
import { Logo } from '@/components/ui/Logo'
import { EliteText } from '@/components/ui/EliteText'

export const metadata: Metadata = {
  title: 'Pricing - Prop Bully Trading Platform',
  description: 'Transparent pricing for our trading challenges. Choose your package and start your journey to funded trading.',
}

export default function PricingPage() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Professional Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black">
        <div className="absolute inset-0 bg-gradient-to-r from-green-500/3 via-transparent to-blue-500/3" />
      </div>

      {/* Navigation */}
      <nav className="relative z-10 flex items-center justify-between p-6 border-b border-slate-700/50">
        <div className="flex items-center space-x-4">
          <Logo size="md" imageSrc="/images/logos/prop-bully-logo.png" alt="Prop Bully Logo" />
          <EliteText size="lg" variant="gold">Prop Bully</EliteText>
        </div>
        <div className="flex items-center space-x-4">
          <Link href="/" className="flex items-center space-x-2 text-white/60 hover:text-white transition-colors">
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Home</span>
          </Link>
          <Link href="/auth/register">
            <button className="professional-button-dark">Get Started</button>
          </Link>
        </div>
      </nav>

      <div className="relative z-10 max-w-6xl mx-auto px-6 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            Simple, <span className="text-gradient-gold">Transparent</span> Pricing
          </h1>
          <p className="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
            No hidden fees, no ongoing costs. Pay once for your challenge, pass the evaluation, and keep up to 90% of your profits forever.
          </p>
        </div>

        {/* Pricing Table */}
        <div className="mb-16">
          <div className="glass-card overflow-hidden">
            <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 p-6 border-b border-slate-700/50">
              <h2 className="text-2xl font-bold text-white text-center mb-2">Challenge Packages</h2>
              <p className="text-white/60 text-center">One-time payment • No monthly fees • Lifetime access</p>
            </div>
            
            <div className="p-8">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-slate-700/50">
                      <th className="text-left py-4 px-4 text-white font-semibold">Package</th>
                      <th className="text-center py-4 px-4 text-white font-semibold">Starter</th>
                      <th className="text-center py-4 px-4 text-white font-semibold relative">
                        <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                          <div className="bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold">Popular</div>
                        </div>
                        Professional
                      </th>
                      <th className="text-center py-4 px-4 text-white font-semibold">Elite</th>
                      <th className="text-center py-4 px-4 text-white font-semibold">Institutional</th>
                    </tr>
                  </thead>
                  <tbody className="text-white/80">
                    <tr className="border-b border-slate-700/30">
                      <td className="py-4 px-4 font-medium">Trading Capital</td>
                      <td className="py-4 px-4 text-center text-green-400 font-semibold">$10,000</td>
                      <td className="py-4 px-4 text-center text-green-400 font-semibold">$25,000</td>
                      <td className="py-4 px-4 text-center text-green-400 font-semibold">$50,000</td>
                      <td className="py-4 px-4 text-center text-green-400 font-semibold">$100,000</td>
                    </tr>
                    <tr className="border-b border-slate-700/30">
                      <td className="py-4 px-4 font-medium">Challenge Price</td>
                      <td className="py-4 px-4 text-center font-bold text-2xl">$149</td>
                      <td className="py-4 px-4 text-center font-bold text-2xl">$299</td>
                      <td className="py-4 px-4 text-center font-bold text-2xl">$549</td>
                      <td className="py-4 px-4 text-center font-bold text-2xl">$999</td>
                    </tr>
                    <tr className="border-b border-slate-700/30">
                      <td className="py-4 px-4 font-medium">Profit Target (Phase 1)</td>
                      <td className="py-4 px-4 text-center">8%</td>
                      <td className="py-4 px-4 text-center">8%</td>
                      <td className="py-4 px-4 text-center">8%</td>
                      <td className="py-4 px-4 text-center">8%</td>
                    </tr>
                    <tr className="border-b border-slate-700/30">
                      <td className="py-4 px-4 font-medium">Profit Target (Phase 2)</td>
                      <td className="py-4 px-4 text-center">5%</td>
                      <td className="py-4 px-4 text-center">5%</td>
                      <td className="py-4 px-4 text-center">5%</td>
                      <td className="py-4 px-4 text-center">5%</td>
                    </tr>
                    <tr className="border-b border-slate-700/30">
                      <td className="py-4 px-4 font-medium">Maximum Drawdown</td>
                      <td className="py-4 px-4 text-center text-red-400">10%</td>
                      <td className="py-4 px-4 text-center text-red-400">10%</td>
                      <td className="py-4 px-4 text-center text-red-400">10%</td>
                      <td className="py-4 px-4 text-center text-red-400">10%</td>
                    </tr>
                    <tr className="border-b border-slate-700/30">
                      <td className="py-4 px-4 font-medium">Profit Share</td>
                      <td className="py-4 px-4 text-center text-purple-400 font-semibold">80%</td>
                      <td className="py-4 px-4 text-center text-purple-400 font-semibold">85%</td>
                      <td className="py-4 px-4 text-center text-purple-400 font-semibold">90%</td>
                      <td className="py-4 px-4 text-center text-purple-400 font-semibold">90%</td>
                    </tr>
                    <tr className="border-b border-slate-700/30">
                      <td className="py-4 px-4 font-medium">Free Retake</td>
                      <td className="py-4 px-4 text-center"><CheckCircle className="h-5 w-5 text-green-400 mx-auto" /></td>
                      <td className="py-4 px-4 text-center"><CheckCircle className="h-5 w-5 text-green-400 mx-auto" /></td>
                      <td className="py-4 px-4 text-center"><CheckCircle className="h-5 w-5 text-green-400 mx-auto" /></td>
                      <td className="py-4 px-4 text-center"><CheckCircle className="h-5 w-5 text-green-400 mx-auto" /></td>
                    </tr>
                    <tr className="border-b border-slate-700/30">
                      <td className="py-4 px-4 font-medium">Personal Account Manager</td>
                      <td className="py-4 px-4 text-center"><X className="h-5 w-5 text-red-400 mx-auto" /></td>
                      <td className="py-4 px-4 text-center"><X className="h-5 w-5 text-red-400 mx-auto" /></td>
                      <td className="py-4 px-4 text-center"><CheckCircle className="h-5 w-5 text-green-400 mx-auto" /></td>
                      <td className="py-4 px-4 text-center"><CheckCircle className="h-5 w-5 text-green-400 mx-auto" /></td>
                    </tr>
                    <tr>
                      <td className="py-6 px-4"></td>
                      <td className="py-6 px-4 text-center">
                        <Link href="/auth/register?challenge=starter">
                          <button className="professional-button w-full py-2">
                            Start Challenge
                          </button>
                        </Link>
                      </td>
                      <td className="py-6 px-4 text-center">
                        <Link href="/auth/register?challenge=professional">
                          <button className="professional-button-dark w-full py-2">
                            Start Challenge
                          </button>
                        </Link>
                      </td>
                      <td className="py-6 px-4 text-center">
                        <Link href="/auth/register?challenge=elite">
                          <button className="professional-button w-full py-2">
                            Start Challenge
                          </button>
                        </Link>
                      </td>
                      <td className="py-6 px-4 text-center">
                        <Link href="/auth/register?challenge=institutional">
                          <button className="professional-button w-full py-2">
                            Start Challenge
                          </button>
                        </Link>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        {/* Value Proposition */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Why Our Pricing Makes Sense</h2>
            <p className="text-white/70">Understanding the value behind our challenge fees</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="glass-card p-6 text-center">
              <DollarSign className="h-12 w-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-3">One-Time Payment</h3>
              <p className="text-white/70 text-sm">
                Pay once and gain access to your challenge. No monthly fees, no hidden costs, no ongoing charges.
              </p>
            </div>

            <div className="glass-card p-6 text-center">
              <Shield className="h-12 w-12 text-blue-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-3">Risk-Free Trial</h3>
              <p className="text-white/70 text-sm">
                30-day money-back guarantee if you're not satisfied. Plus one free retake on your first challenge.
              </p>
            </div>

            <div className="glass-card p-6 text-center">
              <Star className="h-12 w-12 text-purple-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-3">Lifetime Value</h3>
              <p className="text-white/70 text-sm">
                Once funded, keep your account forever. Earn bi-weekly payouts with no additional fees or charges.
              </p>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Pricing Questions</h2>
            <p className="text-white/70">Common questions about our pricing structure</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-white mb-3">Are there any hidden fees?</h3>
                <p className="text-white/70 text-sm">
                  Absolutely not. The challenge fee is the only cost you'll ever pay. Once you're funded, 
                  there are no monthly fees, platform fees, or any other charges.
                </p>
              </div>

              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-white mb-3">What if I fail the challenge?</h3>
                <p className="text-white/70 text-sm">
                  You get one free retake on your first attempt. Additional challenges can be purchased 
                  at a 20% discount. We also offer a 30-day money-back guarantee.
                </p>
              </div>

              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-white mb-3">How do payouts work?</h3>
                <p className="text-white/70 text-sm">
                  Once funded, you can request profit withdrawals bi-weekly. There are no fees for withdrawals, 
                  and you keep your specified profit share percentage forever.
                </p>
              </div>
            </div>

            <div className="space-y-6">
              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-white mb-3">Can I upgrade my package later?</h3>
                <p className="text-white/70 text-sm">
                  Yes! Successful traders can scale up to larger accounts. We offer account scaling 
                  opportunities based on consistent performance and profitability.
                </p>
              </div>

              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-white mb-3">Is there a refund policy?</h3>
                <p className="text-white/70 text-sm">
                  We offer a 30-day money-back guarantee for first-time challengers. If you're not 
                  satisfied with our platform, request a full refund within 30 days.
                </p>
              </div>

              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-white mb-3">What payment methods do you accept?</h3>
                <p className="text-white/70 text-sm">
                  We accept all major credit cards, bank transfers, and popular cryptocurrencies. 
                  All payments are processed securely through Stripe.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="text-center">
          <div className="glass-card p-12">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Start Trading?</h2>
            <p className="text-xl text-white/70 mb-8 max-w-2xl mx-auto">
              Choose your challenge package and begin your journey to funded trading. 
              With our transparent pricing and money-back guarantee, you have nothing to lose.
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-8">
              <Link href="/auth/register">
                <button className="professional-button-dark flex items-center space-x-2 px-8 py-4 text-lg">
                  <span>Start Your Challenge</span>
                  <ArrowRight className="h-5 w-5" />
                </button>
              </Link>
              <Link href="/challenges">
                <button className="professional-button px-8 py-4 text-lg">
                  Compare Packages
                </button>
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center text-sm">
              <div className="flex items-center justify-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="text-white/70">30-day money-back guarantee</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="text-white/70">No hidden fees or charges</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="text-white/70">Free retake on first attempt</span>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
} 
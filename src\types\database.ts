// Database Types - Generated from Supabase Schema
export interface Database {
  public: {
    Tables: {
      users: {
        Row: User
        Insert: UserInsert
        Update: UserUpdate
      }
      challenges: {
        Row: Challenge
        Insert: ChallengeInsert
        Update: ChallengeUpdate
      }
      trading_accounts: {
        Row: TradingAccount
        Insert: TradingAccountInsert
        Update: TradingAccountUpdate
      }
      trades: {
        Row: Trade
        Insert: TradeInsert
        Update: TradeUpdate
      }
      payments: {
        Row: Payment
        Insert: PaymentInsert
        Update: PaymentUpdate
      }
      risk_rules: {
        Row: RiskRule
        Insert: RiskRuleInsert
        Update: RiskRuleUpdate
      }
      notifications: {
        Row: Notification
        Insert: NotificationInsert
        Update: NotificationUpdate
      }
      audit_logs: {
        Row: AuditLog
        Insert: AuditLogInsert
        Update: AuditLogUpdate
      }
      market_data: {
        Row: MarketData
        Insert: MarketDataInsert
        Update: MarketDataUpdate
      }
      trading_sessions: {
        Row: TradingSession
        Insert: TradingSessionInsert
        Update: TradingSessionUpdate
      }
      performance_metrics: {
        Row: PerformanceMetrics
        Insert: PerformanceMetricsInsert
        Update: PerformanceMetricsUpdate
      }
    }
  }
}

// User Types
export interface User {
  id: string
  email: string
  first_name: string
  last_name: string
  phone?: string
  country: string
  date_of_birth: string
  kyc_status: 'pending' | 'verified' | 'rejected' | 'expired'
  account_status: 'active' | 'suspended' | 'closed' | 'pending'
  preferred_language: string
  timezone: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

export interface UserInsert {
  id: string
  email: string
  first_name: string
  last_name: string
  phone?: string
  country: string
  date_of_birth: string
  kyc_status?: 'pending' | 'verified' | 'rejected' | 'expired'
  account_status?: 'active' | 'suspended' | 'closed' | 'pending'
  preferred_language?: string
  timezone?: string
  avatar_url?: string
}

export interface UserUpdate {
  email?: string
  first_name?: string
  last_name?: string
  phone?: string
  country?: string
  date_of_birth?: string
  kyc_status?: 'pending' | 'verified' | 'rejected' | 'expired'
  account_status?: 'active' | 'suspended' | 'closed' | 'pending'
  preferred_language?: string
  timezone?: string
  avatar_url?: string
}

// Challenge Types
export interface Challenge {
  id: string
  name: string
  description: string
  price: number
  target_profit: number
  max_daily_loss: number
  max_total_loss: number
  trading_period: number
  profit_share: number
  min_trading_days: number
  max_position_size: number
  allowed_instruments: string[]
  rules: Record<string, any>
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface ChallengeInsert {
  name: string
  description: string
  price: number
  target_profit: number
  max_daily_loss: number
  max_total_loss: number
  trading_period: number
  profit_share: number
  min_trading_days: number
  max_position_size: number
  allowed_instruments?: string[]
  rules?: Record<string, any>
  is_active?: boolean
}

export interface ChallengeUpdate {
  name?: string
  description?: string
  price?: number
  target_profit?: number
  max_daily_loss?: number
  max_total_loss?: number
  trading_period?: number
  profit_share?: number
  min_trading_days?: number
  max_position_size?: number
  allowed_instruments?: string[]
  rules?: Record<string, any>
  is_active?: boolean
}

// Trading Account Types
export interface TradingAccount {
  id: string
  user_id: string
  challenge_id: string
  account_number: string
  balance: number
  equity: number
  free_margin: number
  margin_level: number
  drawdown: number
  max_drawdown: number
  daily_loss: number
  total_loss: number
  profit: number
  phase: 'evaluation' | 'verification' | 'funded' | 'failed'
  status: 'active' | 'paused' | 'closed' | 'violation'
  mt5_login?: string
  mt5_password?: string
  mt5_server?: string
  start_date?: string
  end_date?: string
  last_trade_date?: string
  created_at: string
  updated_at: string
}

export interface TradingAccountInsert {
  user_id: string
  challenge_id: string
  account_number: string
  balance?: number
  equity?: number
  free_margin?: number
  margin_level?: number
  drawdown?: number
  max_drawdown?: number
  daily_loss?: number
  total_loss?: number
  profit?: number
  phase?: 'evaluation' | 'verification' | 'funded' | 'failed'
  status?: 'active' | 'paused' | 'closed' | 'violation'
  mt5_login?: string
  mt5_password?: string
  mt5_server?: string
  start_date?: string
  end_date?: string
}

export interface TradingAccountUpdate {
  balance?: number
  equity?: number
  free_margin?: number
  margin_level?: number
  drawdown?: number
  max_drawdown?: number
  daily_loss?: number
  total_loss?: number
  profit?: number
  phase?: 'evaluation' | 'verification' | 'funded' | 'failed'
  status?: 'active' | 'paused' | 'closed' | 'violation'
  mt5_login?: string
  mt5_password?: string
  mt5_server?: string
  end_date?: string
  last_trade_date?: string
}

// Trade Types
export interface Trade {
  id: string
  account_id: string
  symbol: string
  type: 'buy' | 'sell' | 'buy_limit' | 'sell_limit' | 'buy_stop' | 'sell_stop'
  volume: number
  open_price: number
  close_price?: number
  stop_loss?: number
  take_profit?: number
  profit?: number
  commission: number
  swap: number
  open_time: string
  close_time?: string
  status: 'open' | 'closed' | 'pending' | 'cancelled'
  comment?: string
  magic_number?: number
  mt5_ticket?: number
  created_at: string
  updated_at: string
}

export interface TradeInsert {
  account_id: string
  symbol: string
  type: 'buy' | 'sell' | 'buy_limit' | 'sell_limit' | 'buy_stop' | 'sell_stop'
  volume: number
  open_price: number
  close_price?: number
  stop_loss?: number
  take_profit?: number
  profit?: number
  commission?: number
  swap?: number
  open_time: string
  close_time?: string
  status?: 'open' | 'closed' | 'pending' | 'cancelled'
  comment?: string
  magic_number?: number
  mt5_ticket?: number
}

export interface TradeUpdate {
  close_price?: number
  stop_loss?: number
  take_profit?: number
  profit?: number
  commission?: number
  swap?: number
  close_time?: string
  status?: 'open' | 'closed' | 'pending' | 'cancelled'
  comment?: string
}

// Payment Types
export interface Payment {
  id: string
  user_id: string
  challenge_id?: string
  account_id?: string
  amount: number
  currency: string
  status: 'pending' | 'succeeded' | 'failed' | 'cancelled' | 'refunded'
  payment_method?: 'card' | 'bank_transfer' | 'crypto' | 'paypal'
  stripe_payment_intent_id?: string
  stripe_charge_id?: string
  description: string
  metadata: Record<string, any>
  processed_at?: string
  created_at: string
  updated_at: string
}

export interface PaymentInsert {
  user_id: string
  challenge_id?: string
  account_id?: string
  amount: number
  currency?: string
  status?: 'pending' | 'succeeded' | 'failed' | 'cancelled' | 'refunded'
  payment_method?: 'card' | 'bank_transfer' | 'crypto' | 'paypal'
  stripe_payment_intent_id?: string
  stripe_charge_id?: string
  description: string
  metadata?: Record<string, any>
}

export interface PaymentUpdate {
  status?: 'pending' | 'succeeded' | 'failed' | 'cancelled' | 'refunded'
  stripe_payment_intent_id?: string
  stripe_charge_id?: string
  metadata?: Record<string, any>
  processed_at?: string
}

// Risk Rule Types
export interface RiskRule {
  id: string
  name: string
  description: string
  type: 'max_daily_loss' | 'max_total_loss' | 'max_position_size' | 'max_positions' | 'trading_hours' | 'forbidden_instruments'
  value: number
  enabled: boolean
  priority: number
  challenge_id?: string
  account_id?: string
  created_at: string
  updated_at: string
}

export interface RiskRuleInsert {
  name: string
  description: string
  type: 'max_daily_loss' | 'max_total_loss' | 'max_position_size' | 'max_positions' | 'trading_hours' | 'forbidden_instruments'
  value: number
  enabled?: boolean
  priority?: number
  challenge_id?: string
  account_id?: string
}

export interface RiskRuleUpdate {
  name?: string
  description?: string
  value?: number
  enabled?: boolean
  priority?: number
}

// Notification Types
export interface Notification {
  id: string
  user_id: string
  title: string
  message: string
  type: 'info' | 'warning' | 'error' | 'success' | 'trade' | 'payment'
  is_read: boolean
  metadata: Record<string, any>
  created_at: string
}

export interface NotificationInsert {
  user_id: string
  title: string
  message: string
  type?: 'info' | 'warning' | 'error' | 'success' | 'trade' | 'payment'
  is_read?: boolean
  metadata?: Record<string, any>
}

export interface NotificationUpdate {
  is_read?: boolean
}

// Audit Log Types
export interface AuditLog {
  id: string
  user_id?: string
  action: string
  resource_type: string
  resource_id?: string
  old_values?: Record<string, any>
  new_values?: Record<string, any>
  ip_address?: string
  user_agent?: string
  created_at: string
}

export interface AuditLogInsert {
  user_id?: string
  action: string
  resource_type: string
  resource_id?: string
  old_values?: Record<string, any>
  new_values?: Record<string, any>
  ip_address?: string
  user_agent?: string
}

export interface AuditLogUpdate {
  // Audit logs are typically immutable
}

// Market Data Types
export interface MarketData {
  id: string
  symbol: string
  bid: number
  ask: number
  last: number
  volume: number
  high: number
  low: number
  change_value: number
  change_percent: number
  timestamp: string
  created_at: string
}

export interface MarketDataInsert {
  symbol: string
  bid: number
  ask: number
  last: number
  volume?: number
  high: number
  low: number
  change_value?: number
  change_percent?: number
  timestamp?: string
}

export interface MarketDataUpdate {
  bid?: number
  ask?: number
  last?: number
  volume?: number
  high?: number
  low?: number
  change_value?: number
  change_percent?: number
  timestamp?: string
}

// Trading Session Types
export interface TradingSession {
  id: string
  account_id: string
  start_time: string
  end_time?: string
  start_balance: number
  end_balance?: number
  profit?: number
  trades_count: number
  status: 'active' | 'completed'
  created_at: string
  updated_at: string
}

export interface TradingSessionInsert {
  account_id: string
  start_time?: string
  start_balance: number
  end_balance?: number
  profit?: number
  trades_count?: number
  status?: 'active' | 'completed'
}

export interface TradingSessionUpdate {
  end_time?: string
  end_balance?: number
  profit?: number
  trades_count?: number
  status?: 'active' | 'completed'
}

// Performance Metrics Types
export interface PerformanceMetrics {
  id: string
  account_id: string
  period_start: string
  period_end: string
  total_trades: number
  winning_trades: number
  losing_trades: number
  win_rate: number
  avg_win: number
  avg_loss: number
  profit_factor: number
  sharpe_ratio: number
  max_drawdown: number
  return_on_investment: number
  created_at: string
  updated_at: string
}

export interface PerformanceMetricsInsert {
  account_id: string
  period_start: string
  period_end: string
  total_trades?: number
  winning_trades?: number
  losing_trades?: number
  win_rate?: number
  avg_win?: number
  avg_loss?: number
  profit_factor?: number
  sharpe_ratio?: number
  max_drawdown?: number
  return_on_investment?: number
}

export interface PerformanceMetricsUpdate {
  period_end?: string
  total_trades?: number
  winning_trades?: number
  losing_trades?: number
  win_rate?: number
  avg_win?: number
  avg_loss?: number
  profit_factor?: number
  sharpe_ratio?: number
  max_drawdown?: number
  return_on_investment?: number
}

// Helper types for relationships
export interface UserWithAccounts extends User {
  trading_accounts: TradingAccount[]
}

export interface TradingAccountWithChallenge extends TradingAccount {
  challenge: Challenge
}

export interface TradingAccountWithTrades extends TradingAccount {
  trades: Trade[]
}

export interface TradeWithAccount extends Trade {
  trading_account: TradingAccount
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T = any> {
  data: T[]
  count: number
  page: number
  per_page: number
  total_pages: number
} 
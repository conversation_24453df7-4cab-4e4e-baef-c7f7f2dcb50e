import type { Metadata } from 'next'
import { Inter, JetBrains_Mono } from 'next/font/google'
import './globals.css'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
})

const jetbrainsMono = JetBrains_Mono({ 
  subsets: ['latin'],
  variable: '--font-mono',
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'Prop Bully - Elite Trading Platform',
  description: 'Professional proprietary trading firm platform with MetaTrader 5 integration, real-time analytics, and institutional-grade infrastructure. Join the elite traders at Prop Bully.',
  keywords: 'prop bully, prop firm, trading, forex, MT5, funded trader, proprietary trading',
  authors: [{ name: 'Prop Bully Team' }],
  creator: 'Prop Bully',
  publisher: 'Prop Bully',
  robots: 'index, follow',
  viewport: 'width=device-width, initial-scale=1',
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#020617' }
  ],
  openGraph: {
    type: 'website',
    title: 'Prop Bully - Elite Trading Platform',
    description: 'Professional proprietary trading firm platform with MetaTrader 5 integration.',
    siteName: 'Prop Bully',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Prop Bully - Elite Trading Platform',
    description: 'Professional proprietary trading firm platform with MetaTrader 5 integration.',
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark" suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased`}>
        <div className="relative flex min-h-screen flex-col bg-background text-foreground">
          <div className="flex-1">
            {children}
          </div>
        </div>
      </body>
    </html>
  )
} 
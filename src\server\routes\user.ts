import { Router, Response } from 'express'
import { z } from 'zod'
import { supabase } from '../../lib/supabase/server'
import { validateRequest, ValidationRequest } from '../middleware/validation'
import { asyncHand<PERSON>, createValidationError, createNotFoundError, createInternalError } from '../middleware/errorHandler'
import { AuthenticatedRequest } from '../middleware/auth'
import bcrypt from 'bcryptjs'
import { randomBytes } from 'crypto'

const router = Router()

// Validation schemas
const updateProfileSchema = z.object({
  firstName: z.string().min(1).max(50).optional(),
  lastName: z.string().min(1).max(50).optional(),
  phone: z.string().min(10).max(20).optional(),
  country: z.string().length(2).optional(), // ISO country code
  timezone: z.string().max(50).optional(),
  language: z.string().length(2).optional() // ISO language code
})

const tradingPreferencesSchema = z.object({
  defaultLotSize: z.number().min(0.01).max(100).optional(),
  maxSlippage: z.number().min(0).max(100).optional(),
  closeOnFriday: z.boolean().optional(),
  autoStopLoss: z.boolean().optional(),
  defaultStopLoss: z.number().min(0).optional(),
  defaultTakeProfit: z.number().min(0).optional(),
  riskPerTrade: z.number().min(0.1).max(10).optional(),
  tradingHours: z.object({
    start: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    end: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
  }).optional()
})

const notificationSettingsSchema = z.object({
  email: z.object({
    tradeAlerts: z.boolean().optional(),
    riskAlerts: z.boolean().optional(),
    marketNews: z.boolean().optional(),
    accountUpdates: z.boolean().optional(),
    weeklyReports: z.boolean().optional()
  }).optional(),
  push: z.object({
    tradeAlerts: z.boolean().optional(),
    riskAlerts: z.boolean().optional(),
    marketNews: z.boolean().optional(),
    accountUpdates: z.boolean().optional()
  }).optional(),
  sms: z.object({
    riskAlerts: z.boolean().optional(),
    accountUpdates: z.boolean().optional()
  }).optional()
})

const securitySettingsSchema = z.object({
  loginAlerts: z.boolean().optional(),
  sessionTimeout: z.number().min(15).max(480).optional(), // 15 minutes to 8 hours
  apiAccess: z.boolean().optional(),
  ipWhitelist: z.array(z.string().ip()).optional()
})

const changePasswordSchema = z.object({
  currentPassword: z.string().min(6),
  newPassword: z.string().min(8).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  confirmPassword: z.string()
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

const enable2FASchema = z.object({
  phone: z.string().min(10).max(20).optional(),
  method: z.enum(['sms', 'app'])
})

const generateApiKeySchema = z.object({
  name: z.string().min(1).max(100),
  permissions: z.array(z.enum(['read', 'trade', 'manage'])).min(1)
})

// GET /api/users/profile
router.get('/profile',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id

    try {
      const { data: user, error } = await supabase
        .from('users')
        .select(`
          id,
          email,
          first_name,
          last_name,
          phone,
          country,
          timezone,
          language,
          date_of_birth,
          kyc_status,
          account_status,
          created_at,
          updated_at
        `)
        .eq('id', userId)
        .single()

      if (error || !user) {
        throw createNotFoundError('User profile not found')
      }

      res.json({
        success: true,
        data: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          phone: user.phone,
          country: user.country,
          timezone: user.timezone,
          language: user.language,
          dateOfBirth: user.date_of_birth,
          kycStatus: user.kyc_status,
          accountStatus: user.account_status,
          createdAt: user.created_at,
          updatedAt: user.updated_at
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// PUT /api/users/profile
router.put('/profile',
  validateRequest({ body: updateProfileSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id
    const updateData = req.validatedBody

    try {
      const { data: updatedUser, error } = await supabase
        .from('users')
        .update({
          first_name: updateData.firstName,
          last_name: updateData.lastName,
          phone: updateData.phone,
          country: updateData.country,
          timezone: updateData.timezone,
          language: updateData.language,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select(`
          id,
          email,
          first_name,
          last_name,
          phone,
          country,
          timezone,
          language,
          updated_at
        `)
        .single()

      if (error) {
        throw createInternalError('Failed to update profile')
      }

      res.json({
        success: true,
        data: {
          id: updatedUser.id,
          email: updatedUser.email,
          firstName: updatedUser.first_name,
          lastName: updatedUser.last_name,
          phone: updatedUser.phone,
          country: updatedUser.country,
          timezone: updatedUser.timezone,
          language: updatedUser.language,
          updatedAt: updatedUser.updated_at
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/users/trading-preferences
router.get('/trading-preferences',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id

    try {
      const { data: preferences, error } = await supabase
        .from('user_preferences')
        .select('trading_preferences')
        .eq('user_id', userId)
        .single()

      if (error && error.code !== 'PGRST116') { // Not found is OK
        throw createInternalError('Failed to fetch trading preferences')
      }

      // Default preferences if none exist
      const defaultPreferences = {
        defaultLotSize: 0.1,
        maxSlippage: 3,
        closeOnFriday: false,
        autoStopLoss: false,
        defaultStopLoss: 50,
        defaultTakeProfit: 50,
        riskPerTrade: 2,
        tradingHours: {
          start: "08:00",
          end: "18:00"
        },
        allowedInstruments: ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD"]
      }

      const tradingPrefs = preferences?.trading_preferences || defaultPreferences

      res.json({
        success: true,
        data: tradingPrefs
      })

    } catch (error) {
      throw error
    }
  })
)

// PUT /api/users/trading-preferences
router.put('/trading-preferences',
  validateRequest({ body: tradingPreferencesSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id
    const updateData = req.validatedBody

    try {
      // First get existing preferences
      const { data: existing } = await supabase
        .from('user_preferences')
        .select('trading_preferences')
        .eq('user_id', userId)
        .single()

      const currentPreferences = existing?.trading_preferences || {}
      const updatedPreferences = { ...currentPreferences, ...updateData }

      // Upsert the preferences
      const { data: updated, error: upsertError } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: userId,
          trading_preferences: updatedPreferences,
          updated_at: new Date().toISOString()
        })
        .select('trading_preferences')
        .single()

      if (upsertError) {
        throw createInternalError('Failed to update trading preferences')
      }

      res.json({
        success: true,
        data: updated.trading_preferences
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/users/notification-settings
router.get('/notification-settings',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id

    try {
      const { data: preferences, error } = await supabase
        .from('user_preferences')
        .select('notification_settings')
        .eq('user_id', userId)
        .single()

      if (error && error.code !== 'PGRST116') {
        throw createInternalError('Failed to fetch notification settings')
      }

      // Default notification settings
      const defaultSettings = {
        email: {
          tradeAlerts: true,
          riskAlerts: true,
          marketNews: false,
          accountUpdates: true,
          weeklyReports: true
        },
        push: {
          tradeAlerts: true,
          riskAlerts: true,
          marketNews: false,
          accountUpdates: true
        },
        sms: {
          riskAlerts: true,
          accountUpdates: false
        }
      }

      const notificationSettings = preferences?.notification_settings || defaultSettings

      res.json({
        success: true,
        data: notificationSettings
      })

    } catch (error) {
      throw error
    }
  })
)

// PUT /api/users/notification-settings
router.put('/notification-settings',
  validateRequest({ body: notificationSettingsSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id
    const updateData = req.validatedBody

    try {
      // Get existing settings
      const { data: existing } = await supabase
        .from('user_preferences')
        .select('notification_settings')
        .eq('user_id', userId)
        .single()

      const currentSettings = existing?.notification_settings || {}
      
      // Deep merge the settings
      const updatedSettings = {
        email: { ...currentSettings.email, ...updateData.email },
        push: { ...currentSettings.push, ...updateData.push },
        sms: { ...currentSettings.sms, ...updateData.sms }
      }

      // Upsert the settings
      const { data: updated, error: upsertError } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: userId,
          notification_settings: updatedSettings,
          updated_at: new Date().toISOString()
        })
        .select('notification_settings')
        .single()

      if (upsertError) {
        throw createInternalError('Failed to update notification settings')
      }

      res.json({
        success: true,
        data: updated.notification_settings
      })

    } catch (error) {
      throw error
    }
  })
)

// GET /api/users/security-settings
router.get('/security-settings',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id

    try {
      // Get security preferences
      const { data: preferences } = await supabase
        .from('user_preferences')
        .select('security_settings')
        .eq('user_id', userId)
        .single()

      // Get user auth data
      const { data: userData } = await supabase
        .from('users')
        .select('last_password_change, two_factor_enabled')
        .eq('id', userId)
        .single()

      // Get active sessions (simplified - in real implementation would track sessions)
      const activeSessions = [
        {
          id: '1',
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0...',
          location: 'New York, US',
          lastActivity: new Date().toISOString(),
          current: true
        }
      ]

      const defaultSecuritySettings = {
        loginAlerts: true,
        sessionTimeout: 120, // 2 hours
        apiAccess: false,
        ipWhitelist: []
      }

      const securitySettings = preferences?.security_settings || defaultSecuritySettings

      res.json({
        success: true,
        data: {
          twoFactorEnabled: userData?.two_factor_enabled || false,
          loginAlerts: securitySettings.loginAlerts,
          sessionTimeout: securitySettings.sessionTimeout,
          apiAccess: securitySettings.apiAccess,
          ipWhitelist: securitySettings.ipWhitelist,
          lastPasswordChange: userData?.last_password_change || null,
          activeSessions
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// PUT /api/users/security-settings
router.put('/security-settings',
  validateRequest({ body: securitySettingsSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id
    const updateData = req.validatedBody

    try {
      // Get existing settings
      const { data: existing } = await supabase
        .from('user_preferences')
        .select('security_settings')
        .eq('user_id', userId)
        .single()

      const currentSettings = existing?.security_settings || {}
      const updatedSettings = { ...currentSettings, ...updateData }

      // Upsert the settings
      const { data: updated, error: upsertError } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: userId,
          security_settings: updatedSettings,
          updated_at: new Date().toISOString()
        })
        .select('security_settings')
        .single()

      if (upsertError) {
        throw createInternalError('Failed to update security settings')
      }

      res.json({
        success: true,
        data: updated.security_settings
      })

    } catch (error) {
      throw error
    }
  })
)

// POST /api/users/password/change
router.post('/password/change',
  validateRequest({ body: changePasswordSchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id
    const { currentPassword, newPassword } = req.validatedBody

    try {
      // Get user's current password hash
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('password_hash')
        .eq('id', userId)
        .single()

      if (userError || !userData) {
        throw createNotFoundError('User not found')
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, userData.password_hash)
      if (!isCurrentPasswordValid) {
        throw createValidationError('Current password is incorrect')
      }

      // Hash new password
      const saltRounds = 12
      const newPasswordHash = await bcrypt.hash(newPassword, saltRounds)

      // Update password
      const { error: updateError } = await supabase
        .from('users')
        .update({
          password_hash: newPasswordHash,
          last_password_change: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (updateError) {
        throw createInternalError('Failed to update password')
      }

      // Create audit log
      await supabase
        .from('audit_logs')
        .insert({
          user_id: userId,
          action: 'password_change',
          details: {
            timestamp: new Date().toISOString(),
            ip_address: req.ip
          }
        })

      res.json({
        success: true,
        data: { message: 'Password changed successfully' }
      })

    } catch (error) {
      throw error
    }
  })
)

// POST /api/users/2fa/enable
router.post('/2fa/enable',
  validateRequest({ body: enable2FASchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id
    const { phone, method } = req.validatedBody

    try {
      // Generate 2FA secret/backup codes
      const backupCodes = Array.from({ length: 8 }, () => 
        randomBytes(4).toString('hex').toUpperCase()
      )

      // Update user 2FA settings
      const { error: updateError } = await supabase
        .from('users')
        .update({
          two_factor_enabled: true,
          two_factor_method: method,
          two_factor_phone: phone,
          two_factor_backup_codes: backupCodes,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (updateError) {
        throw createInternalError('Failed to enable 2FA')
      }

      // Create audit log
      await supabase
        .from('audit_logs')
        .insert({
          user_id: userId,
          action: '2fa_enabled',
          details: {
            method,
            timestamp: new Date().toISOString()
          }
        })

      res.json({
        success: true,
        data: {
          message: '2FA enabled successfully',
          backupCodes,
          method
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// POST /api/users/api-key/generate
router.post('/api-key/generate',
  validateRequest({ body: generateApiKeySchema }),
  asyncHandler(async (req: ValidationRequest & AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id
    const { name, permissions } = req.validatedBody

    try {
      // Generate API key and secret
      const apiKey = `pk_${randomBytes(16).toString('hex')}`
      const secret = randomBytes(32).toString('hex')
      const expiresAt = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year

      // Store API key
      const { error } = await supabase
        .from('api_keys')
        .insert({
          user_id: userId,
          name,
          key: apiKey,
          secret_hash: await bcrypt.hash(secret, 10),
          permissions,
          expires_at: expiresAt.toISOString()
        })
        .select('id, name, key, permissions, expires_at')
        .single()

      if (error) {
        throw createInternalError('Failed to generate API key')
      }

      // Create audit log
      await supabase
        .from('audit_logs')
        .insert({
          user_id: userId,
          action: 'api_key_generated',
          details: {
            key_name: name,
            permissions,
            timestamp: new Date().toISOString()
          }
        })

      res.json({
        success: true,
        data: {
          api_key: apiKey,
          secret, // Only returned once
          expires_at: expiresAt.toISOString(),
          name,
          permissions
        }
      })

    } catch (error) {
      throw error
    }
  })
)

// POST /api/users/data/export
router.post('/data/export',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id

    try {
      // Get all user data
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      const { data: accounts, error: accountsError } = await supabase
        .from('trading_accounts')
        .select('*')
        .eq('user_id', userId)

      const { data: trades, error: tradesError } = await supabase
        .from('trades')
        .select('*')
        .in('account_id', accounts?.map(a => a.id) || [])

      const { data: preferences } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId)

      if (userError || accountsError || tradesError) {
        throw createInternalError('Failed to export user data')
      }

      const exportData = {
        user: userData,
        trading_accounts: accounts || [],
        trades: trades || [],
        preferences: preferences || [],
        exported_at: new Date().toISOString()
      }

      // Create audit log
      await supabase
        .from('audit_logs')
        .insert({
          user_id: userId,
          action: 'data_export',
          details: {
            timestamp: new Date().toISOString(),
            records_count: {
              accounts: accounts?.length || 0,
              trades: trades?.length || 0
            }
          }
        })

      res.json({
        success: true,
        data: exportData
      })

    } catch (error) {
      throw error
    }
  })
)

export default router 